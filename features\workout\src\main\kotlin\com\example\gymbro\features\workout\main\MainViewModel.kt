package com.example.gymbro.features.workout.main

import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.main.internal.effect.HomeEffectHandler
import com.example.gymbro.features.workout.main.internal.reducer.HomeReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Home模块ViewModel - MVI 2.0 标准实现
 *
 * 核心特性：
 * 1. 继承BaseMviViewModel，遵循MVI 2.0架构标准
 * 2. 使用HomeReducer处理状态变更和Effect生成
 * 3. 自动处理Intent → Reducer → State → Effect流程
 * 4. 支持结构化并发和生命周期管理
 * 5. 提供便捷API用于UI层调用
 *
 * 符合项目要求：BaseMviViewModel替代废弃的MviViewModel
 */
@HiltViewModel
internal class HomeViewModel
@Inject
constructor(
    private val homeReducer: HomeReducer,
    private val effectHandler: HomeEffectHandler,
    private val logger: Logger,
) : BaseMviViewModel<HomeContract.Intent, HomeContract.State, HomeContract.Effect>(
    initialState = HomeContract.State(),
) {

    // === MVI 2.0 核心组件 ===

    // 提供Reducer实例给BaseMviViewModel
    override val reducer: Reducer<HomeContract.Intent, HomeContract.State, HomeContract.Effect> = homeReducer

    // === 初始化 ===

    init {
        logger.d("HomeViewModel", "HomeViewModel初始化，开始加载训练数据")

        // 🔥 在init块中初始化EffectHandler - 确保所有依赖都已注入
        initializeEffectHandler()

        dispatch(HomeContract.Intent.LoadInitialData)
    }

    /**
     * 初始化EffectHandler - 处理副作用
     * 🔥 修复：完善 Effect 处理流程，符合 MVI 2.0 标准
     */
    override fun initializeEffectHandler() {
        logger.d("HomeViewModel", "初始化 EffectHandler")

        // 初始化 EffectHandler
        effectHandler.initialize(
            scope = handlerScope,
            intentSender = this::dispatch,
        )

        // 🔥 关键修复：监听 Effect 流并处理
        handlerScope.launch {
            effect.collect { effect ->
                logger.d("HomeViewModel", "收到 Effect: ${effect::class.simpleName}")
                WorkoutLogUtils.Template.debug("📨 收到 Effect: ${effect::class.simpleName}，准备传递给 EffectHandler")
                effectHandler.handle(effect)
                WorkoutLogUtils.Template.debug("📤 Effect 已传递给 EffectHandler")
            }
        }

        logger.d("HomeViewModel", "EffectHandler 初始化完成")
    }

    // === 公共API ===
    // dispatch 方法由 BaseMviViewModel 提供，无需重复实现

    // === 便捷方法（为UI层提供简化的API） ===

    /**
     * 加载初始数据
     */
    fun loadInitialData() {
        dispatch(HomeContract.Intent.LoadInitialData)
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        dispatch(HomeContract.Intent.RefreshData)
    }

    /**
     * 选择日期
     */
    fun selectDate(date: kotlinx.datetime.LocalDate) {
        dispatch(HomeContract.Intent.SelectDate(date))
    }

    /**
     * 月份切换
     */
    fun changeMonth(delta: Int) {
        dispatch(HomeContract.Intent.ChangeMonth(delta))
    }

    /**
     * 开始快速训练
     */
    fun startQuickWorkout() {
        dispatch(HomeContract.Intent.StartQuickWorkout)
    }

    /**
     * 使用模板开始训练
     */
    fun startWithTemplate(templateId: String) {
        dispatch(HomeContract.Intent.StartWithTemplate(templateId))
    }

    /**
     * 完成训练
     */
    fun completeWorkout() {
        dispatch(HomeContract.Intent.CompleteWorkout)
    }

    /**
     * 保存为模板
     */
    fun saveAsTemplate(templateName: String) {
        dispatch(HomeContract.Intent.SaveAsTemplate(templateName))
    }

    /**
     * 导航到模板页面
     */
    fun navigateToTemplates() {
        dispatch(HomeContract.Intent.NavigateToTemplates)
    }

    /**
     * 导航到统计页面
     */
    fun navigateToStats() {
        dispatch(HomeContract.Intent.NavigateToStats)
    }

    /**
     * 导航到计划页面
     */
    fun navigateToPlans() {
        dispatch(HomeContract.Intent.NavigateToPlans)
    }

    /**
     * 导航到动作库页面
     */
    fun navigateToExerciseLibrary() {
        dispatch(HomeContract.Intent.NavigateToExerciseLibrary)
    }

    /**
     * 显示更多选项
     */
    fun showMoreOptions() {
        dispatch(HomeContract.Intent.ShowMoreOptions)
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        dispatch(HomeContract.Intent.ClearError)
    }

    // === 内部方法 ===
    // emitEffect 方法由 BaseMviViewModel 提供，无需重复实现

    // === 调试和监控 ===

    fun getStateSummary(): String {
        val currentState = state.value
        return buildString {
            append("HomeState(")
            append("loading=${currentState.isLoading}, ")
            append("hasUser=${currentState.user != null}, ")
            append("selectedDate=${currentState.selectedDate}, ")
            append("templatesCount=${currentState.userTemplates.size}, ")
            append("hasError=${currentState.errorCode != null}, ")
            append("activeSession=${currentState.activeSessionId}")
            append(")")
        }
    }

    override fun onCleared() {
        super.onCleared()
        logger.d("HomeViewModel", "ViewModel清理完成")
    }
}

// === 扩展函数 ===

// === 扩展函数 ===

/**
 * 检查是否可以开始训练
 */
val HomeContract.State.canStartWorkout: Boolean
    get() = !isLoading && user != null && activeSessionId == null

/**
 * 检查是否有数据
 */
val HomeContract.State.hasData: Boolean
    get() = user != null

/**
 * 检查是否处于错误状态
 */
val HomeContract.State.hasError: Boolean
    get() = errorCode != null
