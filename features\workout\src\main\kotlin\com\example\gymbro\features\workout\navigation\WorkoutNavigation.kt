package com.example.gymbro.features.workout.navigation

import androidx.navigation.*
import androidx.navigation.compose.composable
import androidx.navigation.compose.navigation
import com.example.gymbro.designSystem.components.animations.GymBroPageTransitions
import com.example.gymbro.features.exerciselibrary.api.ExerciseLibraryNavigatable
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.main.HomeScreen
import com.example.gymbro.features.workout.plan.PlanScreen
import com.example.gymbro.features.workout.plan.edit.PlanEditScreen
import com.example.gymbro.features.workout.session.SessionScreen
import com.example.gymbro.features.workout.session.SessionTemplateLoadingScreen
import com.example.gymbro.features.workout.stats.StatsScreen
import com.example.gymbro.features.workout.template.TemplateScreen
import com.example.gymbro.features.workout.template.edit.TemplateEditScreen
import timber.log.Timber

/**
 * 全局动作选择结果持有者 - 解决SavedStateHandle实例不匹配问题
 */
object ExerciseSelectionHolder {
    private val selectedExercises = mutableListOf<com.example.gymbro.domain.exercise.model.Exercise>()

    fun addSelectedExercises(exercises: List<com.example.gymbro.domain.exercise.model.Exercise>) {
        selectedExercises.addAll(exercises)
        Timber.d(
            "🔧 ExerciseSelectionHolder: Added ${exercises.size} exercises, total: ${selectedExercises.size}",
        )
    }

    fun getSelectedExercises(): List<com.example.gymbro.domain.exercise.model.Exercise>? {
        if (selectedExercises.isEmpty()) return null
        val result = selectedExercises.toList()
        selectedExercises.clear() // 清除数据避免重复使用
        Timber.d("🔧 ExerciseSelectionHolder: Retrieved ${result.size} exercises, cleared holder")
        return result
    }

    fun hasSelectedExercises(): Boolean = selectedExercises.isNotEmpty()

    fun clear() {
        selectedExercises.clear()
        Timber.d("🔧 ExerciseSelectionHolder: Cleared all exercises")
    }
}

/**
 * 导航到训练模块的扩展函数
 */
fun NavController.navigateToWorkoutGraph(navOptions: NavOptions? = null) {
    this.navigate(WorkoutRoutes.TRAINING_GRAPH, navOptions)
}

/**
 * 训练模块导航图构建器
 */
fun NavGraphBuilder.workoutGraph(
    navController: NavHostController,
    exerciseLibraryNavigatable: ExerciseLibraryNavigatable,
) {
    navigation(
        startDestination = WorkoutRoutes.WORKOUT_MAIN,
        route = WorkoutRoutes.TRAINING_GRAPH,
    ) {
        // 训练主屏幕
        composable(
            route = WorkoutRoutes.WORKOUT_MAIN,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            HomeScreen(
                onNavigateToSession = { sessionId ->
                    if (sessionId.isEmpty()) {
                        // 空sessionId表示新训练，导航到NEW_SESSION显示选择器
                        navController.navigate(WorkoutRoutes.NEW_SESSION)
                    } else {
                        // 有sessionId表示恢复现有训练
                        navController.navigate(
                            WorkoutRoutes.ACTIVE_SESSION.replace(
                                "{${WorkoutRoutes.SESSION_ID_ARG}}",
                                sessionId,
                            ),
                        )
                    }
                },
                onNavigateToTemplates = {
                    navController.navigate(WorkoutRoutes.TEMPLATE_LIST)
                },
                onNavigateToPlans = {
                    navController.navigate(WorkoutRoutes.PLAN_LIST)
                },
                onNavigateToStats = {
                    navController.navigate(WorkoutRoutes.WORKOUT_STATS)
                },
                onNavigateToExerciseLibrary = {
                    navController.navigate("exercise-library")
                },
                onNavigateToCalendar = {
                    navController.navigate(WorkoutRoutes.CALENDAR)
                },
                onNavigateBack = { navController.popBackStack() },
            )
        }

        // 训练统计屏幕
        composable(
            route = WorkoutRoutes.WORKOUT_STATS,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            StatsScreen(
                onNavigateBack = { navController.popBackStack() },
            )
        }

        // 训练日历屏幕
        composable(
            route = WorkoutRoutes.CALENDAR,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            com.example.gymbro.features.workout.calendar.CalendarScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToSession = { sessionId ->
                    if (sessionId.isEmpty()) {
                        navController.navigate(WorkoutRoutes.NEW_SESSION)
                    } else {
                        navController.navigate(
                            WorkoutRoutes.ACTIVE_SESSION.replace(
                                "{${WorkoutRoutes.SESSION_ID_ARG}}",
                                sessionId,
                            ),
                        )
                    }
                },
                onNavigateToTemplate = { templateId ->
                    navController.navigate(
                        WorkoutRoutes.TEMPLATE_EDIT.replace(
                            "{${WorkoutRoutes.TEMPLATE_ID_ARG}}",
                            templateId,
                        ),
                    )
                },
                onNavigateToPlan = { planId ->
                    navController.navigate(
                        WorkoutRoutes.PLAN_EDIT.replace(
                            "{${WorkoutRoutes.PLAN_ID_ARG}}",
                            planId,
                        ),
                    )
                },
            )
        }

        // 训练模板屏幕
        composable(
            route = WorkoutRoutes.TEMPLATE_LIST,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            TemplateScreen(
                onNavigateToTemplateDetail = { templateId: String ->
                    // 验证templateId不为空
                    if (templateId.isNotBlank()) {
                        navController.navigate(
                            WorkoutRoutes.TEMPLATE_EDIT.replace(
                                "{${WorkoutRoutes.TEMPLATE_ID_ARG}}",
                                templateId,
                            ),
                        )
                    }
                },
                onNavigateToNewTemplate = {
                    WorkoutLogUtils.Template.debug("🔧 onNavigateToNewTemplate 被调用")
                    navController.navigate(WorkoutRoutes.CREATE_TEMPLATE)
                    WorkoutLogUtils.Template.debug("🔧 导航到 CREATE_TEMPLATE: ${WorkoutRoutes.CREATE_TEMPLATE}")
                },
                onNavigateToEditTemplate = { templateId: String ->
                    println("🔧 [DEBUG] onNavigateToEditTemplate 被调用，templateId: $templateId")
                    // 验证templateId不为空
                    if (templateId.isNotBlank()) {
                        val route = WorkoutRoutes.TEMPLATE_EDIT.replace(
                            "{${WorkoutRoutes.TEMPLATE_ID_ARG}}",
                            templateId,
                        )
                        println("🔧 [DEBUG] 导航到路由: $route")
                        navController.navigate(route)
                    } else {
                        println("🔧 [DEBUG] templateId为空，无法导航")
                    }
                },
                onNavigateToCreateDraft = {
                    navController.navigate(WorkoutRoutes.CREATE_DRAFT)
                },
                onNavigateToDraftEditor = { draftId: String ->
                    // 验证draftId不为空
                    if (draftId.isNotBlank()) {
                        navController.navigate(
                            WorkoutRoutes.DRAFT_EDIT.replace(
                                "{${WorkoutRoutes.DRAFT_ID_ARG}}",
                                draftId,
                            ),
                        )
                    }
                },
                onNavigateToStartWorkout = { templateId: String ->
                    // 从模板开始训练
                    if (templateId.isNotBlank()) {
                        navController.navigate(
                            WorkoutRoutes.ACTIVE_SESSION.replace(
                                "{${WorkoutRoutes.SESSION_ID_ARG}}",
                                templateId,
                            ),
                        )
                    }
                },
                onNavigateBack = { navController.popBackStack() },
            )
        }

        // 创建训练模板屏幕 - 🔧 修复：直接进入编辑模式
        composable(
            route = WorkoutRoutes.CREATE_TEMPLATE,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            // 🆕 直接使用模板编辑器创建新模板
            TemplateEditScreen(
                templateId = null, // 创建新模板，不基于现有模板
                onNavigateBack = { navController.popBackStack() },
                onNavigateToExerciseLibrary = {
                    // 🔧 修复：使用动作库原生回调机制，直接保存到SavedStateHandle
                    exerciseLibraryNavigatable.navigateToPicker(
                        navController = navController,
                        multipleSelection = true,
                        preSelectedIds = emptyList(),
                        onResult = { selectedExercises ->
                            // 🔧 最终修复：使用全局单例存储，避免SavedStateHandle实例不匹配问题
                            try {
                                Timber.d(
                                    "✅ Received ${selectedExercises.size} exercises from exercise library",
                                )
                                selectedExercises.forEachIndexed { index, exercise ->
                                    Timber.d("✅ Exercise $index: id=${exercise.id}, name=${exercise.name}")
                                }

                                // 🔧 使用全局单例累积添加选择结果
                                ExerciseSelectionHolder.addSelectedExercises(selectedExercises)
                                Timber.d("✅ Added exercises to global holder for template creation")
                            } catch (e: Exception) {
                                Timber.e(e, "❌ Failed to store exercises in global holder")
                            }
                        },
                    )
                },
                onNavigateToPreview = {
                    // TODO: 实现预览功能
                },
            )
        }

        // 编辑训练模板屏幕 - 统一使用草稿编辑器
        composable(
            route = WorkoutRoutes.TEMPLATE_EDIT,
            arguments = listOf(navArgument(WorkoutRoutes.TEMPLATE_ID_ARG) { type = NavType.StringType }),
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) { backStackEntry ->
            // 从路由参数获取templateId
            val templateId = backStackEntry.arguments?.getString(WorkoutRoutes.TEMPLATE_ID_ARG) ?: ""

            // 使用统一的模板编辑器，支持编辑现有模板
            TemplateEditScreen(
                templateId = templateId.takeIf { it.isNotBlank() }, // 🆕 传递templateId
                onNavigateBack = { navController.popBackStack() },
                onNavigateToExerciseLibrary = {
                    // 🔧 修复：使用动作库原生回调机制，直接保存到SavedStateHandle
                    exerciseLibraryNavigatable.navigateToPicker(
                        navController = navController,
                        multipleSelection = true,
                        preSelectedIds = emptyList(),
                        onResult = { selectedExercises ->
                            // 🔧 最终修复：使用全局单例存储，避免SavedStateHandle实例不匹配问题
                            try {
                                Timber.d(
                                    "✅ Received ${selectedExercises.size} exercises from exercise library",
                                )
                                selectedExercises.forEachIndexed { index, exercise ->
                                    Timber.d("✅ Exercise $index: id=${exercise.id}, name=${exercise.name}")
                                }

                                // 🔧 使用全局单例累积添加选择结果
                                ExerciseSelectionHolder.addSelectedExercises(selectedExercises)
                                Timber.d("✅ Added exercises to global holder for template edit")
                            } catch (e: Exception) {
                                Timber.e(e, "❌ Failed to store exercises in global holder")
                            }
                        },
                    )
                },
                onNavigateToPreview = {
                    // TODO: 实现预览功能
                },
            )
        }

        // 创建训练模板草稿屏幕
        composable(
            route = WorkoutRoutes.CREATE_DRAFT,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            TemplateEditScreen(
                templateId = null, // 创建新模板，不基于现有模板
                onNavigateBack = { navController.popBackStack() },
                onNavigateToExerciseLibrary = {
                    // 🔧 修复：使用动作库原生回调机制，直接保存到SavedStateHandle
                    exerciseLibraryNavigatable.navigateToPicker(
                        navController = navController,
                        multipleSelection = true,
                        preSelectedIds = emptyList(),
                        onResult = { selectedExercises ->
                            // 🔧 最终修复：使用全局单例存储，避免SavedStateHandle实例不匹配问题
                            try {
                                Timber.d(
                                    "✅ Received ${selectedExercises.size} exercises from exercise library",
                                )
                                selectedExercises.forEachIndexed { index, exercise ->
                                    val exerciseName =
                                        when (val name = exercise.name) {
                                            is com.example.gymbro.core.ui.text.UiText.DynamicString -> name.value
                                            else -> name.toString()
                                        }
                                    Timber.d("✅ Exercise $index: id=${exercise.id}, name=$exerciseName")
                                }

                                // 🔧 使用全局单例累积添加选择结果
                                ExerciseSelectionHolder.addSelectedExercises(selectedExercises)
                                Timber.d("✅ Added exercises to global holder for draft creation")
                            } catch (e: Exception) {
                                Timber.e(e, "❌ Failed to store exercises in global holder")
                            }
                        },
                    )
                },
                onNavigateToPreview = {
                    // TODO: 实现预览功能
                },
            )
        }

        // 编辑训练模板草稿屏幕
        composable(
            route = WorkoutRoutes.DRAFT_EDIT,
            arguments = listOf(navArgument(WorkoutRoutes.DRAFT_ID_ARG) { type = NavType.StringType }),
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) { backStackEntry ->
            // 从路由参数获取draftId
            val draftId = backStackEntry.arguments?.getString(WorkoutRoutes.DRAFT_ID_ARG) ?: ""
            TemplateEditScreen(
                templateId = draftId.takeIf { it.isNotBlank() }, // 将draftId作为templateId使用
                onNavigateBack = { navController.popBackStack() },
                onNavigateToExerciseLibrary = {
                    // 🔧 修复：使用动作库原生回调机制，直接保存到SavedStateHandle
                    exerciseLibraryNavigatable.navigateToPicker(
                        navController = navController,
                        multipleSelection = true,
                        preSelectedIds = emptyList(),
                        onResult = { selectedExercises ->
                            // 🔧 最终修复：使用全局单例存储，避免SavedStateHandle实例不匹配问题
                            try {
                                Timber.d(
                                    "✅ Received ${selectedExercises.size} exercises from exercise library",
                                )
                                selectedExercises.forEachIndexed { index, exercise ->
                                    Timber.d("✅ Exercise $index: id=${exercise.id}, name=${exercise.name}")
                                }

                                // 🔧 使用全局单例累积添加选择结果
                                ExerciseSelectionHolder.addSelectedExercises(selectedExercises)
                                Timber.d("✅ Added exercises to global holder for draft edit")
                            } catch (e: Exception) {
                                Timber.e(e, "❌ Failed to store exercises in global holder")
                            }
                        },
                    )
                },
                onNavigateToPreview = {
                    // TODO: 实现预览功能
                },
            )
        }

        // 训练计划屏幕
        composable(
            route = WorkoutRoutes.PLAN_LIST,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            PlanScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToPlanEditor = { planId ->
                    if (planId != null) {
                        navController.navigate(
                            WorkoutRoutes.PLAN_EDIT.replace(
                                "{${WorkoutRoutes.PLAN_ID_ARG}}",
                                planId,
                            ),
                        )
                    } else {
                        // 🔧 修复：创建新计划时使用专门的CREATE_PLAN路由
                        navController.navigate(WorkoutRoutes.CREATE_PLAN)
                    }
                },
                onNavigateToPlanDetail = { planId ->
                    // TODO: 实现计划详情页面导航
                },
                onNavigateToTemplateDetail = { templateId ->
                    // 🔧 修复：添加参数验证防止空templateId导航
                    if (templateId.isNotBlank()) {
                        navController.navigate(
                            WorkoutRoutes.TEMPLATE_EDIT.replace(
                                "{${WorkoutRoutes.TEMPLATE_ID_ARG}}",
                                templateId,
                            ),
                        )
                    }
                },
                onNavigateToWorkoutSession = { planId ->
                    navController.navigate(
                        WorkoutRoutes.ACTIVE_SESSION.replace(
                            "{${WorkoutRoutes.SESSION_ID_ARG}}",
                            planId,
                        ),
                    )
                },
                onNavigateToCalendar = { planId, startDate ->
                    // TODO: 实现日历页面导航
                },
                onNavigateToAIGenerator = {
                    // TODO: 实现AI生成器页面导航
                },
            )
        }

        // 训练计划编辑屏幕 - 🔥 统一的计划编辑路由（支持创建和编辑）
        composable(
            route = WorkoutRoutes.PLAN_EDIT,
            arguments = listOf(navArgument(WorkoutRoutes.PLAN_ID_ARG) { type = NavType.StringType }),
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) { backStackEntry ->
            val planId = backStackEntry.arguments?.getString(WorkoutRoutes.PLAN_ID_ARG)
            PlanEditScreen(
                planId = planId?.takeIf { it != "new" }, // "new" 表示创建新计划
                onNavigateBack = { navController.popBackStack() },
            )
        }

        // 创建训练计划屏幕 - 🔥 重定向到统一的编辑屏幕
        composable(
            route = WorkoutRoutes.CREATE_PLAN,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            PlanEditScreen(
                planId = null, // 创建新计划
                onNavigateBack = { navController.popBackStack() },
            )
        }

        // 🔥 模板选择器已集成到PlanEditScreen中的FloatingTemplateSelector

        // 新训练会话屏幕（模板选择加载屏幕）
        composable(
            route = WorkoutRoutes.NEW_SESSION,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            SessionTemplateLoadingScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToSession = { sessionId ->
                    // Session 创建成功，导航到活动训练屏幕
                    navController.navigate(
                        WorkoutRoutes.ACTIVE_SESSION.replace(
                            "{${WorkoutRoutes.SESSION_ID_ARG}}",
                            sessionId,
                        ),
                    ) {
                        // 清除加载屏幕，避免返回时重新选择模板
                        popUpTo(WorkoutRoutes.NEW_SESSION) { inclusive = true }
                    }
                },
            )
        }

        // 活动训练屏幕
        composable(
            route = WorkoutRoutes.ACTIVE_SESSION,
            arguments = listOf(navArgument(WorkoutRoutes.SESSION_ID_ARG) { type = NavType.StringType }),
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString(WorkoutRoutes.SESSION_ID_ARG) ?: ""
            SessionScreen(
                sessionId = sessionId,
                onNavigateBack = { navController.popBackStack() },
                onNavigateToSummary = { summary ->
                    // TODO: 导航到训练总结页面
                    navController.popBackStack()
                },
            )
        }

        // 选择动作屏幕 - 集成动作库模块 - 🔧 修复版本
        composable(
            route = WorkoutRoutes.SELECT_EXERCISE,
            enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
            exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
            popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
            popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
        ) {
            // 🔧 关键修复：实现真正的动作选择结果处理
            exerciseLibraryNavigatable.navigateToPicker(
                navController = navController,
                multipleSelection = true,
                preSelectedIds = emptyList(),
                onResult = { selectedExercises ->
                    // 🔧 将选择的动作保存到SavedStateHandle，供模板编辑器使用
                    // 使用全局键来存储选择结果
                    navController.currentBackStackEntry?.savedStateHandle?.set(
                        "selected_exercises",
                        selectedExercises,
                    )

                    // 返回到模板编辑器
                    navController.popBackStack()
                },
            )
        }
    }
}
