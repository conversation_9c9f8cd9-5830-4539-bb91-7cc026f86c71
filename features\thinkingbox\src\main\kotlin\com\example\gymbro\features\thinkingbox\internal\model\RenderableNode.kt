package com.example.gymbro.features.thinkingbox.internal.model

import androidx.compose.runtime.Immutable
import androidx.compose.ui.text.AnnotatedString

/**
 * RenderableNode - UI 消费模型
 *
 * 基于 626_thinkingbox_impl_spec.md 规范实现
 * 供 LazyColumn 使用的渲染节点模型
 *
 * 设计原则：
 * - 不可变数据结构
 * - 包含完整渲染信息
 * - 支持层级结构
 * - 优化列表性能
 *
 * 性能优化：
 * - 使用 @Immutable 注解确保重组优化
 * - 计算属性使用内联函数减少对象分配
 * - 避免不必要的字符串操作
 *
 * @property id 节点唯一标识
 * @property level 层级深度（用于缩进和连线）
 * @property block 块内容
 * @property isExpanded 是否展开
 * @property channel 思考通道
 * @property type 节点类型
 * @property timestamp 时间戳
 * @property isCompleted 是否完成
 * @property animationState 动画状态
 * @property startTime 思考开始时间
 * @property endTime 思考结束时间
 * @property tokenCount Token 数量
 */
@Immutable
data class RenderableNode(
    /**
     * 节点唯一标识
     */
    val id: String,

    /**
     * 层级深度（用于缩进和连线）
     */
    val level: Int,

    /**
     * 块内容
     */
    val block: BlockContent,

    /**
     * 是否展开
     */
    val isExpanded: Boolean = true,

    /**
     * 思考通道
     */
    val channel: String = "default",

    /**
     * 节点类型
     */
    val type: NodeType = NodeType.PHASE,

    /**
     * 时间戳
     */
    val timestamp: Long = System.currentTimeMillis(),

    /**
     * 是否完成
     */
    val isCompleted: Boolean = false,

    /**
     * 动画状态
     */
    val animationState: AnimationState = AnimationState.IDLE,

    /**
     * 思考开始时间
     */
    val startTime: Long = System.currentTimeMillis(),

    /**
     * 思考结束时间
     */
    val endTime: Long? = null,

    /**
     * Token 数量
     */
    val tokenCount: Int = 0,
) {
    /**
     * 是否可展开
     * 使用内联函数优化性能
     */
    inline val isExpandable: Boolean
        get() = block.content.length > 100 || type == NodeType.TOOL_CALL

    /**
     * 显示内容（考虑展开状态）
     * 优化：避免不必要的字符串操作
     */
    inline val displayContent: AnnotatedString
        get() = when {
            isExpanded || !isExpandable -> block.content
            else -> AnnotatedString(
                text = block.content.text.substring(0, minOf(100, block.content.text.length)) + "...",
            )
        }

    // 🔥 【唯一真源性修复】移除重复的时间计算和格式化逻辑
    // 统一使用UiState.getFormattedDuration()和UiState.thinkingDuration
}

/**
 * 块内容 - 节点的实际内容
 *
 * @property title 标题
 * @property content 内容（支持富文本）
 * @property format 内容格式
 * @property metadata 元数据
 */
@Immutable
data class BlockContent(
    /**
     * 标题
     */
    val title: String,

    /**
     * 内容（支持富文本）
     */
    val content: AnnotatedString,

    /**
     * 内容格式
     */
    val format: ContentFormat = ContentFormat.MARKDOWN,

    /**
     * 元数据
     */
    val metadata: Map<String, String> = emptyMap(),
) {
    /**
     * 纯文本内容
     * 使用内联函数优化访问
     */
    inline val plainText: String
        get() = content.text

    /**
     * 内容长度
     * 使用内联函数优化访问
     */
    inline val length: Int
        get() = content.length

    /**
     * 是否为空
     * 使用内联函数优化访问
     */
    inline val isEmpty: Boolean
        get() = content.text.isBlank()
}

/**
 * 节点类型枚举
 */
enum class NodeType {
    /**
     * 思考阶段
     */
    PHASE,

    /**
     * 工具调用
     */
    TOOL_CALL,

    /**
     * 最终结果
     */
    FINAL_RESULT,

    /**
     * 错误信息
     */
    ERROR,

    /**
     * 普通文本
     */
    TEXT,

    /**
     * 思考摘要（折叠状态）
     */
    SUMMARY,
}

/**
 * 内容格式枚举
 */
enum class ContentFormat {
    /**
     * Markdown 格式
     */
    MARKDOWN,

    /**
     * 纯文本
     */
    PLAIN_TEXT,

    /**
     * HTML 格式
     */
    HTML,

    /**
     * JSON 格式
     */
    JSON,
}

/**
 * 动画状态枚举
 */
enum class AnimationState {
    /**
     * 空闲状态
     */
    IDLE,

    /**
     * 进入动画
     */
    ENTERING,

    /**
     * 退出动画
     */
    EXITING,

    /**
     * 脉冲动画
     */
    PULSING,

    /**
     * 震动动画
     */
    SHAKING,
}

/**
 * 扩展函数：将字符串转换为 AnnotatedString
 */
fun String.toAnnotatedString(): AnnotatedString = AnnotatedString(this)
