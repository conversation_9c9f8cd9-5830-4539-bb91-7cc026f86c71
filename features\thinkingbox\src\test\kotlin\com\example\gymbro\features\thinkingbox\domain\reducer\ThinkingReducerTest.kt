package com.example.gymbro.features.thinkingbox.domain.reducer

import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingReducer
import org.junit.Assert.*
import org.junit.Test

/**
 * ThinkingReducer 测试套件
 *
 * 验证双时序架构的状态管理逻辑
 */
class ThinkingReducerTest {

    @Test
    fun `should handle PreThinkChunk accumulation`() {
        val initialState = ThinkingReducer.createInitialState("test-session")

        val event1 = ThinkingEvent.PreThinkChunk("First chunk")
        val state1 = ThinkingReducer.reduce(initialState, event1)

        assertEquals("First chunk", state1.preThinking)
        assertTrue(state1.isStreaming)

        val event2 = ThinkingEvent.PreThinkChunk(" Second chunk")
        val state2 = ThinkingReducer.reduce(state1, event2)

        assertEquals("First chunk Second chunk", state2.preThinking)
    }

    @Test
    fun `should handle PreThinkEnd without clearing content`() {
        val initialState = ThinkingReducer.createInitialState("test-session")
            .copy(preThinking = "Pre-think content")

        val event = ThinkingEvent.PreThinkEnd
        val newState = ThinkingReducer.reduce(initialState, event)

        // PreThinkEnd不应该清空内容，只是标记结束
        assertEquals("Pre-think content", newState.preThinking)
    }

    @Test
    fun `should activate first phase immediately`() {
        val initialState = ThinkingReducer.createInitialState("test-session")

        val event = ThinkingEvent.PhaseStart("phase1", "Phase 1 Title")
        val newState = ThinkingReducer.reduce(initialState, event)

        assertEquals("phase1", newState.activePhaseId)
        assertTrue(newState.phases.containsKey("phase1"))
        assertEquals("Phase 1 Title", newState.phases["phase1"]?.title)
        assertTrue(newState.pending.isEmpty())
        assertTrue(newState.isStreaming)
    }

    @Test
    fun `should queue second phase when first is active`() {
        val initialState = ThinkingReducer.createInitialState("test-session")
            .copy(
                activePhaseId = "phase1",
                phases = java.util.LinkedHashMap(
                    mapOf("phase1" to ThinkingReducer.PhaseUi("phase1", "Title 1")),
                ),
            )

        val event = ThinkingEvent.PhaseStart("phase2", "Phase 2 Title")
        val newState = ThinkingReducer.reduce(initialState, event)

        assertEquals("phase1", newState.activePhaseId) // 保持当前活跃phase
        assertTrue(newState.phases.containsKey("phase2"))
        assertEquals(java.util.ArrayDeque(listOf("phase2")), newState.pending)
    }

    @Test
    fun `should accumulate phase content`() {
        val phase1 = ThinkingReducer.PhaseUi("phase1", "Title", "Initial content")
        val initialState = ThinkingReducer.createInitialState("test-session")
            .copy(phases = java.util.LinkedHashMap(mapOf("phase1" to phase1)))

        val event = ThinkingEvent.PhaseContent("phase1", " Additional content")
        val newState = ThinkingReducer.reduce(initialState, event)

        assertEquals("Initial content Additional content", newState.phases["phase1"]?.content)
    }

    @Test
    fun `should ignore content for non-existent phase`() {
        val initialState = ThinkingReducer.createInitialState("test-session")

        val event = ThinkingEvent.PhaseContent("non-existent", "Content")
        val newState = ThinkingReducer.reduce(initialState, event)

        assertEquals(initialState, newState) // 状态不应该改变
    }

    @Test
    fun `should handle phase end and wait for animation finished`() {
        // 🔥 【v1修复更新】PhaseEnd不再直接切换phase，需要等待PhaseAnimationFinished
        val phase1 = ThinkingReducer.PhaseUi("phase1", "Title 1", "Content 1")
        val phase2 = ThinkingReducer.PhaseUi("phase2", "Title 2", "Content 2")
        val initialState = ThinkingReducer.createInitialState("test-session")
            .copy(
                activePhaseId = "phase1",
                phases = java.util.LinkedHashMap(mapOf("phase1" to phase1, "phase2" to phase2)),
                pending = java.util.ArrayDeque(listOf("phase2")),
            )

        // PhaseEnd只标记完成，不切换activePhaseId
        val event = ThinkingEvent.PhaseEnd("phase1")
        val stateAfterEnd = ThinkingReducer.reduce(initialState, event)

        assertEquals("phase1", stateAfterEnd.activePhaseId) // 保持不变
        assertEquals(1, stateAfterEnd.pending.size) // pending保持不变
        assertTrue(stateAfterEnd.phases["phase1"]?.isComplete == true) // 标记为完成

        // PhaseAnimFinished才真正切换
        val animEvent = ThinkingEvent.PhaseAnimFinished("phase1")
        val finalState = ThinkingReducer.reduce(stateAfterEnd, animEvent)

        assertEquals("phase2", finalState.activePhaseId) // 现在才切换
        assertTrue(finalState.pending.isEmpty()) // pending被清空
    }

    @Test
    fun `should handle phase end with no pending phases and wait for animation`() {
        // 🔥 【v1修复更新】即使没有pending phases，也需要等待PhaseAnimationFinished
        val phase1 = ThinkingReducer.PhaseUi("phase1", "Title 1", "Content 1")
        val initialState = ThinkingReducer.createInitialState("test-session")
            .copy(
                activePhaseId = "phase1",
                phases = java.util.LinkedHashMap(mapOf("phase1" to phase1)),
                pending = java.util.ArrayDeque<String>(),
            )

        // PhaseEnd只标记完成
        val event = ThinkingEvent.PhaseEnd("phase1")
        val stateAfterEnd = ThinkingReducer.reduce(initialState, event)

        assertEquals("phase1", stateAfterEnd.activePhaseId) // 保持不变
        assertTrue(stateAfterEnd.pending.isEmpty())
        assertTrue(stateAfterEnd.phases["phase1"]?.isComplete == true)

        // PhaseAnimationFinished才清空activePhaseId
        val animEvent = ThinkingEvent.PhaseAnimationFinished("phase1")
        val finalState = ThinkingReducer.reduce(stateAfterEnd, animEvent)

        assertNull(finalState.activePhaseId) // 现在才清空
        assertTrue(finalState.pending.isEmpty())
    }

    @Test
    fun `should handle final answer arrival`() {
        val initialState = ThinkingReducer.createInitialState("test-session")
            .copy(
                activePhaseId = "phase1",
                pending = java.util.ArrayDeque(listOf("phase2")),
                isStreaming = true,
            )

        val markdown = "# Final Answer\n\nThis is the final answer."
        val event = ThinkingEvent.FinalArrived(markdown)
        val newState = ThinkingReducer.reduce(initialState, event)

        assertEquals(markdown, newState.finalMarkdown)
        assertFalse(newState.isStreaming)
        assertNull(newState.activePhaseId)
        assertTrue(newState.pending.isEmpty())
        assertTrue(newState.hasFinal)
        assertTrue(newState.isCompleted)
    }

    @Test
    fun `should handle batch event processing`() {
        val initialState = ThinkingReducer.createInitialState("test-session")

        val events = listOf(
            ThinkingEvent.PreThinkChunk("Pre-think"),
            ThinkingEvent.PreThinkEnd,
            ThinkingEvent.PhaseStart("phase1", "Analysis"),
            ThinkingEvent.PhaseContent("phase1", "Content"),
            ThinkingEvent.PhaseEnd("phase1"),
            ThinkingEvent.FinalArrived("Final answer"),
        )

        val finalState = ThinkingReducer.reduceBatch(initialState, events)

        assertEquals("Pre-think", finalState.preThinking)
        assertEquals("Final answer", finalState.finalMarkdown)
        assertTrue(finalState.phases.containsKey("phase1"))
        assertTrue(finalState.phases["phase1"]?.isComplete == true)
        assertFalse(finalState.isStreaming)
    }

    @Test
    fun `should reset state correctly`() {
        val existingState = ThinkingReducer.createInitialState("old-session")
            .copy(
                activePhaseId = "phase1",
                phases = java.util.LinkedHashMap(mapOf("phase1" to ThinkingReducer.PhaseUi("phase1"))),
                preThinking = "Old content",
                finalMarkdown = "Old final",
            )

        val resetState = ThinkingReducer.resetState(existingState, "new-session")

        assertEquals("new-session", resetState.sessionId)
        assertNull(resetState.activePhaseId)
        assertTrue(resetState.phases.isEmpty())
        assertNull(resetState.preThinking)
        assertNull(resetState.finalMarkdown)
        assertTrue(resetState.pending.isEmpty())
    }

    @Test
    fun `should handle version increments for compose recomposition`() {
        val initialState = ThinkingReducer.createInitialState("test-session")
        val initialVersion = initialState.version

        val event = ThinkingEvent.PreThinkChunk("Content")
        val newState = ThinkingReducer.reduce(initialState, event)

        assertEquals(initialVersion + 1, newState.version)
    }
}
