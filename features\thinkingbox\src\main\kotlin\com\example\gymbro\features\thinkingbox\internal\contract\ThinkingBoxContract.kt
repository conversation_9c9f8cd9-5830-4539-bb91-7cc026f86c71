package com.example.gymbro.features.thinkingbox.internal.contract

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent

/**
 * ThinkingBox MVI Contract
 * 定义Intent、State、Effect的完整契约
 */
object ThinkingBoxContract {

    /**
     * UI状态 - 简化版本，符合finalmermaid大纲.md要求
     *
     * 🔥 【重构改进】移除网络状态和复杂双时序字段
     * 🎯 【核心字段】仅保留UI渲染必需的状态字段
     */
    @Immutable
    data class State(
        // 🔥 【核心状态】基础字段
        val messageId: String = "",
        val phases: List<PhaseUi> = emptyList(),
        val activePhaseId: String? = null,
        val preThinking: String? = null,

        // 🔥 【流式状态】简化的流式控制
        val isStreaming: Boolean = false,
        val isThinkingComplete: Boolean = false,

        // 🔥 【Final相关】最终内容状态
        val finalMarkdown: String? = null,
        val finalTokens: List<String> = emptyList(),
        val finalRichTextReady: Boolean = false,
        val finalContentArrived: Boolean = false,
        val isFinalStreaming: Boolean = false,

        // 🔥 【UI控制】简化的UI状态
        val summaryTextVisible: Boolean = false,
        val showSummaryPanel: Boolean = false,
        val isFinalRenderingComplete: Boolean = false,

        // 🔥 【统计信息】基础统计
        val thinkingDuration: Long = 0L,
        val totalTokens: Int = 0,

        // 🔥 【基础状态】错误和加载状态
        val error: UiText? = null,
        val isLoading: Boolean = false,
    ) : UiState {
        // 🔥 【简化计算属性】仅保留UI渲染必需的计算属性

        /**
         * 计算是否有实际思考内容
         */
        val hasActualThinkingContent: Boolean
            get() = phases.isNotEmpty() || !preThinking.isNullOrBlank()

        /**
         * 计算是否应该显示final文本
         */
        val shouldShowFinalText: Boolean
            get() = finalRichTextReady && (finalTokens.isNotEmpty() || !finalMarkdown.isNullOrBlank())

        /**
         * 计算是否应该显示ThinkingHeader
         */
        val shouldShowThinkingHeader: Boolean
            get() = when {
                phases.isNotEmpty() -> false
                isThinkingComplete -> false
                shouldShowFinalText -> false
                !preThinking.isNullOrBlank() -> true
                else -> isStreaming
            }
    }

    /**
     * 用户意图 - 简化版本，移除网络相关Intent
     */
    sealed interface Intent : AppIntent {
        // 🔥 【核心生命周期】启动和初始化
        data class StartStream(
            val sessionId: String,
            val userMessageId: String,
            val aiResponseId: String,
            val prompt: String,
        ) : Intent

        data class Initialize(val messageId: String) : Intent
        data object Reset : Intent

        // 🔥 【事件处理】ThinkingEvent处理
        data class HandleThinkingEvent(
            val event: ThinkingEvent,
        ) : Intent

        // 🔥 【UI交互】简化的UI控制
        data class PhaseAnimationFinished(val phaseId: String) : Intent
        data object ToggleSummaryPanel : Intent
        data object CompleteSummaryAnimation : Intent
        data object CompleteFinalRendering : Intent

        // 🔥 【双时序握手】UI动画完成事件
        data object ThinkingBoxClosed : Intent
        data object FinalRenderingComplete : Intent

        // 🔥 【错误处理】基础错误处理
        data object ClearError : Intent
    }

    /**
     * 副作用 - 简化版本，移除网络相关Effect
     */
    sealed interface Effect : UiEffect {
        // 🔥 【UI效果】基础UI控制
        data object ScrollToBottom : Effect

        // 🔥 【外部通信】模块间通信
        data class SendEventToExternal(
            val event: ThinkingEvent,
        ) : Effect

        data class NotifyMessageComplete(
            val messageId: String,
            val finalMarkdown: String,
        ) : Effect

        // 🔥 【Token流】启动Token流监听
        data class StartTokenStreamListening(val messageId: String) : Effect

        // 🔥 【错误处理】基础错误显示
        data class ShowError(val error: UiText) : Effect

        // 🔥 【调试日志】开发调试
        data class LogDebug(val message: String) : Effect
    }
}
