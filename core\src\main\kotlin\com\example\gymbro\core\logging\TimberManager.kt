package com.example.gymbro.core.logging

import android.annotation.SuppressLint
import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【重构】增强的 Timber 日志管理器
 *
 * 统一管理Timber日志系统的初始化、配置和控制。
 * 集成模块级别的日志控制和性能优化。
 */
@Singleton
class TimberManager
@Inject
constructor(
    private val loggingConfig: LoggingConfig,
) {
    /**
     * 🔥 【修复】初始化日志系统 - 使用专用ThinkingBoxLogTree
     *
     * 移除ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree作为主要日志处理器
     *
     * @param isDebug 是否为调试模式
     * @param environment 环境类型
     * @param enableThinkingBoxMode 是否启用ThinkingBox专用模式
     */
    fun initialize(
        isDebug: Boolean,
        environment: LoggingConfig.Environment = LoggingConfig.Environment.DEVELOPMENT,
        enableThinkingBoxMode: Boolean = true, // 🔥 【临时调试】启用ThinkingBox专用日志
    ) {
        // 设置环境
        loggingConfig.setEnvironment(environment)

        // 清除所有已有的Tree
        Timber.uprootAll()

        // 根据环境和模式安装适当的Tree
        when {
            isDebug && enableThinkingBoxMode -> {
                // 🔥 【修复】ThinkingBox专用模式：使用专用的ThinkingBoxLogTree作为主要日志处理器
                // 移除ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree
                loadThinkingBoxLogTree()

                // 🔥 【重构清理】移除WorkoutLogTree动态加载，统一由ModuleAwareTree处理
                // WorkoutLogTree保留为工具类，避免重复Tree处理同一日志

                // 🔥 【新增】动态加载NetworkLogTree
                loadNetworkLogTree()

                Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox专用日志系统已启动 - 使用专用ThinkingBoxLogTree + 统一模块路由 + 网络支持")
            }

            isDebug -> {
                // 开发环境：使用模块感知的Tree + NetworkLogTree
                Timber.plant(ModuleAwareTree(loggingConfig))

                // 🔥 【重构清理】移除WorkoutLogTree动态加载，统一由ModuleAwareTree处理
                // 避免同一日志被多个Tree重复处理

                // 🔥 【新增】动态加载NetworkLogTree
                loadNetworkLogTree()

                Timber.tag("LOG-MANAGER").i("🔥 开发环境日志系统已启动 - 模块感知模式 + 统一模块路由 + 网络支持")
            }

            environment == LoggingConfig.Environment.PRODUCTION -> {
                // 生产环境：只记录错误
                Timber.plant(ReleaseTree())

                // 🔥 【关键修复】生产环境也需要加载NetworkLogTree以处理CNET-ERROR
                loadNetworkLogTree()

                Timber.tag("LOG-MANAGER").i("🔥 生产环境日志系统已启动 - 仅错误模式 + 网络错误支持")
            }

            else -> {
                // 测试环境：适度日志
                Timber.plant(ModuleAwareTree(loggingConfig))

                // 🔥 【关键修复】测试环境也需要加载NetworkLogTree以处理CNET标签
                loadNetworkLogTree()

                Timber.tag("LOG-MANAGER").i("🔥 测试环境日志系统已启动 - 适度日志模式 + 网络支持")
            }
        }

        // 启用协程调试支持（仅在调试模式下）
        if (isDebug) {
            System.setProperty("kotlinx.coroutines.debug", "on")
        }
    }

    /**
     * 🔥 【新增】专用于应用层的ThinkingBox模式初始化
     *
     * 符合Clean Architecture原则，应用层通过此方法启用ThinkingBox功能
     * 而无需直接依赖features模块
     */
    fun initializeWithThinkingBoxSupport(isDebug: Boolean) {
        initialize(
            isDebug = isDebug,
            environment = if (isDebug) LoggingConfig.Environment.DEVELOPMENT else LoggingConfig.Environment.PRODUCTION,
            enableThinkingBoxMode = isDebug,
        )

        if (isDebug) {
            // 配置ThinkingBox专用的模块设置
            enableThinkingBoxDebugLogs()
            // 🔥 移除噪音日志：ThinkingBox专用模式启动信息
        }
    }

    /**
     * 启用ThinkingBox调试日志
     */
    fun enableThinkingBoxDebugLogs() {
        loggingConfig.updateModuleConfig(
            LoggingConfig.MODULE_THINKING_BOX,
            LoggingConfig.ModuleLogConfig(
                enabled = true,
                minLevel = Log.DEBUG,
                tags = setOf(
                    // 🔥 【核心组件标签】
                    "TB-VIEWMODEL", "TB-REDUCER", "TB-PARSER", "TB-DOMAIN-MAPPER",
                    "TB-XML-SCANNER", "TB-GUARDRAIL",
                    // 🔥 【数据处理标签】
                    "TB-TOKEN-FLOW", "TB-RAW-COLLECTOR", "TB-RAW-PROCESSOR",
                    // 🔥 【UI组件标签】
                    "TB-UI", "TB-LIFECYCLE", "TB-AUTO-SCROLL", "TB-FINAL-RENDERER",
                    // 🔥 【XML处理标签】
                    "TB-XML-INPUT", "TB-XML-BUFFER", "TB-XML-PARSE", "TB-XML-OUTPUT",
                    // 🔥 【调试标签】
                    "TB-DEBUG", "TB-FIX",
                    "TB-TAG-PHASE", "TB-TAG-FINAL", "TB-TAG-CLOSE", "TB-PROCESSED",
                    "TB-XML-INPUT", "TB-XML-BUFFER", "TB-XML-PARTIAL", "TB-XML-PARSE", "TB-XML-OUTPUT",
                ),
                sampleRate = 1,
            ),
        )
        Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox 调试日志已启用 - 包含Token流修复验证标签")
    }

    /**
     * 运行时切换环境
     */
    fun switchEnvironment(environment: LoggingConfig.Environment) {
        loggingConfig.setEnvironment(environment)
        Timber.tag("LOG-MANAGER").i("🔥 已切换到环境: $environment")
    }

    /**
     * 获取当前环境
     */
    fun getCurrentEnvironment(): LoggingConfig.Environment = loggingConfig.getCurrentEnvironment()

    /**
     * 🔥 【修复】动态加载ThinkingBoxLogTree作为主要日志处理器
     *
     * 替换ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree
     * 🔥 【Token流修复】确保ThinkingBox相关日志能够正确输出，包含所有修复验证功能
     */
    private fun loadThinkingBoxLogTree() {
        try {
            // 动态加载ThinkingBoxLogTree类
            val thinkingBoxLogTreeClass = Class.forName(
                "com.example.gymbro.features.thinkingbox.logging.ThinkingBoxLogTree"
            )

            // 🔥 【修复】ThinkingBoxLogTree使用无参构造函数（继承自Timber.DebugTree）
            val constructor = thinkingBoxLogTreeClass.getConstructor()
            val thinkingBoxLogTree = constructor.newInstance() as Timber.Tree

            // 植入ThinkingBoxLogTree作为主要日志处理器
            Timber.plant(thinkingBoxLogTree)

            Timber.tag("LOG-MANAGER").i("🔥 ThinkingBoxLogTree 已加载作为主要日志处理器 - 包含Token流修复调试功能")

        } catch (e: Exception) {
            Timber.tag("LOG-MANAGER").w("ThinkingBoxLogTree 加载失败，回退到基础日志: ${e.message}")
            // 回退到基础日志树
            Timber.plant(Timber.DebugTree())
        }
    }

    /**
     * 🔥 【新增】动态加载NetworkLogTree
     *
     * 遵循现有的模块化日志架构，动态加载网络模块的专用日志树
     * 🔥 【架构集成】使用LoggingConfig统一管理，与其他模块保持一致
     */
    private fun loadNetworkLogTree() {
        try {
            // 动态加载NetworkLogTree类
            val networkLogTreeClass = Class.forName(
                "com.example.gymbro.core.network.logging.NetworkLogTree"
            )

            // 🔥 【架构集成】使用LoggingConfig作为构造参数，与WorkoutLogTree保持一致
            val constructor = networkLogTreeClass.getConstructor(LoggingConfig::class.java)
            val networkLogTree = constructor.newInstance(loggingConfig) as Timber.Tree

            // 植入NetworkLogTree
            Timber.plant(networkLogTree)

            Timber.tag("LOG-MANAGER").i("🔥 NetworkLogTree 已加载 - 环境: ${loggingConfig.getCurrentEnvironment()}")

        } catch (e: Exception) {
            Timber.tag("LOG-MANAGER").w("NetworkLogTree 加载失败: ${e.message}")
            // 网络日志树加载失败不影响其他功能，继续运行
        }
    }

    /**
     * 设置全局日志标签过滤器
     *
     * @param tagFilter 标签过滤函数
     */
    fun setGlobalTagFilter(tagFilter: (String?) -> String) {
        globalTagFilter = tagFilter
    }

    companion object {
        private const val LOG_STACK_INDICATOR = "LogStack"

        // 全局标签过滤器
        @Volatile
        private var globalTagFilter: ((String?) -> String)? = null

        /**
         * 应用全局标签过滤器
         */
        internal fun applyTagFilter(
            tag: String?,
        ): String = globalTagFilter?.invoke(tag) ?: tag ?: "GymBro"

        /**
         * 基于当前类名生成的标签
         */
        @SuppressLint("DefaultLocale")
        fun createTag(): String {
            val stackTrace = Thread.currentThread().stackTrace
            for (element in stackTrace) {
                val className = element.className
                if (!className.contains("TimberManager") &&
                    !className.contains("Thread") &&
                    !className.contains("VMStack") &&
                    !className.contains("Method") &&
                    !className.startsWith("java.") &&
                    !className.startsWith("android.") &&
                    !className.startsWith("dalvik.")
                ) {
                    val fullClassName = className.substringAfterLast('.')
                    return fullClassName.substring(fullClassName.lastIndexOf('.') + 1)
                }
            }
            return "Unknown"
        }
    }
}
