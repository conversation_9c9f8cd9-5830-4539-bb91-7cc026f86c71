﻿package com.example.gymbro.features.coach.aicoach

import android.net.Uri
import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.shared.common.model.QuickActionCategoryGroup
import com.example.gymbro.shared.models.coach.SuggestionConfig
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableSet
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.datetime.Instant
import timber.log.Timber

/**
 * AI Coach Contract - v6.0-重构版MVI契约定义
 *
 * 重构原则：
 * - 按逻辑分组，清晰组织
 * - 移除未使用和重复代码
 * - 保持核心功能完整性
 * - 遵循MVI架构的Intent/State/Effect模式
 */
internal object AiCoachContract {
    // ==================== 状态相关 ====================

    /**
     * 🔥 流式状态密封接口 - 替换模糊的 isStreaming 布尔值
     *
     * 职责明确的状态管理，消除UI层的状态推断逻辑
     */
    sealed interface StreamingState {
        /** 空闲状态 - 没有进行中的流式响应 */
        object Idle : StreamingState

        /** 等待第一个token - 已发送请求，等待AI响应开始（此时应显示Header） */
        object AwaitingFirstToken : StreamingState

        /** 正在思考 - 正在接收和处理思考内容（此时应显示StageCard） */
        data class Thinking(val messageId: String) : StreamingState
    }

    /**
     * 🔥 Function Call结果数据
     */
    @Immutable
    data class FunctionCallResult(
        val functionName: String,
        val success: Boolean,
        val data: String? = null,
        val error: String? = null,
        val actionTriggered: String? = null,
        val executionPath: String? = null,
    )

    /**
     * 🔥 History RAG 上下文项数据类
     */
    @Immutable
    data class HistoryContextItem(
        val id: String,
        val content: String,
        val relevanceScore: Float,
        val timestamp: Instant,
        val messageType: String,
    )

    /**
     * 消息保存状态
     */
    enum class SaveStatus {
        PENDING, // 等待保存
        SAVING, // 正在保存
        SAVED, // 已保存
        FAILED, // 保存失败
    }

    /**
     * 消息UI模型
     */
    @Immutable
    data class MessageUi(
        val id: String,
        val content: String,
        val isFromUser: Boolean,
        val timestamp: Instant,
        // val isStreaming: Boolean = false, // 🔥 移除：流式状态由 State.isStreaming 和 ThinkingBox 的 UiState 统一管理
        val hasError: Boolean = false,
        // 🔥 Function Call结果支持
        val functionCallResult: FunctionCallResult? = null,
        // 🔥 修复Final渲染：添加finalMarkdown字段支持ThinkingBox Final内容
        val finalMarkdown: String? = null,
        // 🔥 新增：消息保存状态，提供UI反馈
        val saveStatus: SaveStatus = SaveStatus.PENDING,
    ) {
        val isFromAi: Boolean get() = !isFromUser
        val hasFunctionCallResult: Boolean get() = functionCallResult != null
        val hasFinalMarkdown: Boolean get() = finalMarkdown != null && finalMarkdown.isNotBlank()
        val isSaved: Boolean get() = saveStatus == SaveStatus.SAVED
        val isSaving: Boolean get() = saveStatus == SaveStatus.SAVING
        val saveFailed: Boolean get() = saveStatus == SaveStatus.FAILED
    }

    /**
     * AI Coach 主状态 - 单一数据源
     *
     * 🔥 【Week 2优化】重新定义messages字段职责：
     * - messages: 仅存储当前活跃会话的实时消息（非流式）
     * - historyFlow: 处理所有已保存消息（历史+当前会话，分页加载）
     * - thinkingState: 处理流式消息显示
     */
    @Immutable
    data class State(
        // 核心状态
        val isLoading: Boolean = false,
        val activeSession: ChatSession? = null,
        // 🔥 【职责重定义】当前会话的实时消息，与historyFlow形成互补
        val messages: ImmutableList<MessageUi> = persistentListOf(),
        val inputState: InputState, // 无默认值，强制在创建时提供正确的placeholder
        val errorCode: ErrorCode? = null,
        // 🔥 【P0修复】移除ThinkingBox状态字段 - Coach不再直接管理ThinkingBox状态
        // 🔥 【独立SummaryCard设计】独立的SummaryCard状态
        val summaryCardState: SummaryCardState = SummaryCardState(),
        // 🔥 【SuggestionChip交互】建议标签显示状态控制
        val suggestionChipsVisible: Boolean = true,
        // 🔥 【AI对话重置修复】待重置的消息ID集合
        val pendingResetMessageIds: ImmutableSet<String> = persistentSetOf(),
        // 功能状态
        val searchState: SearchState = SearchState(),
        val quickActionState: QuickActionState = QuickActionState(),
        val sessionState: SessionManagementState = SessionManagementState(),
        // 🔥 新增：历史记录状态
        val historyState: HistoryState = HistoryState(),
        // 🔥 【架构重构】流式响应状态 - 使用职责明确的 StreamingState 替换模糊的布尔值
        val streamingState: StreamingState = StreamingState.Idle,
        // API状态
        val currentApiProvider: ApiProvider = ApiProvider.DEEPSEEK,
        val apiProviderStatus: ApiProviderStatus = ApiProviderStatus.READY,
        // Prompt模式状态
        val currentPromptMode: String = "standard",
        // 悬浮Card状态机
        val panelState: PanelState = PanelState.COLLAPSED,
        val isPanelVisible: Boolean = false,
        val expandedCard: String? = null,
        val cards: ImmutableList<String> = persistentListOf(),
        // Profile状态
        val profilePromptState: String? = null,
        // 动态建议配置
        val suggestionConfig: SuggestionConfig? = null,
        // 🔥 消息队列 - 解决会话初始化和消息发送的竞态条件
        val pendingMessage: String? = null,
        // 🔥 新增：持有当前活动的Function Call结果，用于驱动UI弹窗
        val activeFunctionCall: FunctionCallResult? = null,
        // 🔥 图片选择器显示状态
        val isImagePickerVisible: Boolean = false,
    ) : UiState {
        /**
         * 派生状态：是否可以发送消息
         * 🔥 【架构重构】使用 StreamingState 进行精确的状态判断
         */
        val canSendMessage: Boolean
            get() {
                val hasText = inputState.text.isNotBlank()
                val notStreaming = streamingState is StreamingState.Idle
                val notLoading = !isLoading
                // 🔥 修复：移除sessionExists要求，允许在没有会话时发送消息来触发会话创建
                // val sessionExists = activeSession != null

                // 用于调试的日志 (仅在状态变化时记录)
                if (!hasText || !notStreaming || !notLoading) {
                    Timber.v(
                        "🔍 canSendMessage=false: hasText=$hasText, notStreaming=$notStreaming, " +
                            "notLoading=$notLoading, streamingState=${streamingState::class.simpleName}",
                    )
                }

                // 🔥 【架构重构】确保不在加载状态，且输入不为空，且流式状态为Idle
                // 不再要求会话存在，因为发送消息时会自动创建会话
                return hasText && notStreaming && notLoading
            }

        /**
         * 派生状态：当前会话ID
         */
        val activeSessionId: String?
            get() = activeSession?.id

        /**
         * 🔥 【Week 2优化】派生状态：是否显示欢迎建议（ChatGPT式）
         * 基于会话状态而非消息列表判断
         */
        val shouldShowWelcomeSuggestions: Boolean
            get() = inputState.text.isEmpty() && activeSession == null && !isLoading

        /**
         * 🔥 【Week 2优化】派生状态：是否显示悬浮建议卡片
         * 基于会话状态判断
         */
        val showSuggestionCard: Boolean
            get() = activeSession == null && inputState.text.isEmpty() && panelState == PanelState.COLLAPSED

        /**
         * 🔥 【Week 2优化】派生状态：是否显示输入提示
         * 基于会话存在性和输入焦点判断
         */
        val shouldShowInputHints: Boolean
            get() = inputState.text.isEmpty() && activeSession != null && inputState.isFocused

        /**
         * 派生状态：是否显示建议面板
         */
        fun shouldShowSuggestionPanel(): Boolean {
            val inputText = this.inputState.text.trim()
            if (inputText.isEmpty()) return false

            val coreKeywords = listOf("减脂", "有氧", "力量", "瑜伽", "饮食")
            val generalKeywords =
                listOf(
                    "训练",
                    "锻炼",
                    "运动",
                    "健身",
                    "练习",
                    "计划",
                    "方案",
                    "营养",
                    "增肌",
                    "减肥",
                    "塑形",
                    "拉伸",
                    "无氧",
                    "跑步",
                    "胸肌",
                    "背部",
                    "腿部",
                    "肩膀",
                    "手臂",
                    "怎么",
                    "如何",
                    "什么",
                    "为什么",
                    "帮我",
                )

            val allKeywords = coreKeywords + generalKeywords
            return allKeywords.any { keyword ->
                inputText.contains(keyword, ignoreCase = true)
            }
        }
    }

    /**
     * 输入状态
     */
    @Immutable
    data class InputState(
        val text: String = "",
        val isComposing: Boolean = false,
        val placeholder: String, // 无默认值，强制通过资源管理
        // 附件功能
        val selectedImages: ImmutableList<String> = persistentListOf(),
        val isUploading: Boolean = false,
        val uploadProgress: Float? = null,
        val pendingImages: ImmutableList<Uri> = persistentListOf(),
        // 语音输入
        val showVoiceInput: Boolean = false,
        val voiceRecording: Boolean = false,
        val voiceTranscript: String? = null,
        // 输入框状态
        val isFocused: Boolean = false,
        val maxLength: Int = 2000,
        // 工具面板
        val isToolbarExpanded: Boolean = false,
        val sendAnimState: SendAnimState = SendAnimState.IDLE,
        val sendButtonState: SendButtonState = SendButtonState.VOICE,
    ) {
        /**
         * 派生状态：是否可以发送消息
         */
        val canSendMessage: Boolean
            get() = text.isNotBlank()

        /**
         * 派生状态：字符计数
         */
        val characterCount: Int
            get() = text.length
    }

    /**
     * 搜索状态
     */
    @Immutable
    data class SearchState(
        val query: String = "",
        val results: ImmutableList<MessageUi> = persistentListOf(),
        val isSearching: Boolean = false,
        val searchError: String? = null,
        val searchSessionId: String? = null,
        val hasSearched: Boolean = false,
    )

    /**
     * 历史记录状态
     */
    @Immutable
    data class HistoryState(
        val isVisible: Boolean = false,
        val conversations: ImmutableList<com.example.gymbro.features.coach.shared.model.Conversation> =
            persistentListOf(),
        val isLoading: Boolean = false,
        val error: String? = null,
        val searchQuery: String = "",
        val selectedConversationId: String? = null,
        val hasMore: Boolean = true,
        val currentPage: Int = 0,
    )

    /**
     * 快速操作状态
     */
    @Immutable
    data class QuickActionState(
        val categories: ImmutableList<QuickActionCategoryGroup> = persistentListOf(),
        val isLoading: Boolean = false,
        val selectedAction: com.example.gymbro.domain.shared.common.model.QuickAction? = null,
        // 🔥 【预设数据清理】移除硬编码的建议列表，改为从数据层动态获取
        val suggestions: ImmutableList<com.example.gymbro.domain.shared.common.model.QuickAction> =
            persistentListOf(),
    )

    /**
     * 会话管理状态
     */
    @Immutable
    data class SessionManagementState(
        val allSessions: ImmutableList<ChatSession> = persistentListOf(),
        val isLoadingSessions: Boolean = false,
        val isCreatingSession: Boolean = false,
    )

    // ==================== 意图相关 ====================

    /**
     * 用户意图
     */
    internal sealed class Intent : AppIntent {
        // === 核心消息功能 ===
        data class SendMessage(
            val content: String,
        ) : Intent()

        data class UpdateInput(
            val text: String,
        ) : Intent()

        // 🔥 【架构清理】移除 UpdateStreamingMessage - Coach 不处理流式消息内容
        // 流式消息内容完全由 ThinkingBox 负责处理和管理

        // === 输入功能 ===
        data object StartVoiceInput : Intent()

        data object StopVoiceInput : Intent()

        data object FocusInput : Intent()

        data object BlurInput : Intent()

        data object ClearInput : Intent()

        data object ToggleToolPicker : Intent()

        data class OnVoiceTranscript(
            val partialText: String,
        ) : Intent()

        data object LoadInitialData : Intent()

        data object StartNewChat : Intent()

        // === 🔥 【架构简化】移除重复的流式响应处理，ThinkingBox负责全部AI token处理 ===
        // ProcessStreamEvent Intent已废弃，ThinkingBox直接处理所有StreamEvent

        data object ResetStreamingState : Intent()

        // 🔥 【AI对话重置修复】标记待重置状态
        data class MarkPendingReset(
            val messageId: String,
        ) : Intent()

        // === 🔥 【独立SummaryCard设计】SummaryCard管理 ===
        data class ShowSummaryCard(
            val messageId: String,
        ) : Intent()

        data object HideSummaryCard : Intent()

        // === 会话管理 ===
        data object LoadInitialSession : Intent()

        data class CreateSession(
            val title: String,
        ) : Intent()

        data object CreateNewSession : Intent()

        data class LoadSession(
            val sessionId: String,
        ) : Intent()

        data object LoadSessionHistory : Intent()

        data class SwitchSession(
            val sessionId: String,
        ) : Intent()

        data object LoadAllSessions : Intent()

        data object ClearCurrentSession : Intent()

        // 🔥 【历史导航修复】更新加载状态
        data class UpdateLoadingState(
            val isLoading: Boolean,
        ) : Intent()

        // 🔥 【消息迁移修复】刷新历史消息流
        data class RefreshHistoryFlow(
            val sessionId: String,
        ) : Intent()

        // === 搜索功能 ===
        data class SearchMessages(
            val query: String,
            val sessionId: String? = null,
        ) : Intent()

        data object ClearSearchResults : Intent()

        // === 快速操作 ===
        data object LoadQuickActionCategories : Intent()

        data class LoadQuickActionById(
            val actionId: String,
        ) : Intent()

        data object LoadQuickActions : Intent()

        // === 导航和UI ===
        data class PrefillInputAndNavigate(
            val prefillText: String,
        ) : Intent()

        data class GenerateSummary(
            val sessionId: String,
        ) : Intent()

        data class ShowError(
            val error: String,
        ) : Intent()

        // === 消息保存 ===
        // 🔥 【P0修复】简化AI消息保存Intent，移除thinkingNodes字段
        data class SaveAiMessage(
            val sessionId: String,
            val aiResponseId: String,
            val content: String,
            val inReplyToMessageId: String = "",
            // 🔥 【P0修复】仅保留最终Markdown，移除thinkingNodes
            val finalMarkdown: String? = null,
            // 🔥 元数据字段
            val tokensSnapshot: String = "",
            val lastFunctionCall: String? = null,
            val lastMcp: String? = null,
        ) : Intent()

        // 🔥 【单一数据源修复】移除重复的保存Intent，统一使用SaveAiMessage
        data class SaveUserMessage(
            val sessionId: String,
            val userMessageId: String,
            val content: String,
        ) : Intent()

        // === 保存状态更新 ===
        data class UpdateMessageSaveStatus(
            val messageId: String,
            val saveStatus: SaveStatus,
        ) : Intent()

        // === Function Call处理 ===
        data class DetectFunctionCall(
            val messageId: String,
            val content: String,
        ) : Intent()

        data class ProcessTemplateGeneration(
            val messageId: String,
            val functionCallJson: String,
        ) : Intent()

        data class ProcessPlanGeneration(
            val messageId: String,
            val functionCallJson: String,
        ) : Intent()

        // === 配置切换 ===
        data class SwitchPromptMode(
            val mode: String,
        ) : Intent()

        data class SwitchApiProvider(
            val provider: ApiProvider,
        ) : Intent()

        // === 网络状态 ===
        data class NetworkEventReceived(
            val event: com.example.gymbro.core.network.monitor.NetworkEvent,
        ) : Intent()

        // 🔥 【P0修复】移除ThinkingBox相关Intent - Coach不再直接操作ThinkingBox状态

        // === 工具和输入增强 ===
        data object OnToolsClick : Intent()

        data class OnToolSelected(
            val functionCallName: String,
        ) : Intent()

        data object OnStopGenerationClick : Intent()

        data object OnImagePickerClick : Intent()

        data object ShowImagePicker : Intent()

        data object HideImagePicker : Intent()

        data class OnImagesSelected(
            val uris: List<Uri>,
        ) : Intent()

        data class OnImageRemoved(
            val uri: Uri,
        ) : Intent()

        data class OnRemoveImageClick(
            val imageUrl: String,
        ) : Intent()

        // === 建议标签交互 ===
        data object HideSuggestionChips : Intent()

        // === 附件功能 ===
        data class AttachFiles(
            val uris: List<Uri>,
        ) : Intent()

        data class RemoveAttachment(
            val index: Int,
        ) : Intent()

        data class SelectImages(
            val uris: List<Uri>,
        ) : Intent()

        data class RemoveImage(
            val uri: Uri,
        ) : Intent()

        data class UpdateUploadProgress(
            val attachmentIndex: Int,
            val progress: Float,
        ) : Intent()

        // === Function Call完成 ===
        data class FunctionCallCompleted(
            val result: FunctionCallResult,
        ) : Intent()

        // 🔥 新增：关闭Function Call结果弹窗的意图
        data object DismissFunctionCallResult : Intent()

        // 🔥 新增：用于调试的Intent，直接触发一个模拟的Function Call结果
        data class DebugTriggerFunctionCallResult(val result: FunctionCallResult) : Intent()

        // 🔥 新增：执行真实的Function Call调试测试
        // 用于触发真实的数据库写入操作，而非仅UI展示
        data class DebugExecuteRealFunctionCall(
            val functionName: String,
        ) : Intent()

        // === 🔥 导航相关Intent ===
        data object NavigateToWorkout : Intent()
        data object NavigateToTemplates : Intent()
        data object NavigateToPlans : Intent()
        data object NavigateToExerciseLibrary : Intent()

        data class FunctionCallDetected(
            val messageId: String,
            val functionCall: String,
        ) : Intent()

        data class TemplateGenerationRequested(
            val messageId: String,
            val parameters: String,
        ) : Intent()

        data class PlanGenerationRequested(
            val messageId: String,
            val parameters: String,
        ) : Intent()

        data class LoadActionContext(
            val actionId: String,
        ) : Intent()

        data object ToggleQuickActionPanel : Intent()

        // === Panel控制 ===
        data class OnCardClick(
            val cardId: String,
        ) : Intent()

        data object DismissPanel : Intent()

        data class PreFillInput(
            val text: String,
        ) : Intent()

        data class SetPanelState(
            val state: PanelState,
        ) : Intent()

        // === 通用错误处理 ===
        data class OperationFailed(
            val errorCode: ErrorCode,
        ) : Intent()

        // === Session加载结果 ===
        data class SessionWithMessagesLoaded(
            val session: ChatSession,
            val messages: ImmutableList<MessageUi>,
        ) : Intent()

        // === Profile相关 ===
        data class CheckProfileCompletion(
            val userProfile: String,
        ) : Intent()

        data object DismissProfilePrompt : Intent()

        data object NavigateToProfile : Intent()

        // === Memory相关 ===
        data class RecallMemoriesFromService(
            val userId: String,
            val query: String,
            val tokenBudget: Int = 600,
        ) : Intent()

        // === 历史记录相关 ===
        data object ToggleHistoryPanel : Intent()

        data object LoadConversationHistory : Intent()

        data class SearchConversationHistory(
            val query: String,
        ) : Intent()

        data class SelectHistoryConversation(
            val conversationId: String,
        ) : Intent()

        data class DeleteHistoryConversation(
            val conversationId: String,
            val newTitle: String,
        ) : Intent()

        data class RenameHistoryConversation(
            val conversationId: String,
            val newTitle: String,
        ) : Intent()

        data object LoadMoreConversations : Intent()

        data object RefreshConversationHistory : Intent()

        // === 🔥 History RAG 相关 Intent ===
        data class HistoryContextFetched(
            val sessionId: String,
            val context: List<HistoryContextItem>,
            val success: Boolean,
            val error: String?,
        ) : Intent()

        data class HistoryContextCleared(
            val success: Boolean,
            val message: String,
        ) : Intent()

        // === 🔥 导航相关 Intent ===
        data class NavigationCompleted(
            val destination: String,
            val success: Boolean,
        ) : Intent()

        // === 🔥 新增：结果型 Intent，用于从 EffectHandler 更新状态 ===
        // 遵循 MVI 模式，将异步操作结果包装成 Intent 发送给 Reducer

        data class SessionCreatedResult(
            val session: ChatSession,
        ) : Intent()

        data class SessionsLoadedResult(
            val sessions: ImmutableList<ChatSession>,
        ) : Intent()

        data class SessionWithMessagesLoadedResult(
            val session: ChatSession,
            val messages: ImmutableList<MessageUi>,
        ) : Intent()

        data class MessageSaveCompletedResult(
            val messageId: String,
            val success: Boolean,
        ) : Intent()

        data class SearchResultsLoadedResult(
            val query: String,
            val results: ImmutableList<MessageUi>,
            val sessionId: String?,
        ) : Intent()

        data class SearchFailedResult(
            val query: String,
            val error: String,
        ) : Intent()

        data class QuickActionCategoriesLoadedResult(
            val categories: ImmutableList<QuickActionCategoryGroup>,
        ) : Intent()

        data class ActionContextLoadedResult(
            val action: com.example.gymbro.domain.shared.common.model.QuickAction,
        ) : Intent()

        data class SuggestionConfigLoadedResult(
            val config: SuggestionConfig,
        ) : Intent()

        data class SuggestionConfigFailedResult(
            val error: Throwable,
        ) : Intent()

        data class MemoryRecalledResult(
            val memories: List<String>,
        ) : Intent()

        data class MemoryErrorResult(
            val error: String,
        ) : Intent()

        data class MemorySavedResult(
            val memoryId: String,
        ) : Intent()

        data class FunctionCallProcessedResult(
            val messageId: String,
            val result: FunctionCallResult,
        ) : Intent()

        // 🔥 新增：加载中的 Intent，用于在 EffectHandler 开始异步操作时通知 UI
        object LoadingInitialSession : Intent()

        data class LoadingSession(
            val sessionId: String,
        ) : Intent()

        object LoadingSessionHistory : Intent()

        object LoadingAllSessions : Intent()

        data class LoadingSearch(val query: String) : Intent()

        object LoadingQuickActions : Intent()

        object LoadingSuggestionConfig : Intent()
    }

    // ==================== 效果相关 ====================

    /**
     * 副作用
     */
    sealed interface Effect : UiEffect {
        // === 核心功能 ===
        data class StartAiStream(
            val prompt: String,
            val sessionId: String,
            val userMessageId: String,
            val aiResponseId: String,
        ) : Effect

        // === 会话管理 ===
        data object LoadInitialSession : Effect

        data class CreateSession(
            val title: String,
        ) : Effect

        data object CreateNewSession : Effect

        data class LoadSession(
            val sessionId: String,
        ) : Effect

        data object LoadSessionHistory : Effect

        data class SwitchSession(
            val sessionId: String,
        ) : Effect

        data object LoadAllSessions : Effect

        // 🔥 【修复】添加历史消息流设置Effect
        data class SetupHistoryFlow(
            val sessionId: String,
        ) : Effect

        // === 搜索 ===
        data class PerformSearch(
            val query: String,
            val sessionId: String? = null,
        ) : Effect

        data class SearchMessages(
            val query: String,
            val sessionId: String? = null,
        ) : Effect

        // === 快速操作 ===
        data object LoadQuickActionCategories : Effect

        data class LoadQuickActionById(
            val actionId: String,
        ) : Effect

        data object LoadQuickActions : Effect

        // === 导航和UI ===
        data class PrefillInputAndNavigate(
            val prefillText: String,
        ) : Effect

        data class GenerateSummary(
            val sessionId: String,
        ) : Effect

        data class ShowError(
            val error: String,
        ) : Effect

        // === Function Call 消息构建 ===
        data class BuildFunctionCallMessage(
            val functionCallName: String,
        ) : Effect

        // === 消息保存 ===
        data class SaveAiMessage(
            val sessionId: String,
            val aiResponseId: String,
            val content: String,
            val inReplyToMessageId: String = "", // 🔥 数据流统一：设为可选参数
            // 🔥 数据流统一：新增持久化相关字段
            val tokensSnapshot: String = "",
            val lastFunctionCall: String? = null,
            val lastMcp: String? = null,
            // 🔥 【思考内容保存修复】新增思考内容字段
            val finalMarkdown: String? = null,
            val thinkingNodes: String? = null,
        ) : Effect

        data class SaveUserMessage(
            val sessionId: String,
            val userMessageId: String,
            val content: String,
        ) : Effect

        // 🔥 【消息迁移修复】新增消息迁移Effect
        data class MigrateMessagesToSession(
            val fromSessionId: String,
            val toSessionId: String,
            val messages: ImmutableList<MessageUi>,
        ) : Effect

        // 🔥 【单一数据源修复】移除重复的HistoryPersister Effect
        // 统一使用ChatRaw架构的SaveAiMessage Effect

        // === Function Call处理 ===
        data class DetectFunctionCall(
            val messageId: String,
            val content: String,
        ) : Effect

        data class ProcessTemplateGeneration(
            val messageId: String,
            val functionCallJson: String,
        ) : Effect

        data class ProcessPlanGeneration(
            val messageId: String,
            val functionCallJson: String,
        ) : Effect

        data class ExecuteFunctionCall(
            val functionCallName: String,
            val arguments: String,
            val sessionId: String,
        ) : Effect

        data class FunctionCallCompleted(
            val messageId: String,
            val result: FunctionCallResult,
        ) : Effect

        // === 配置切换 ===
        data class SwitchPromptMode(
            val mode: String,
        ) : Effect

        data class SwitchApiProvider(
            val provider: ApiProvider,
        ) : Effect

        // === 🔥 导航相关Effect ===
        data object NavigateToWorkout : Effect
        data object NavigateToTemplates : Effect
        data object NavigateToPlans : Effect
        data object NavigateToExerciseLibrary : Effect

        // === Profile相关 ===
        data object NavigateToProfilePage : Effect

        // === History RAG ===
        data class FetchHistoryContext(
            val sessionId: String?,
        ) : Effect

        data object ClearHistoryRagContext : Effect

        // === Memory系统 ===
        data class RecallMemoriesFromService(
            val userId: String,
            val query: String,
            val tokenBudget: Int = 600,
        ) : Effect

        data class SaveMemoryToService(
            val userId: String,
            val content: String,
            val tier: com.example.gymbro.shared.models.memory.MemoryTier,
            val importance: Int,
        ) : Effect
    }

    // ==================== 数据模型相关 ====================

    /**
     * 附件数据类
     */
    @Immutable
    data class Attachment(
        val uri: Uri,
        val type: AttachmentType,
        val name: String,
        val size: Long,
        val uploadProgress: Float? = null,
    )

    /**
     * 附件类型
     */
    enum class AttachmentType {
        IMAGE,
        FILE,
    }

    /**
     * 视图模式
     */
    enum class ViewMode {
        STREAMING,
        FINAL,
    }

    /**
     * 最终答案包装类
     */
    @Immutable
    data class FinalAnswerBundle(
        val markdown: String,
        val meta: Map<String, Any> = emptyMap(),
    )

    /**
     * API提供商
     */
    enum class ApiProvider(
        val id: String,
    ) {
        DEEPSEEK("deepseek"),
        GOOGLE_GEMINI("google_gemini"),
        OPENAI("openai"),
    }

    /**
     * API提供商状态
     */
    enum class ApiProviderStatus {
        READY,
        BUSY,
        ERROR,
        OFFLINE,
    }

    /**
     * 错误代码
     */
    enum class ErrorCode {
        NETWORK_ERROR,
        AI_SERVICE_UNAVAILABLE,
        INVALID_INPUT,
        SESSION_LOAD_FAILED,
        SESSION_CREATION_FAILED, // 🔥 新增：会话创建失败
        MESSAGE_SEND_FAILED,
        UNKNOWN_ERROR,
    }

    /**
     * Panel状态机
     */
    enum class PanelState {
        COLLAPSED,
        EXPANDED,
        FOCUSED,
    }

    /**
     * 发送按钮动画状态
     */
    enum class SendAnimState {
        IDLE,
        SENDING,
        SENT,
    }

    /**
     * 发送按钮状态
     */
    enum class SendButtonState {
        VOICE,
        SEND,
        STOP,
    }

    // ==================== Contract扩展函数区域 ====================
    // 注意：数据转换逻辑已迁移至AiCoachMapper，避免Contract层承担转换职责
}

// ===== Contract扩展函数 =====

/**
 * ApiProvider获取显示名称
 *
 * 统一API提供商的显示名称逻辑，避免在多个文件中重复定义
 */
internal fun AiCoachContract.ApiProvider.getDisplayName(): String =
    when (this) {
        AiCoachContract.ApiProvider.DEEPSEEK -> "DeepSeek"
        AiCoachContract.ApiProvider.GOOGLE_GEMINI -> "Gemini"
        AiCoachContract.ApiProvider.OPENAI -> "OpenAI"
    }

/**
 * ErrorCode转换为UiText
 *
 * 🔥 【国际化重构】使用StringResource替代硬编码字符串
 */
internal fun AiCoachContract.ErrorCode.toUiText(): com.example.gymbro.core.ui.text.UiText =
    when (this) {
        AiCoachContract.ErrorCode.NETWORK_ERROR ->
            com.example.gymbro.core.ui.text.UiText.StringResource(
                com.example.gymbro.designSystem.R.string.ai_coach_error_code_network_error,
                emptyList(),
            )

        AiCoachContract.ErrorCode.AI_SERVICE_UNAVAILABLE ->
            com.example.gymbro.core.ui.text.UiText.StringResource(
                com.example.gymbro.designSystem.R.string.ai_coach_error_code_ai_service_unavailable,
                emptyList(),
            )

        AiCoachContract.ErrorCode.INVALID_INPUT ->
            com.example.gymbro.core.ui.text.UiText.StringResource(
                com.example.gymbro.designSystem.R.string.ai_coach_error_code_invalid_input,
                emptyList(),
            )

        AiCoachContract.ErrorCode.SESSION_LOAD_FAILED ->
            com.example.gymbro.core.ui.text.UiText.StringResource(
                com.example.gymbro.designSystem.R.string.ai_coach_error_code_session_load_failed,
                emptyList(),
            )

        AiCoachContract.ErrorCode.SESSION_CREATION_FAILED ->
            com.example.gymbro.core.ui.text.UiText.StringResource(
                com.example.gymbro.designSystem.R.string.ai_coach_error_code_session_creation_failed,
                emptyList(),
            )

        AiCoachContract.ErrorCode.MESSAGE_SEND_FAILED ->
            com.example.gymbro.core.ui.text.UiText.StringResource(
                com.example.gymbro.designSystem.R.string.ai_coach_error_code_message_send_failed,
                emptyList(),
            )

        AiCoachContract.ErrorCode.UNKNOWN_ERROR ->
            com.example.gymbro.core.ui.text.UiText.StringResource(
                com.example.gymbro.designSystem.R.string.ai_coach_error_code_unknown_error,
                emptyList(),
            )
    }

/**
 * 🔥 【独立SummaryCard设计】SummaryCard状态管理
 *
 * 支持多轮对话的思考内容展示
 */
@Immutable
data class SummaryCardState(
    val isVisible: Boolean = false,
    val messageId: String? = null, // 对应的消息ID，支持多轮对话
    val thinkingContent: com.example.gymbro.features.thinkingbox.domain.interfaces.UiState? = null, // 对应的思考内容
)
