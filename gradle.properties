# Project-wide Gradle settings.
android.nonFinalResIds=false
# JVM Memory Optimization
org.gradle.jvmargs=-Xmx6144m -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:MaxMetaspaceSize=1024m
org.gradle.parallel=true
android.useAndroidX=true
kotlin.code.style=official
android.nonTransitiveRClass=true
org.gradle.configuration-cache=true
org.gradle.configureondemand=true
android.suppressUnsupportedCompileSdk=34
# Build Speed Optimization
ksp.incremental=true
ksp.incremental.intermodule=true
org.gradle.daemon=true
org.gradle.caching=true
org.gradle.workers.max=8

# APK Size Optimization
android.enableR8.fullMode=true
android.uniquePackageNames=true

# APK Optimization Control (only enable when building APK)
# Set to true when building release APK: ./gradlew assembleRelease -PenableApkOptimization=true
enableApkOptimization=false

