package com.example.gymbro.features.thinkingbox

import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.domain.parser.XmlStreamScanner
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingReducer
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Test

/**
 * Title 标签处理过程验证测试
 *
 * 🔥 验证从 token 解析到 UI 状态的完整流程：
 * Token → XmlStreamScanner → StreamingThinkingMLParser → SemanticEvent → DomainMapper → ThinkingEvent → ThinkingReducer → UiState
 */
class TitleTagProcessingTest {

    @Test
    fun `验证 title 标签的完整处理流程`() = runTest {
        // 🔥 【步骤1】准备测试数据 - 包含 title 标签的完整 phase
        val testInput = "<phase id=\"analysis\"><title>数据分析阶段</title>正在分析用户输入的数据...</phase>"

        // 🔥 【步骤2】Token 解析 - XmlStreamScanner
        val xmlScanner = XmlStreamScanner()
        val tokens = xmlScanner.feed(testInput)

        println("🔍 [Token解析] 解析出的tokens:")
        tokens.forEachIndexed { index, token ->
            println("  $index: ${token::class.simpleName} - $token")
        }

        // 验证 token 解析结果
        assertTrue("应该解析出多个tokens", tokens.size >= 5)

        val tagOpenTokens = tokens.filterIsInstance<XmlStreamScanner.TagOpen>()
        val textTokens = tokens.filterIsInstance<XmlStreamScanner.Text>()
        val tagCloseTokens = tokens.filterIsInstance<XmlStreamScanner.TagClose>()

        // 验证 phase 标签
        val phaseOpenToken = tagOpenTokens.find { it.name == "phase" }
        assertNotNull("应该有 phase 开始标签", phaseOpenToken)
        assertEquals("analysis", phaseOpenToken?.attributes?.get("id"))

        // 验证 title 标签
        val titleOpenToken = tagOpenTokens.find { it.name == "title" }
        assertNotNull("应该有 title 开始标签", titleOpenToken)

        val titleCloseToken = tagCloseTokens.find { it.name == "title" }
        assertNotNull("应该有 title 结束标签", titleCloseToken)

        // 验证文本内容
        val titleText = textTokens.find { it.content.contains("数据分析阶段") }
        assertNotNull("应该有 title 文本内容", titleText)

        val phaseContent = textTokens.find { it.content.contains("正在分析用户输入") }
        assertNotNull("应该有 phase 内容", phaseContent)

        println("✅ [Token解析] 验证通过")
    }

    @Test
    fun `验证 StreamingThinkingMLParser 的 SemanticEvent 生成`() = runTest {
        // 🔥 【步骤3】SemanticEvent 生成 - StreamingThinkingMLParser
        val parser = StreamingThinkingMLParser(
            xmlScanner = XmlStreamScanner(),
            functionCallDetector = null, // 测试中不需要 function call 检测
        )
        val semanticEvents = mutableListOf<SemanticEvent>()

        val testInput = "<phase id=\"analysis\"><title>数据分析阶段</title>正在分析数据</phase>"

        parser.parse(
            tokens = flowOf(testInput),
            messageId = "test-msg",
        ) { event ->
            semanticEvents.add(event)
        }

        println("🔍 [SemanticEvent生成] 生成的事件:")
        semanticEvents.forEachIndexed { index, event ->
            println("  $index: ${event::class.simpleName} - $event")
        }

        // 验证事件序列
        assertTrue("应该生成多个事件", semanticEvents.size >= 4)

        // 验证 PhaseStart 事件
        val phaseStartEvent = semanticEvents.filterIsInstance<SemanticEvent.PhaseStart>()
            .find { it.id == "analysis" }
        assertNotNull("应该有 PhaseStart 事件", phaseStartEvent)
        assertEquals("analysis", phaseStartEvent?.id)

        // 验证 TagOpened("title") 事件
        val titleOpenEvent = semanticEvents.filterIsInstance<SemanticEvent.TagOpened>()
            .find { it.name == "title" }
        assertNotNull("应该有 TagOpened(title) 事件", titleOpenEvent)

        // 验证 TextChunk 事件（title 内容）
        val titleTextEvent = semanticEvents.filterIsInstance<SemanticEvent.TextChunk>()
            .find { it.text.contains("数据分析阶段") }
        assertNotNull("应该有 title 文本事件", titleTextEvent)

        // 验证 TagClosed("title") 事件
        val titleCloseEvent = semanticEvents.filterIsInstance<SemanticEvent.TagClosed>()
            .find { it.name == "title" }
        assertNotNull("应该有 TagClosed(title) 事件", titleCloseEvent)

        // 验证 PhaseContent 事件
        val phaseContentEvent = semanticEvents.filterIsInstance<SemanticEvent.PhaseContent>()
            .find { it.content.contains("正在分析数据") }
        assertNotNull("应该有 PhaseContent 事件", phaseContentEvent)
        assertEquals("analysis", phaseContentEvent?.id)

        // 验证 PhaseEnd 事件
        val phaseEndEvent = semanticEvents.filterIsInstance<SemanticEvent.PhaseEnd>()
            .find { it.id == "analysis" }
        assertNotNull("应该有 PhaseEnd 事件", phaseEndEvent)

        println("✅ [SemanticEvent生成] 验证通过")
    }

    @Test
    fun `验证 DomainMapper 的 ThinkingEvent 映射`() {
        // 🔥 【步骤4】ThinkingEvent 映射 - DomainMapper
        val mapper = DomainMapper()
        val thinkingEvents = mutableListOf<ThinkingEvent>()

        // 模拟 SemanticEvent 序列
        val semanticEvents = listOf(
            SemanticEvent.PhaseStart("analysis", null),
            SemanticEvent.TagOpened("title", emptyMap()),
            SemanticEvent.TextChunk("数据分析阶段"),
            SemanticEvent.TagClosed("title"),
            SemanticEvent.PhaseContent("analysis", "正在分析数据"),
            SemanticEvent.PhaseEnd("analysis"),
        )

        var mappingContext = DomainMapper.MappingContext()

        semanticEvents.forEach { semanticEvent ->
            val result = mapper.map(semanticEvent, mappingContext)
            mappingContext = result.context
            thinkingEvents.addAll(result.events)
        }

        println("🔍 [ThinkingEvent映射] 映射的事件:")
        thinkingEvents.forEachIndexed { index, event ->
            println("  $index: ${event::class.simpleName} - $event")
        }

        // 验证映射结果
        assertTrue("应该映射出多个事件", thinkingEvents.size >= 3)

        // 验证 PhaseStart 事件
        val phaseStartEvent = thinkingEvents.filterIsInstance<ThinkingEvent.PhaseStart>()
            .find { it.id == "analysis" }
        assertNotNull("应该有 PhaseStart 事件", phaseStartEvent)
        assertEquals("analysis", phaseStartEvent?.id)
        assertNull("初始 title 应该为 null", phaseStartEvent?.title)

        // 验证 PhaseTitleUpdate 事件
        val titleUpdateEvent = thinkingEvents.filterIsInstance<ThinkingEvent.PhaseTitleUpdate>()
            .find { it.id == "analysis" }
        assertNotNull("应该有 PhaseTitleUpdate 事件", titleUpdateEvent)
        assertEquals("analysis", titleUpdateEvent?.id)
        assertEquals("数据分析阶段", titleUpdateEvent?.title)

        // 验证 PhaseContent 事件
        val phaseContentEvent = thinkingEvents.filterIsInstance<ThinkingEvent.PhaseContent>()
            .find { it.id == "analysis" }
        assertNotNull("应该有 PhaseContent 事件", phaseContentEvent)
        assertEquals("analysis", phaseContentEvent?.id)
        assertEquals("正在分析数据", phaseContentEvent?.content)

        // 验证 PhaseEnd 事件
        val phaseEndEvent = thinkingEvents.filterIsInstance<ThinkingEvent.PhaseEnd>()
            .find { it.id == "analysis" }
        assertNotNull("应该有 PhaseEnd 事件", phaseEndEvent)

        println("✅ [ThinkingEvent映射] 验证通过")
    }

    @Test
    fun `验证 ThinkingReducer 的状态更新`() {
        // 🔥 【步骤5】状态更新 - ThinkingReducer
        val thinkingEvents = listOf(
            ThinkingEvent.PhaseStart("analysis", null),
            ThinkingEvent.PhaseTitleUpdate("analysis", "数据分析阶段"),
            ThinkingEvent.PhaseContent("analysis", "正在分析数据"),
            ThinkingEvent.PhaseEnd("analysis"),
        )

        var uiState = ThinkingReducer.ThinkingUiState()

        println("🔍 [状态更新] 初始状态: phases=${uiState.phases.keys}")

        thinkingEvents.forEach { event ->
            uiState = ThinkingReducer.reduce(uiState, event)
            println(
                "  处理事件 ${event::class.simpleName}: phases=${uiState.phases.keys}, activePhaseId=${uiState.activePhaseId}",
            )
        }

        // 验证最终状态
        assertTrue("应该有 analysis phase", uiState.phases.containsKey("analysis"))

        val analysisPhase = uiState.phases["analysis"]
        assertNotNull("analysis phase 不应该为 null", analysisPhase)
        assertEquals("analysis", analysisPhase?.id)
        assertEquals("数据分析阶段", analysisPhase?.title)
        assertEquals("正在分析数据", analysisPhase?.content)
        assertTrue("phase 应该标记为完成", analysisPhase?.isComplete == true)

        println("✅ [状态更新] 验证通过")
        println("🎉 Title 标签完整处理流程验证成功！")
    }

    @Test
    fun `双时序修复验证 - final数据处理完成标志`() {
        // Given: 初始状态
        val initialState = ThinkingReducer.ThinkingUiState()

        // When: 处理FinalStart事件
        val stateAfterStart = ThinkingReducer.reduce(ThinkingEvent.FinalStart, initialState)

        // Then: 验证final数据处理标志被重置
        assertThat(stateAfterStart.finalContentArrived).isTrue()
        assertThat(stateAfterStart.finalDataProcessingComplete).isFalse() // 🔥 新增验证
        assertThat(stateAfterStart.isFinalStreaming).isTrue()

        // When: 处理FinalEnd事件
        val stateAfterEnd = ThinkingReducer.reduce(ThinkingEvent.FinalEnd, stateAfterStart)

        // Then: 验证final数据处理完成
        assertThat(stateAfterEnd.finalDataProcessingComplete).isTrue() // 🔥 关键验证
        assertThat(stateAfterEnd.isFinalStreaming).isFalse()

        println("✅ [双时序修复] Final数据处理完成标志验证通过")
    }

    @Test
    fun `DomainMapper生成FinalEnd事件验证`() {
        // Given: DomainMapper和初始上下文
        val mapper = DomainMapper()
        val context = DomainMapper.MappingContext()

        // When: 处理</final>标签关闭事件
        val finalCloseEvent = SemanticEvent.TagClosed("final")
        val result = mapper.map(finalCloseEvent, context)

        // Then: 验证生成FinalEnd事件
        assertThat(result.events).hasSize(1)
        assertThat(result.events.first()).isInstanceOf(ThinkingEvent.FinalEnd::class.java)
        assertThat(result.context.inFinalTag).isFalse()

        println("✅ [DomainMapper] FinalEnd事件生成验证通过")
    }
}
