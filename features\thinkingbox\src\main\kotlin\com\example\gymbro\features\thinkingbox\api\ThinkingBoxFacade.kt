package com.example.gymbro.features.thinkingbox.api

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBox Facade实现
 *
 * 🔥 【Hilt修复】使用@Singleton服务而不是直接注入@HiltViewModel
 * 避免违反Hilt的ViewModel注入规则
 */
@Singleton
class ThinkingBoxFacade @Inject constructor(
    private val thinkingBoxService: ThinkingBoxService
) : ThinkingBoxApi {

    private var onMessageCompleteCallback: OnMessageComplete? = null
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    init {
        // 订阅消息完成事件
        thinkingBoxService.messageCompletionEvents
            .onEach { event ->
                onMessageCompleteCallback?.invoke(event.messageId, event.finalMarkdown)
            }
            .launchIn(scope)
    }

    // 🔥 【Hilt修复】通过服务处理跨模块通信
    override fun startStream(request: StartStream) {
        scope.launch {
            thinkingBoxService.startStream(request)
        }
    }

    override fun setOnMessageComplete(callback: OnMessageComplete) {
        this.onMessageCompleteCallback = callback
    }

    override fun reset() {
        scope.launch {
            thinkingBoxService.reset()
        }
    }
}
