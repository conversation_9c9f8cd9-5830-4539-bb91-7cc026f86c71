package com.example.gymbro.features.thinkingbox.domain.parser

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * XmlStreamScanner - 循环缓冲XML扫描器 (v3.0 - 符合双时序baseline文档)
 *
 * 🔥 【文档规范修复】实现Context-Aware Streaming Pipeline要求的循环缓冲tokenizer：
 * - 循环缓冲机制：固定大小缓冲区，避免内存无限增长
 * - 跨chunk标签识别：<pha + se id="1"> 完整解析
 * - Look-ahead策略：避免"残缺tag当纯文本"的问题
 * - 属性跨行支持：id="1"\n title="分析"
 * - 内存安全：自动清理过期缓冲内容
 */
@Singleton
class XmlStreamScanner @Inject constructor() {

    // 🔥 【循环缓冲修复】使用固定大小的循环缓冲区，符合文档要求
    private companion object {
        const val BUFFER_SIZE = 8192 // 8KB循环缓冲区
        const val MAX_TAG_SIZE = 1024 // 单个标签最大1KB
    }

    private val circularBuffer = CharArray(BUFFER_SIZE)
    private var writePos = 0
    private var readPos = 0
    private var bufferLength = 0
    private val TAG = "TB-XML-SCANNER"

    /**
     * Token类型定义
     */
    sealed interface Token

    data class TagOpen(
        val name: String,
        val attributes: Map<String, String> = emptyMap(),
        val isSelfClosing: Boolean = false,
    ) : Token

    data class TagClose(val name: String) : Token

    data class Text(val content: String) : Token

    /**
     * 🔥 【循环缓冲修复】核心方法：循环缓冲流式处理 (v3.0 - 符合双时序baseline文档)
     *
     * 循环缓冲+Context-Aware策略：
     * - 固定大小循环缓冲区，避免内存无限增长
     * - 跨chunk标签完整识别：<pha + se id="1">
     * - 内存安全：自动清理过期内容
     * - 性能优化：避免频繁的字符串拷贝
     */
    fun feed(str: String): List<Token> {
        // 🔥 【XML扫描器调试】记录输入内容
        if (str.contains("<") || str.contains(">")) {
            Timber.tag("TB-XML-INPUT").e("🔍 [XML扫描器输入] 长度=${str.length}: '$str'")
        }

        // 🔥 【循环缓冲修复】将输入写入循环缓冲区
        writeToCircularBuffer(str)
        val tokens = mutableListOf<Token>()

        // 🔥 【缓冲区状态】记录缓冲区当前状态
        if (str.contains("<") || str.contains(">")) {
            Timber.tag(
                "TB-XML-BUFFER",
            ).e("🔍 [缓冲区状态] 长度=$bufferLength, 内容='${getBufferContent().take(100)}'")
        }

        loop@ while (bufferLength > 0) {
            val ltIndex = findInBuffer('<')
            when (ltIndex) {
                -1 -> {
                    // 无tag起点 → 全当文本
                    if (bufferLength > 0) {
                        val textContent = readFromBuffer(bufferLength)
                        if (textContent.isNotEmpty()) {
                            tokens.add(Text(textContent))
                        }
                    }
                    break@loop
                }
                else -> {
                    if (ltIndex > 0) {
                        // 先输出ltIndex前的纯文本
                        val textContent = readFromBuffer(ltIndex)
                        if (textContent.isNotEmpty()) {
                            tokens.add(Text(textContent))
                        }
                    }

                    // 现在缓冲区开头是'<'，查找对应的'>'
                    val gtIndex = findInBuffer('>', startOffset = 1)
                    if (gtIndex == -1) {
                        // tag未闭合 → 等下个chunk
                        // 🔥 【循环缓冲修复】确保跨chunk标签等待完整
                        val partialTag = getBufferContent().take(20)
                        Timber.tag("TB-XML-SCANNER").v("🔍 [不完整标签] 等待下个chunk: '$partialTag'")
                        break@loop
                    }

                    // 提取完整标签内容（跳过'<'和'>'）
                    skipFromBuffer(1) // 跳过'<'
                    val tagContent = readFromBuffer(gtIndex - 1)
                    skipFromBuffer(1) // 跳过'>'

                    val token = parseTag(tagContent)
                    if (token != null) {
                        tokens.add(token)
                    }
                }
            }
        }

        // 🔥 【Token输出调试】记录所有生成的tokens
        if (tokens.isNotEmpty()) {
            Timber.tag("TB-XML-OUTPUT").e("🔍 [Token输出] 生成${tokens.size}个tokens:")
            tokens.forEachIndexed { index, token ->
                Timber.tag("TB-XML-OUTPUT").e("🔍 [Token输出] [$index] $token")
            }
        }

        Timber.tag(
            TAG,
        ).v("🔍 [循环缓冲修复] Feed处理: 输入${str.length}字符, 输出${tokens.size}个Token, 缓冲剩余${bufferLength}字符")

        return tokens
    }

    /**
     * 🔥 【循环缓冲修复】将输入字符串写入循环缓冲区
     *
     * 实现固定大小循环缓冲区，避免内存无限增长
     * 当缓冲区满时，自动覆盖最旧的数据
     */
    private fun writeToCircularBuffer(str: String) {
        for (char in str) {
            // 检查缓冲区是否已满
            if (bufferLength >= BUFFER_SIZE) {
                // 缓冲区满，移动读指针（丢弃最旧数据）
                readPos = (readPos + 1) % BUFFER_SIZE
                bufferLength--
                Timber.tag(TAG).w("🔄 [循环缓冲] 缓冲区满，丢弃最旧数据")
            }

            // 写入新字符
            circularBuffer[writePos] = char
            writePos = (writePos + 1) % BUFFER_SIZE
            bufferLength++
        }
    }

    /**
     * 🔥 【循环缓冲修复】在循环缓冲区中查找指定字符
     *
     * @param char 要查找的字符
     * @param startOffset 开始查找的偏移量（相对于读指针）
     * @return 字符位置（相对于读指针），未找到返回-1
     */
    private fun findInBuffer(char: Char, startOffset: Int = 0): Int {
        if (startOffset >= bufferLength) return -1

        for (i in startOffset until bufferLength) {
            val pos = (readPos + i) % BUFFER_SIZE
            if (circularBuffer[pos] == char) {
                return i
            }
        }
        return -1
    }

    /**
     * 🔥 【循环缓冲修复】从循环缓冲区读取指定长度的字符串
     *
     * @param length 要读取的字符数
     * @return 读取的字符串
     */
    private fun readFromBuffer(length: Int): String {
        if (length <= 0 || length > bufferLength) return ""

        val result = StringBuilder(length)
        for (i in 0 until length) {
            val pos = (readPos + i) % BUFFER_SIZE
            result.append(circularBuffer[pos])
        }

        // 移动读指针
        readPos = (readPos + length) % BUFFER_SIZE
        bufferLength -= length

        return result.toString()
    }

    /**
     * 🔥 【循环缓冲修复】跳过循环缓冲区中的指定字符数
     *
     * @param count 要跳过的字符数
     */
    private fun skipFromBuffer(count: Int) {
        val skipCount = minOf(count, bufferLength)
        readPos = (readPos + skipCount) % BUFFER_SIZE
        bufferLength -= skipCount
    }

    /**
     * 🔥 解析标签内容
     */
    private fun parseTag(tagContent: String): Token? {
        // 🔥 【标签解析调试】记录所有标签解析尝试
        Timber.tag("TB-XML-SCANNER").v("🔍 [标签解析] 尝试解析: '$tagContent'")

        if (tagContent.isEmpty()) {
            Timber.tag("TB-XML-SCANNER").w("🔍 [标签解析] 空标签内容，跳过")
            return null
        }

        val trimmed = tagContent.trim()
        Timber.tag("TB-XML-SCANNER").v("🔍 [标签解析] 清理后内容: '$trimmed'")

        // 处理闭合标签
        if (trimmed.startsWith("/")) {
            val tagName = trimmed.substring(1).trim()
            Timber.tag("TB-XML-SCANNER").v("🔍 [标签解析] ✅ 识别为关闭标签: '$tagName'")
            return TagClose(tagName)
        }

        // 处理自闭合标签
        val isSelfClosing = trimmed.endsWith("/")
        val contentToProcess = if (isSelfClosing) {
            trimmed.substring(0, trimmed.length - 1).trim()
        } else {
            trimmed
        }

        // 解析标签名和属性
        val parts = contentToProcess.split(" ", limit = 2)
        val tagName = parts[0]
        val attributes = if (parts.size > 1) {
            val attrString = parts[1]
            Timber.tag("TB-XML-SCANNER").v("🔍 [属性解析] 属性字符串: '$attrString'")
            val parsedAttrs = parseAttributes(attrString)
            Timber.tag("TB-XML-SCANNER").v("🔍 [属性解析] 解析结果: $parsedAttrs")
            parsedAttrs
        } else {
            emptyMap()
        }

        val result = TagOpen(tagName, attributes, isSelfClosing)
        Timber.tag(
            "TB-XML-PARSE",
        ).e("🔍 [标签解析] ✅ 识别为开放标签: name='$tagName', attrs=$attributes, selfClosing=$isSelfClosing")
        return result
    }

    /**
     * 🔥 解析属性 (v2.0 - 支持跨行属性)
     *
     * 支持属性跨行：id="1"\n title="分析"
     */
    private fun parseAttributes(attrString: String): Map<String, String> {
        val attributes = mutableMapOf<String, String>()

        // 🔥 跨行属性支持：使用DOT_MATCHES_ALL模式
        val regex = """(\w+)="(.*?)"""".toRegex(RegexOption.DOT_MATCHES_ALL)
        regex.findAll(attrString).forEach { match ->
            val key = match.groupValues[1]
            val value = match.groupValues[2]
            attributes[key] = value
        }

        return attributes
    }

    /**
     * 🔥 【循环缓冲修复】清理循环缓冲区
     *
     * 用于会话结束或错误恢复时清理缓冲区状态
     */
    fun clear() {
        readPos = 0
        writePos = 0
        bufferLength = 0
        Timber.tag(TAG).d("🧹 [循环缓冲] 缓冲区已清理")
    }

    /**
     * 🔥 【循环缓冲修复】获取当前缓冲区内容（调试用）
     */
    fun getBufferContent(): String {
        if (bufferLength == 0) return ""

        val result = StringBuilder(bufferLength)
        for (i in 0 until bufferLength) {
            val pos = (readPos + i) % BUFFER_SIZE
            result.append(circularBuffer[pos])
        }
        return result.toString()
    }

    /**
     * 🔥 【循环缓冲修复】检查是否有未完成的标签
     */
    fun hasIncompleteTag(): Boolean {
        val content = getBufferContent()
        return content.contains("<") && !content.contains(">")
    }

    /**
     * 🔥 【循环缓冲修复】获取缓冲区状态信息（调试用）
     */
    fun getBufferInfo(): String {
        return "CircularBuffer[size=$BUFFER_SIZE, length=$bufferLength, readPos=$readPos, writePos=$writePos]"
    }

    /**
     * 🔥 【循环缓冲修复】检查缓冲区是否接近满载
     *
     * 当缓冲区使用率超过80%时返回true，用于性能监控
     */
    fun isBufferNearFull(): Boolean {
        return bufferLength > (BUFFER_SIZE * 0.8).toInt()
    }
}
