package com.example.gymbro.features.workout.session.internal.effect

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.overlay.floating.FloatingCountdownService
import com.example.gymbro.domain.coach.usecase.AiCoachUseCase
import com.example.gymbro.domain.coach.usecase.ChatSessionManagementUseCase
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.domain.workout.service.SessionManager
import com.example.gymbro.domain.workout.usecase.PlanProgressUseCase
import com.example.gymbro.domain.workout.usecase.plan.GetPlanWithTemplateVersionsUseCase
import com.example.gymbro.domain.workout.usecase.session.ApplySessionChangesToTemplateUseCase
import com.example.gymbro.domain.workout.usecase.session.GetWorkoutSessionByIdUseCase
import com.example.gymbro.domain.workout.usecase.session.RecordExerciseSetUseCase
import com.example.gymbro.domain.workout.usecase.session.SaveWorkoutSessionUseCase
import com.example.gymbro.domain.workout.usecase.stats.CreateDailyStatsUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.features.workout.session.SessionContract.Effect
import com.example.gymbro.features.workout.session.SessionContract.Intent
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.datetime.toLocalDateTime
import timber.log.Timber
import javax.inject.Inject
import com.example.gymbro.domain.workout.model.session.WorkoutSession as SessionWorkoutSession

/**
 * Workout Session Effect Handler - 基于训练页面.md的MVI 2.0副作用处理器
 *
 * 核心职责：
 * 1. 处理所有Effect并产生相应的副作用
 * 2. 管理训练会话的数据持久化
 * 3. 处理倒计时管理和WorkManager集成
 * 4. 集成AI助手功能和Coach模块
 * 5. 处理状态恢复和滚动位置保存
 * 6. 管理系统级操作（沉浸模式、屏幕常亮等）
 */
internal class SessionEffectHandler
@Inject
constructor(
    private val getWorkoutSessionByIdUseCase: GetWorkoutSessionByIdUseCase,
    private val saveWorkoutSessionUseCase: SaveWorkoutSessionUseCase,
    private val recordExerciseSetUseCase: RecordExerciseSetUseCase,
    private val floatingCountdownService: FloatingCountdownService,
    private val applySessionChangesToTemplateUseCase: ApplySessionChangesToTemplateUseCase,
    private val templateManagementUseCase: TemplateManagementUseCase,
    private val templateRepository: TemplateRepository, // 新增：用于获取published templates
    private val planRepository: PlanRepository, // 新增：用于获取plan templates
    private val getPlanWithTemplateVersionsUseCase: GetPlanWithTemplateVersionsUseCase,
    private val aiCoachUseCase: AiCoachUseCase,
    private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
    private val sessionManager: SessionManager, // 🔥 新增：用于模板转换
    private val planProgressUseCase: PlanProgressUseCase, // 新增：用于Plan progress更新
    private val createDailyStatsUseCase: CreateDailyStatsUseCase, // 新增：用于创建日级统计
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {
    companion object {
        private const val TAG = "SessionEffectHandler"
        private const val COUNTDOWN_TICK_INTERVAL_MS = 1000L
    }

    // 倒计时管理
    private val activeCountdownJobs = mutableMapOf<String, Job>()

    // 结构化并发
    private lateinit var handlerScope: CoroutineScope
    private lateinit var sendIntent: (Intent) -> Unit

    /**
     * 初始化EffectHandler - MVI 2.0结构化并发标准
     */
    fun initialize(
        scope: CoroutineScope,
        intentSender: (Intent) -> Unit,
    ) {
        this.handlerScope = scope
        this.sendIntent = intentSender
        Timber.tag(TAG).d("🔥 SessionEffectHandler初始化完成")
    }

    /**
     * 处理单个Effect - EH-Centric架构的核心方法
     */
    suspend fun handleEffect(effect: Effect) {
        Timber.tag(TAG).d("🎯 处理Effect: ${effect::class.simpleName}")

        when (effect) {
            // === 数据持久化副作用 ===
            is Effect.SaveSession -> handleSaveSession(effect)
            is Effect.SaveSet -> handleSaveSet(effect)
            is Effect.SaveScrollPosition -> handleSaveScrollPosition(effect)

            // === 训练源选择副作用 ===
            is Effect.CreateSessionFromTemplate -> {
                when {
                    effect.templateId != null -> handleCreateSessionFromTemplateId(
                        effect.templateId,
                        effect.date,
                    )
                    effect.template != null -> handleCreateSessionFromTemplateObject(
                        effect.template,
                        effect.fromPlan,
                    )
                    else -> {
                        Timber.tag(
                            TAG,
                        ).e("CreateSessionFromTemplate effect has neither templateId nor template")
                        sendIntent(Intent.ShowError(UiText.DynamicString("创建会话参数错误")))
                    }
                }
            }
            is Effect.LoadAvailablePlans -> handleLoadPlanList()
            is Effect.LoadAvailableTemplates -> handleLoadTemplateList()
            is Effect.LoadAvailableDrafts -> handleLoadDraftList() // 新增：加载草稿列表
            is Effect.LoadPlanTemplates -> handleLoadPlanTemplates(effect)
            is Effect.LoadTemplateDetails -> handleLoadTemplateDetails(effect.templateId)
            is Effect.LoadPlanDetails -> handleLoadPlanDetails(effect.planId)

            // === PRD v2.0 新增：模板切换和版本控制副作用 ===
            is Effect.LoadTemplateVersions -> handleLoadTemplateVersions(effect.sourceType)
            is Effect.SwitchToTemplate -> handleSwitchToTemplate(
                effect.templateId,
                effect.preserveProgress,
            )
            is Effect.SaveTemplateChanges -> handleSaveTemplateChanges(effect.templateId, effect.changes)
            is Effect.AddExerciseToTemplate -> handleAddExerciseToTemplate(
                effect.templateId,
                effect.exercise,
            )

            // === 导航副作用 ===
            is Effect.NavigateBack -> handleNavigateBack()
            is Effect.NavigateToExerciseDetail -> handleNavigateToExerciseDetail(effect)
            is Effect.NavigateToWorkoutSummary -> handleNavigateToWorkoutSummary(effect)
            is Effect.NavigateToSession -> handleNavigateToSession(effect)

            // === UI副作用 ===
            is Effect.ShowToast -> handleShowToast(effect)
            is Effect.ShowSnackbar -> handleShowSnackbar(effect)

            // === Session → Template 反向更新副作用 ===
            is Effect.ApplyChangesToTemplate -> handleApplyChangesToTemplate(effect)

            is Effect.HideKeyboard -> handleHideKeyboard()
            is Effect.ShowKeyboard -> handleShowKeyboard()
            is Effect.HapticFeedback -> handleHapticFeedback()

            // === 系统副作用 ===
            is Effect.KeepScreenOn -> handleKeepScreenOn()
            is Effect.AllowScreenOff -> handleAllowScreenOff()
            is Effect.EnableImmersiveMode -> handleEnableImmersiveMode()
            is Effect.DisableImmersiveMode -> handleDisableImmersiveMode()
            is Effect.ReleaseScreenLock -> handleReleaseScreenLock()

            // === 恢复副作用 ===
            is Effect.TriggerFullRecovery -> handleTriggerFullRecovery()

            // 删除重复的训练源选择副作用处理，上面已经有了

            // === 统计数据副作用 ===
            is Effect.CalculateDailyStats -> handleCalculateDailyStats(effect)
            is Effect.ShowDailyStats -> handleShowDailyStats(effect)
        }
    }

    // ==================== 数据持久化副作用 ====================

    private suspend fun handleSaveSession(effect: Effect.SaveSession) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("保存训练会话: ${effect.session.id}")

                // 🔥 修复：添加 JSON 包装处理（与 Template 保持一致）
                val session = effect.session as com.example.gymbro.domain.workout.model.WorkoutSession
                val sessionWithJson = try {
                    // JSON 安全处理和验证
                    val sessionData = mapOf(
                        "id" to session.id,
                        "userId" to session.userId,
                        "templateId" to (session.templateId ?: ""),
                        "name" to session.name,
                        "status" to session.status,
                        "exercises" to session.exercises.map { exercise ->
                            mapOf(
                                "id" to exercise.id,
                                "name" to exercise.name,
                                "sets" to exercise.sets.map { set ->
                                    mapOf(
                                        "id" to set.id,
                                        "weight" to (set.weight ?: 0.0),
                                        "reps" to (set.reps ?: 0),
                                        "isCompleted" to set.isCompleted,
                                        "timestamp" to set.timestamp,
                                    )
                                },
                            )
                        },
                        "startTime" to session.startTime,
                        "endTime" to (session.endTime ?: 0L),
                        // 🔥 已移除：lastAutosaveTime 日志记录 - 自动保存功能已被移除
                    )

                    val unifiedJsonWrapper = com.example.gymbro.features.workout.json.wrapper.UnifiedJsonWrapper(
                        com.example.gymbro.features.workout.json.core.ProcessingMode.SESSION,
                    )
                    val config = com.example.gymbro.features.workout.json.core.JsonProcessorConfig()
                    val jsonSecurityChecker = com.example.gymbro.features.workout.json.safety.JsonSecurityChecker(
                        config,
                    )
                    val jsonErrorHandler = com.example.gymbro.features.workout.json.safety.JsonErrorHandler()

                    val sessionJson: String = try {
                        unifiedJsonWrapper.wrapData(sessionData)
                    } catch (e: Exception) {
                        Timber.tag(TAG).e(e, "Session JSON包装失败，使用错误处理器")
                        com.example.gymbro.features.workout.json.safety.JsonErrorHandlerStatic.handleProcessingError(
                            e,
                            sessionData.toString(),
                        )
                    }

                    // 安全检查
                    val securityResult = jsonSecurityChecker.performSecurityCheck(sessionJson)
                    val finalSessionJson = if (!securityResult.isSecure) {
                        Timber.tag(TAG).w("Session数据安全检查失败: ${securityResult.reason}")
                        com.example.gymbro.features.workout.json.safety.JsonErrorHandlerStatic.handleSecurityIssue(
                            sessionJson,
                            listOf(securityResult.reason ?: "未知安全问题"),
                        )
                    } else {
                        sessionJson
                    }

                    // 🔥 关键修复：将 JSON 数据附加到会话对象中，确保保存完整数据
                    session.copy(
                        notes = session.notes?.let { "$it\n[SESSION_JSON_DATA]$finalSessionJson[/SESSION_JSON_DATA]" }
                            ?: "[SESSION_JSON_DATA]$finalSessionJson[/SESSION_JSON_DATA]",
                    )
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Session JSON处理失败，使用原始数据")
                    session
                }

                try {
                    val result = saveWorkoutSessionUseCase(sessionWithJson)
                    when (result) {
                        is ModernResult.Success<String> -> {
                            Timber.tag(TAG).d("训练会话保存成功，JSON数据已保存")

                            // 如果Session已完成且关联了Plan，更新Plan进度
                            if (sessionWithJson.status == "COMPLETED" && sessionWithJson.planId != null) {
                                updatePlanProgressForCompletedSession(sessionWithJson)
                            }

                            sendIntent(Intent.SessionSaved(effect.session.id))
                        }

                        is ModernResult.Error -> {
                            Timber.tag(TAG).e("保存训练会话失败: ${result.error}")
                            sendIntent(Intent.SessionSaveError)
                        }

                        is ModernResult.Loading -> {
                            // 保存过程中，暂时不处理
                        }
                    }
                } catch (exception: Throwable) {
                    Timber.tag(TAG).e(exception, "保存训练会话失败")
                    sendIntent(Intent.SessionSaveError)
                }
            } catch (exception: Exception) {
                Timber.tag(TAG).e(exception, "保存训练会话异常")
                sendIntent(Intent.SessionSaveError)
            }
        }
    }

    private suspend fun handleSaveSet(effect: Effect.SaveSet) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("保存训练组: ${effect.sessionId}/${effect.exerciseId}")

                try {
                    val result =
                        recordExerciseSetUseCase(
                            RecordExerciseSetUseCase.Params(
                                sessionId = effect.sessionId,
                                performedExerciseId = effect.exerciseId,
                                set = effect.set,
                            ),
                        )
                    when (result) {
                        is ModernResult.Success -> {
                            Timber.tag(TAG).d("训练组保存成功")
                        }

                        is ModernResult.Error -> {
                            Timber.tag(TAG).e("保存训练组失败: ${result.error}")
                            sendIntent(Intent.ShowError(UiText.DynamicString("保存训练数据失败")))
                        }

                        is ModernResult.Loading -> {
                            // 保存过程中，暂时不处理
                        }
                    }
                } catch (exception: Throwable) {
                    Timber.tag(TAG).e(exception, "保存训练组失败")
                    sendIntent(Intent.ShowError(UiText.DynamicString("保存训练数据失败")))
                }
            } catch (exception: Exception) {
                Timber.tag(TAG).e(exception, "保存训练组异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("保存训练数据失败")))
            }
        }
    }

    private suspend fun handleSaveScrollPosition(effect: Effect.SaveScrollPosition) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("保存滚动位置: ${effect.firstVisibleItemIndex}/${effect.scrollOffset}")
                // 滚动位置持久化功能已移除 - 简化实现
                Timber.tag(TAG).d("滚动位置记录完成")
            } catch (exception: Exception) {
                Timber.tag(TAG).e(exception, "滚动位置记录失败")
            }
        }
    }

    // ==================== 其他Effect处理方法（基础实现）====================

    private suspend fun handleNavigateBack() {
        Timber.tag(TAG).d("处理返回导航")
        // 导航逻辑由UI层处理，此处仅记录
    }

    private suspend fun handleNavigateToExerciseDetail(effect: Effect.NavigateToExerciseDetail) {
        Timber.tag(TAG).d("导航到动作详情: ${effect.exerciseId}")
        // 动作详情导航功能已移除 - 专注训练流程
    }

    private suspend fun handleNavigateToWorkoutSummary(effect: Effect.NavigateToWorkoutSummary) {
        Timber.tag(TAG).d("导航到训练总结")
        // 训练总结导航由UI层处理，此处仅记录
    }

    private suspend fun handleNavigateToSession(effect: Effect.NavigateToSession) {
        Timber.tag(TAG).d("导航到训练会话: ${effect.sessionId}")
        // Session 导航由UI层处理，此处仅记录
    }

    private suspend fun handleShowToast(effect: Effect.ShowToast) {
        Timber.tag(TAG).d("显示Toast: ${effect.message}")
        // Toast显示功能已移除 - 使用日志记录
    }

    private suspend fun handleShowSnackbar(effect: Effect.ShowSnackbar) {
        Timber.tag(TAG).d("显示Snackbar: ${effect.message}")
        // Snackbar显示功能已移除 - 使用日志记录
    }

    private suspend fun handleHideKeyboard() {
        Timber.tag(TAG).d("隐藏键盘")
        // 键盘控制功能已移除 - 由UI层自动处理
    }

    private suspend fun handleShowKeyboard() {
        Timber.tag(TAG).d("显示键盘")
        // 键盘控制功能已移除 - 由UI层自动处理
    }

    private suspend fun handleHapticFeedback() {
        Timber.tag(TAG).d("触觉反馈")
        // 触觉反馈功能已移除 - 简化实现
    }

    private suspend fun handleKeepScreenOn() {
        Timber.tag(TAG).d("保持屏幕常亮")
        // 屏幕常亮功能已移除 - 简化实现
    }

    private suspend fun handleAllowScreenOff() {
        Timber.tag(TAG).d("允许屏幕关闭")
        // 屏幕控制功能已移除 - 简化实现
    }

    private suspend fun handleEnableImmersiveMode() {
        Timber.tag(TAG).d("启用沉浸模式")
        // 沉浸模式功能已移除 - 简化实现
    }

    private suspend fun handleDisableImmersiveMode() {
        Timber.tag(TAG).d("禁用沉浸模式")
        // 沉浸模式功能已移除 - 简化实现
    }

    private suspend fun handleReleaseScreenLock() {
        Timber.tag(TAG).d("释放屏幕锁定")
        // 屏幕锁定功能已移除 - 简化实现
    }

    private suspend fun handleTriggerFullRecovery() {
        Timber.tag(TAG).d("触发完整状态恢复")

        // 状态恢复功能已简化 - 由ViewModel自动处理
        // 复杂的状态恢复逻辑已移除，专注核心训练功能
    }

    // ==================== 训练源选择副作用处理 ====================

    /**
     * 加载已发布的模板列表（非草稿）
     */
    private suspend fun handleLoadTemplateList() {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("加载已发布模板列表")

                templateRepository.getPublishedTemplates().collect { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val templates = result.data.toImmutableList()
                            Timber.tag(TAG).d("✅ 已发布模板加载成功: ${templates.size} 个模板")

                            // 发送成功的 Intent，包含模板列表
                            // 需要将Domain模板转换为Shared模板
                            val sharedTemplates =
                                templates
                                    .map { domainTemplate ->
                                        convertDomainTemplateToShared(domainTemplate)
                                    }.toImmutableList()
                            sendIntent(Intent.TemplatesLoaded(sharedTemplates))
                        }

                        is ModernResult.Error -> {
                            Timber.tag(TAG).e("加载模板列表失败: ${result.error}")
                            sendIntent(Intent.ShowError(UiText.DynamicString("加载模板列表失败")))
                        }

                        is ModernResult.Loading -> {
                            // 加载状态已在 Reducer 中处理
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "加载模板列表异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("加载模板列表异常")))
            }
        }
    }

    /**
     * 加载计划列表
     */
    private suspend fun handleLoadPlanList() {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("加载用户计划列表")

                // 获取当前用户ID（使用默认值，实际应该从用户会话获取）
                val currentUserId = "default_user" // 简化实现，使用默认用户ID

                // 使用PlanRepository获取用户的所有计划
                planRepository.getUserPlans(currentUserId).collect { planList ->
                    val plans = planList.toImmutableList()
                    Timber.tag(TAG).d("✅ 用户计划加载成功: ${plans.size} 个计划")

                    sendIntent(Intent.PlansLoaded(plans))
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "加载计划列表异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("加载计划列表异常")))
            }
        }
    }

    /**
     * 加载模板详情
     */
    private suspend fun handleLoadTemplateDetails(effect: Effect.LoadTemplateDetails) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("加载模板详情: ${effect.templateId}")

                val result = templateManagementUseCase.GetTemplate().invoke(effect.templateId)
                when (result) {
                    is ModernResult.Success -> {
                        result.data?.let { templateDto ->
                            // 从模板创建训练会话
                            sendIntent(Intent.LoadFromTemplate(effect.templateId, ""))
                        } ?: run {
                            sendIntent(Intent.ShowError(UiText.DynamicString("模板不存在")))
                        }
                    }

                    is ModernResult.Error -> {
                        Timber.tag(TAG).e("加载模板详情失败: ${result.error}")
                        sendIntent(Intent.ShowError(UiText.DynamicString("加载模板详情失败")))
                    }

                    is ModernResult.Loading -> {
                        // 加载状态处理
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "加载模板详情异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("加载模板详情异常")))
            }
        }
    }

    /**
     * 从模板ID创建训练会话
     */
    private suspend fun handleCreateSessionFromTemplateId(templateId: String, date: String) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("从模板ID创建训练会话: $templateId")

                // 使用 SessionManager 创建会话
                val result = sessionManager.startSessionFromTemplate(templateId)

                when (result) {
                    is ModernResult.Success -> {
                        val domainSession = result.data
                        Timber.tag(TAG).d("训练会话创建成功: ${domainSession.id}")

                        // 转换为 SessionContract 期望的类型
                        val sessionContractSession = convertToSessionContractWorkoutSession(domainSession)
                        val convertedResult = ModernResult.Success(sessionContractSession)

                        // 发送会话加载成功的 Intent
                        sendIntent(Intent.OnSessionLoaded(convertedResult))

                        // 🔥 新增：Session 创建成功后，通过 Intent 触发导航
                        sendIntent(Intent.NavigateToSessionResult(domainSession.id))
                    }

                    is ModernResult.Error -> {
                        Timber.tag(TAG).e("从模板创建会话失败: ${result.error}")
                        sendIntent(Intent.OnSessionLoaded(result as ModernResult<SessionWorkoutSession>))
                    }

                    is ModernResult.Loading -> {
                        // 发送加载状态
                        sendIntent(Intent.OnSessionLoaded(result as ModernResult<SessionWorkoutSession>))
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "从模板创建会话异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("创建训练会话异常: ${e.message}")))
            }
        }
    }

    /**
     * 从模板对象创建训练会话
     */
    private suspend fun handleCreateSessionFromTemplateObject(
        template: com.example.gymbro.shared.models.workout.WorkoutTemplate,
        fromPlan: com.example.gymbro.shared.models.workout.WorkoutPlan?,
    ) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("从模板对象创建训练会话: ${template.id}")

                // 使用 SessionManager 创建会话
                val result = sessionManager.startSessionFromTemplate(template.id)

                when (result) {
                    is ModernResult.Success -> {
                        val domainSession = result.data
                        Timber.tag(TAG).d("训练会话创建成功: ${domainSession.id}")

                        // 如果从计划创建，设置planId
                        val updatedSession = fromPlan?.let { plan ->
                            domainSession.copy(planId = plan.id)
                        } ?: domainSession

                        // 转换为 SessionContract 期望的类型
                        val sessionContractSession =
                            convertToSessionContractWorkoutSession(updatedSession)
                        val convertedResult = ModernResult.Success(sessionContractSession)

                        // 发送会话加载成功的 Intent
                        sendIntent(Intent.OnSessionLoaded(convertedResult))

                        // 🔥 新增：Session 创建成功后，通过 Intent 触发导航
                        sendIntent(Intent.NavigateToSessionResult(updatedSession.id))
                    }

                    is ModernResult.Error -> {
                        Timber.tag(TAG).e("从模板对象创建会话失败: ${result.error}")
                        sendIntent(Intent.OnSessionLoaded(result as ModernResult<SessionWorkoutSession>))
                    }

                    is ModernResult.Loading -> {
                        // 发送加载状态
                        sendIntent(Intent.OnSessionLoaded(result as ModernResult<SessionWorkoutSession>))
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "从模板对象创建会话异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("创建训练会话异常: ${e.message}")))
            }
        }
    }

    /**
     * 转换 Domain WorkoutSession 为 SessionContract WorkoutSession
     */
    private fun convertToSessionContractWorkoutSession(
        domainSession: com.example.gymbro.domain.workout.model.WorkoutSession,
    ): SessionWorkoutSession =
        SessionWorkoutSession(
            id = domainSession.id,
            date = null, // Domain 模型中没有 LocalDate，使用 null
            templateId = domainSession.templateId,
            name = domainSession.name,
            description = domainSession.notes,
            startTime = domainSession.startTime,
            endTime = domainSession.endTime,
            isCompleted = domainSession.status == "COMPLETED",
            plannedDate = null,
            plannedTemplateId = domainSession.templateId,
            status =
            when (domainSession.status) {
                "IN_PROGRESS" -> SessionWorkoutSession.Status.IN_PROGRESS
                "COMPLETED" -> SessionWorkoutSession.Status.COMPLETED
                "ABORTED" -> SessionWorkoutSession.Status.SKIPPED
                "PAUSED" -> SessionWorkoutSession.Status.IN_PROGRESS
                else -> SessionWorkoutSession.Status.PLANNED
            },
            completionTimestamp = domainSession.endTime,
            exercises =
            domainSession.exercises.map { domainExercise ->
                com.example.gymbro.domain.workout.model.session.SessionExercise(
                    id = domainExercise.id,
                    sessionId = domainExercise.sessionId,
                    exerciseId = domainExercise.exerciseId,
                    order = domainExercise.order,
                    sets =
                    domainExercise.sets.mapIndexed { index, domainSet ->
                        com.example.gymbro.domain.exercise.model.ExerciseSet(
                            id = domainSet.id,
                            exerciseId = domainExercise.exerciseId,
                            order = index + 1,
                            weight = domainSet.weight?.toFloat(),
                            reps = domainSet.reps,
                            isCompleted = domainSet.isCompleted,
                            notes = domainSet.notes,
                        )
                    },
                    notes = domainExercise.notes,
                )
            },
            isSynced = false,
            // 🔥 已移除：lastAutosaveTime 数据映射 - 自动保存功能已被移除
            userId = domainSession.userId,
        )

    /**
     * 加载计划详情
     */
    private suspend fun handleLoadPlanDetails(effect: Effect.LoadPlanDetails) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("加载计划详情: ${effect.planId}")

                val result = getPlanWithTemplateVersionsUseCase(effect.planId)
                when (result) {
                    is ModernResult.Success -> {
                        result.data?.let { planDetails ->
                            // 从计划创建训练会话，使用当前日期
                            val currentDate = System.currentTimeMillis().toString()
                            sendIntent(Intent.LoadFromPlan(effect.planId, currentDate))
                        } ?: run {
                            sendIntent(Intent.ShowError(UiText.DynamicString("计划不存在")))
                        }
                    }

                    is ModernResult.Error -> {
                        Timber.tag(TAG).e("加载计划详情失败: ${result.error}")
                        sendIntent(Intent.ShowError(UiText.DynamicString("加载计划详情失败")))
                    }

                    is ModernResult.Loading -> {
                        // 加载状态处理
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "加载计划详情异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("加载计划详情异常")))
            }
        }
    }

    // ==================== Session → Template 反向更新副作用 ====================

    /**
     * 处理将Session更改应用到Template
     */
    private suspend fun handleApplyChangesToTemplate(effect: Effect.ApplyChangesToTemplate) {
        Timber.tag(TAG).d("🔄 开始应用Session更改到Template: templateId=${effect.templateId}")

        try {
            val result =
                applySessionChangesToTemplateUseCase.invoke(
                    ApplySessionChangesToTemplateUseCase.Params(
                        sessionId = effect.sessionId,
                        templateId = effect.templateId,
                        exerciseOverrides = effect.exerciseOverrides,
                        description = effect.description,
                    ),
                )

            when (result) {
                is ModernResult.Success -> {
                    val newVersion = result.data
                    Timber.tag(TAG).d("✅ 成功应用Session更改到Template: 版本=${newVersion.versionNumber}")

                    // 显示成功消息
                    sendIntent(Intent.ClearError) // 清除错误
                    // 可以添加成功的Snackbar或Toast

                    // 可选：刷新当前Session以反映Template的更新
                    // sendIntent(Intent.LoadSession(effect.sessionId))
                }

                is ModernResult.Error -> {
                    val error = result.error
                    Timber.tag(TAG).e("❌ 应用Session更改到Template失败: ${error.uiMessage}")
                    sendIntent(Intent.ShowError(error.uiMessage ?: UiText.DynamicString("保存模板更改失败")))
                }

                is ModernResult.Loading -> {
                    // Loading 状态通常在 UseCase 内部处理，这里不需要特殊处理
                    Timber.tag(TAG).d("🔄 正在应用Session更改到Template...")
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 应用Session更改到Template异常")
            sendIntent(Intent.ShowError(UiText.DynamicString("保存模板更改失败")))
        }
    }

    /**
     * 加载计划内的模板
     */
    private suspend fun handleLoadPlanTemplates(effect: Effect.LoadPlanTemplates) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("加载计划内模板: planId=${effect.planId}")

                when (val result = planRepository.getPlanTemplates(effect.planId)) {
                    is ModernResult.Success -> {
                        val templates = result.data.toImmutableList()
                        Timber.tag(TAG).d("✅ 计划内模板加载成功: ${templates.size} 个模板")

                        // 需要将Domain模板转换为Shared模板
                        val sharedTemplates =
                            templates
                                .map { domainTemplate ->
                                    convertDomainTemplateToShared(domainTemplate)
                                }.toImmutableList()
                        sendIntent(Intent.PlanTemplatesLoaded(effect.planId, sharedTemplates))
                    }

                    is ModernResult.Error -> {
                        Timber.tag(TAG).e("加载计划内模板失败: ${result.error}")
                        sendIntent(Intent.ShowError(UiText.DynamicString("加载计划模板失败")))
                    }

                    is ModernResult.Loading -> {
                        // Loading状态已在Reducer中处理
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "加载计划内模板异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("加载计划模板异常")))
            }
        }
    }

    /**
     * 开始即兴训练
     */
    private suspend fun handleStartFreestyleWorkout() {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("🎯 开始即兴训练模式")

                // 激活即兴训练模式
                sendIntent(Intent.FreestyleModeActivated)

                // 即兴训练会话创建功能待实现
                // 当前版本专注于模板和计划训练
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "启动即兴训练异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("启动即兴训练失败")))
            }
        }
    }

    /**
     * 更新已完成Session的Plan进度
     */
    private fun updatePlanProgressForCompletedSession(
        session: com.example.gymbro.domain.workout.model.WorkoutSession,
    ) {
        handlerScope.launch(ioDispatcher) {
            try {
                val planId = session.planId ?: return@launch

                Timber.tag(TAG).d("更新Plan进度: planId=$planId, sessionId=${session.id}")

                // 获取Plan信息以确定dayNumber
                val planResult = planRepository.getPlan(planId)
                when (planResult) {
                    is ModernResult.Success -> {
                        val plan = planResult.data

                        // 根据Session的startTime计算对应的dayNumber
                        val dayNumber = calculateDayNumberFromSession(session, plan)

                        if (dayNumber != null) {
                            // 更新Plan进度为COMPLETED
                            val progressResult =
                                planProgressUseCase.setDayProgress(
                                    planId = planId,
                                    dayNumber = dayNumber,
                                    status = PlanProgressStatus.COMPLETED,
                                )

                            progressResult.fold(
                                onSuccess = {
                                    Timber.tag(TAG).d("Plan进度更新成功: planId=$planId, day=$dayNumber")
                                },
                                onFailure = { error ->
                                    Timber.tag(TAG).e(error, "Plan进度更新失败: planId=$planId, day=$dayNumber")
                                },
                            )
                        } else {
                            Timber.tag(TAG).w("无法确定Session对应的dayNumber: sessionId=${session.id}")
                        }
                    }
                    is ModernResult.Error -> {
                        Timber.tag(TAG).e("获取Plan信息失败: planId=$planId, error=${planResult.error}")
                    }
                    is ModernResult.Loading -> {
                        // 忽略Loading状态
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "更新Plan进度时发生异常: sessionId=${session.id}")
            }
        }
    }

    /**
     * 根据Session的startTime计算对应Plan的dayNumber
     */
    private fun calculateDayNumberFromSession(
        session: com.example.gymbro.domain.workout.model.WorkoutSession,
        plan: com.example.gymbro.shared.models.workout.WorkoutPlan,
    ): Int? =
        try {
            // 简化实现：根据Session的templateId在Plan的dailySchedule中查找对应的dayNumber
            // 这是一个临时解决方案，理想情况下Session应该直接记录dayNumber
            val templateId = session.templateId

            plan.dailySchedule.entries
                .find { (_, dayPlan) ->
                    dayPlan.getActiveTemplateReferences().any { versionId ->
                        // 这里需要更复杂的逻辑来匹配templateId和templateVersionId
                        // 暂时使用简化的匹配逻辑
                        versionId.contains(templateId) || templateId.contains(versionId)
                    }
                }?.key
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "计算dayNumber失败")
            null
        }

    /**
     * 清理资源 - 在ViewModel onCleared时调用
     */
    fun cleanup() {
        Timber.tag(TAG).d("清理SessionEffectHandler资源")

        // 取消所有活跃的倒计时
        activeCountdownJobs.values.forEach { job ->
            job.cancel()
        }
        activeCountdownJobs.clear()
    }

    /**
     * 将Domain模板转换为Shared模板
     */
    private fun convertDomainTemplateToShared(
        domainTemplate: com.example.gymbro.domain.workout.model.template.WorkoutTemplate,
    ): com.example.gymbro.shared.models.workout.WorkoutTemplate =
        com.example.gymbro.shared.models.workout.WorkoutTemplate(
            id = domainTemplate.id,
            name = domainTemplate.name,
            description = domainTemplate.description,
            exercises =
            domainTemplate.exercises.map { domainExercise ->
                com.example.gymbro.shared.models.workout.TemplateExercise(
                    id = domainExercise.id,
                    exerciseId = domainExercise.exerciseId,
                    exerciseName = domainExercise.name,
                    imageUrl = null, // Domain model doesn't have imageUrl
                    videoUrl = null, // Domain model doesn't have videoUrl
                    customSets =
                    (1..domainExercise.sets).map { setNumber ->
                        com.example.gymbro.shared.models.workout.TemplateSet(
                            setNumber = setNumber,
                            targetWeight = domainExercise.weight ?: 0f,
                            targetReps = domainExercise.reps,
                            restTimeSeconds = domainExercise.restSeconds,
                            targetDuration = null, // Domain model doesn't have targetDuration
                            rpe = null, // Domain model doesn't have rpe
                        )
                    },
                    restTimeSeconds = domainExercise.restSeconds,
                    notes = domainExercise.notes,
                )
            },
            metadata =
            com.example.gymbro.shared.models.workout.TemplateMetadata(
                estimatedDuration = domainTemplate.estimatedDuration ?: 30,
                targetMuscleGroups = domainTemplate.targetMuscleGroups ?: emptyList(),
                equipment = emptyList(), // Domain model doesn't have equipment field
                tags = domainTemplate.tags ?: emptyList(),
                difficultyLevel = null, // Domain model has Int difficulty, shared model has DifficultyLevel enum
                authorId = null, // Domain model doesn't have authorId
                authorName = null, // Domain model doesn't have authorName
                isPublic = domainTemplate.isPublic,
                popularity = domainTemplate.usageCount,
                source = if (domainTemplate.isPublic) "PUBLIC" else "USER",
            ),
            createdAt = domainTemplate.createdAt,
            version = "2.0", // Domain model doesn't have version field
        )

    // ==================== 统计数据副作用 ====================

    private suspend fun handleCalculateDailyStats(effect: Effect.CalculateDailyStats) {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("计算日级统计数据: ${effect.sessionId}")

                // 根据文档实现：调用CreateDailyStatsUseCase
                val result = createDailyStatsUseCase(
                    userId = "default_user", // 简化实现，使用默认用户ID
                    date = null, // 使用今日日期
                )

                when (result) {
                    is ModernResult.Loading -> {
                        Timber.tag(TAG).d("统计数据计算中...")
                        // Loading状态不需要特殊处理，等待完成
                    }
                    is ModernResult.Success -> {
                        // CreateDailyStatsUseCase返回Unit，不是DailyStats
                        // 创建简化的UI模型
                        val today =
                            kotlinx.datetime.Clock.System
                                .now()
                                .toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault())
                                .date
                        val uiModel =
                            com.example.gymbro.features.workout.session.SessionContract.DailyStatsUiModel(
                                date = today,
                                completedSessions = 1,
                                totalVolume = 0.0,
                                totalDurationSec = 0,
                                completedSets = 0,
                                avgRpe = null,
                            )

                        // 发送结果Intent
                        sendIntent(Intent.DailyStatsReadyResult(uiModel))
                    }
                    is ModernResult.Error -> {
                        Timber.tag(TAG).e("计算日级统计失败: ${result.error}")
                        sendIntent(Intent.ShowError(UiText.DynamicString("统计计算失败")))
                    }
                }
            } catch (exception: Exception) {
                Timber.tag(TAG).e(exception, "计算日级统计异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("统计计算异常: ${exception.message}")))
            }
        }
    }

    private suspend fun handleShowDailyStats(effect: Effect.ShowDailyStats) {
        Timber.tag(TAG).d("显示日级统计数据")
        // 统计数据显示功能已简化 - 仅记录日志
        // 实际的UI显示由UI层处理
    }

    // ==================== PRD v2.0 新增Effect处理方法 ====================

    /**
     * 加载草稿列表 - 修复实现
     * 从 TemplateRepository 获取草稿状态的模板
     */
    private suspend fun handleLoadDraftList() {
        handlerScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).d("加载草稿模板列表")

                // 🔧 修复：使用 TemplateRepository.getDraftTemplates() 获取草稿
                templateRepository.getDraftTemplates().collect { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val draftTemplates = result.data.toImmutableList()
                            Timber.tag(TAG).d("✅ 草稿模板加载成功: ${draftTemplates.size} 个草稿")

                            // 转换为 Shared 模型
                            val sharedDrafts = draftTemplates.map { domainTemplate ->
                                convertDomainTemplateToShared(domainTemplate)
                            }.toImmutableList()

                            sendIntent(Intent.DraftsLoaded(sharedDrafts))
                        }

                        is ModernResult.Error -> {
                            Timber.tag(TAG).e("加载草稿列表失败: ${result.error}")
                            sendIntent(Intent.ShowError(UiText.DynamicString("加载草稿列表失败")))
                        }

                        is ModernResult.Loading -> {
                            // 加载状态已在 Reducer 中处理
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "加载草稿列表异常")
                sendIntent(Intent.ShowError(UiText.DynamicString("加载草稿列表异常")))
            }
        }
    }

    // 删除重复的 handleLoadPlanTemplates 方法，使用上面的完整实现

    private suspend fun handleLoadTemplateDetails(templateId: String) {
        Timber.tag(TAG).d("加载模板详情: $templateId")
        try {
            // 模拟加载模板详情
            // 实际应该从Repository获取模板详细信息
            Timber.tag(TAG).d("模板详情加载完成: $templateId")
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "加载模板详情失败")
            sendIntent(Intent.ShowError(UiText.DynamicString("加载模板详情失败: ${exception.message}")))
        }
    }

    private suspend fun handleLoadPlanDetails(planId: String) {
        Timber.tag(TAG).d("加载计划详情: $planId")
        try {
            // 模拟加载计划详情
            // 实际应该从Repository获取计划详细信息
            Timber.tag(TAG).d("计划详情加载完成: $planId")
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "加载计划详情失败")
            sendIntent(Intent.ShowError(UiText.DynamicString("加载计划详情失败: ${exception.message}")))
        }
    }

    private suspend fun handleLoadTemplateVersions(sourceType: com.example.gymbro.features.workout.session.WorkoutSourceType) {
        Timber.tag(TAG).d("加载模板版本列表: $sourceType")
        try {
            // 根据源类型加载可切换的模板/草稿列表
            val templates = when (sourceType) {
                com.example.gymbro.features.workout.session.WorkoutSourceType.TEMPLATE -> {
                    // 加载正式模板列表
                    listOf<com.example.gymbro.shared.models.workout.WorkoutTemplate>()
                }
                com.example.gymbro.features.workout.session.WorkoutSourceType.DRAFT -> {
                    // 加载草稿列表
                    listOf<com.example.gymbro.shared.models.workout.WorkoutTemplate>()
                }
                com.example.gymbro.features.workout.session.WorkoutSourceType.PLAN -> {
                    // 加载计划模板列表
                    listOf<com.example.gymbro.shared.models.workout.WorkoutTemplate>()
                }
            }
            sendIntent(Intent.TemplateVersionsLoaded(templates.toImmutableList()))
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "加载模板版本失败")
            sendIntent(Intent.ShowError(UiText.DynamicString("加载模板版本失败: ${exception.message}")))
        }
    }

    private suspend fun handleSwitchToTemplate(templateId: String, preserveProgress: Boolean) {
        Timber.tag(TAG).d("切换到模板: $templateId, 保留进度: $preserveProgress")
        try {
            // 实现模板切换逻辑
            // 1. 保存当前训练进度（如果preserveProgress为true）
            // 2. 加载新模板数据
            // 3. 创建新的session或更新当前session

            if (preserveProgress) {
                Timber.tag(TAG).d("保留当前训练进度")
                // 保存当前进度的逻辑
            }

            // 模拟加载新模板
            Timber.tag(TAG).d("模板切换完成: $templateId")
            // 通过ViewModel显示Toast，不是直接发送Intent
            // sendIntent(Intent.ShowToast(UiText.DynamicString("已切换到新模板")))
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "模板切换失败")
            sendIntent(Intent.ShowError(UiText.DynamicString("模板切换失败: ${exception.message}")))
        }
    }

    private suspend fun handleSaveTemplateChanges(templateId: String, changes: Map<String, Any>) {
        Timber.tag(TAG).d("保存模板修改: $templateId")
        try {
            // 实现模板修改保存逻辑
            // 1. 验证修改数据
            // 2. 更新模板到数据库
            // 3. 更新版本号

            Timber.tag(TAG).d("模板修改保存完成: $templateId, 修改项: ${changes.keys}")
            // 通过ViewModel显示Toast，不是直接发送Intent
            // sendIntent(Intent.ShowToast(UiText.DynamicString("模板修改已保存")))
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "保存模板修改失败")
            sendIntent(Intent.ShowError(UiText.DynamicString("保存模板修改失败: ${exception.message}")))
        }
    }

    private suspend fun handleAddExerciseToTemplate(templateId: String, exercise: com.example.gymbro.domain.exercise.model.Exercise) {
        Timber.tag(TAG).d("添加动作到模板: $templateId, 动作: ${exercise.name}")
        try {
            // 实现动作添加到模板的逻辑
            // 1. 将动作转换为TemplateExercise
            // 2. 添加到模板的exercises列表
            // 3. 更新模板到数据库

            Timber.tag(TAG).d("动作添加到模板完成: ${exercise.name}")
            // 通过ViewModel显示Toast，不是直接发送Intent
            // sendIntent(Intent.ShowToast(UiText.DynamicString("动作已添加到模板")))
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "添加动作到模板失败")
            sendIntent(Intent.ShowError(UiText.DynamicString("添加动作失败: ${exception.message}")))
        }
    }
}
