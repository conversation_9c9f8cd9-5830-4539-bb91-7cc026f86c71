package com.example.gymbro.features.workout.session

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.model.ExerciseSet
import com.example.gymbro.domain.exercise.model.getDisplayName
import com.example.gymbro.domain.workout.model.session.SessionExercise
import com.example.gymbro.domain.workout.model.session.WorkoutSession
import com.example.gymbro.shared.models.workout.ExerciseUpdateData
import com.example.gymbro.shared.models.workout.WorkoutPlan
import com.example.gymbro.shared.models.workout.WorkoutTemplate
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

/**
 * 训练总结数据类型
 * 用于表示训练完成后的总结信息
 */
typealias WorkoutSummary = String

/**
 * 训练源类型枚举 - PRD v2.0 三源设计
 */
enum class WorkoutSourceType {
    PLAN, // 使用计划
    TEMPLATE, // 直接使用正式模板
    DRAFT, // 直接使用草稿模板
}

/**
 * 选择步骤枚举
 */
enum class SelectionStep {
    SOURCE_TYPE, // 第一步：选择源类型（PLAN或TEMPLATE）
    SPECIFIC_SELECTION, // 第二步：具体选择计划或模板
    CONFIRM, // 第三步：确认选择
}

/**
 * 训练源选择状态 - PRD v2.0 扩展三源支持
 */
@Immutable
data class WorkoutSourceState(
    val isShowingSelector: Boolean = false,
    val currentStep: SelectionStep = SelectionStep.SOURCE_TYPE,
    val selectedSourceType: WorkoutSourceType = WorkoutSourceType.TEMPLATE,
    val availablePlans: ImmutableList<WorkoutPlan> = persistentListOf(),
    val availableTemplates: ImmutableList<WorkoutTemplate> = persistentListOf(),
    val availableDrafts: ImmutableList<WorkoutTemplate> = persistentListOf(), // 草稿模板列表
    val selectedPlan: WorkoutPlan? = null,
    val selectedTemplate: WorkoutTemplate? = null,
    val selectedDraft: WorkoutTemplate? = null, // 选中的草稿
    val planTemplates: ImmutableList<WorkoutTemplate> = persistentListOf(), // 计划内的模板
    val isLoadingPlans: Boolean = false,
    val isLoadingTemplates: Boolean = false,
    val isLoadingDrafts: Boolean = false, // 草稿加载状态
    val error: UiText? = null,
)

/**
 * Workout Session Contract - 基于训练页面.md文档的MVI 2.0架构设计
 *
 * 核心特性：
 * - 全屏沉浸训练体验
 * - 完整状态恢复机制（滚动位置+倒计时+AI窗口状态）
 * - AI助手小窗集成
 * - 即时训练记录持久化
 * - 动效交互组件
 * - 全局倒计时覆盖层
 *
 * 基于文档要求：
 * 1. 页面加载 = 训练计划 → 模板 → Session → ExerciseSetRow；全屏无系统栏
 * 2. 任意时刻锁屏/切后台/强退/重启 → 直接回到相同滚动位置+倒计时+AI窗口状态
 * 3. 动效顺滑、手势直觉、UI极简且遵守Theme
 * 4. 训练记录与休息倒计时即刻写盘，永不丢失
 */
internal object SessionContract {
    // ==================== 数据模型 ====================

    /**
     * UI专用的SessionExercise模型
     * 组合了SessionExercise和Exercise的信息，供UI层使用
     */
    @Immutable
    data class SessionExerciseUiModel(
        val sessionExercise: SessionExercise,
        val exercise: Exercise,
    ) {
        // 便利属性，供UI直接访问
        val exerciseId: String get() = sessionExercise.exerciseId
        val sessionId: String get() = sessionExercise.sessionId
        val order: Int get() = sessionExercise.order
        val sets: ImmutableList<ExerciseSet> get() = sessionExercise.sets.toImmutableList()
        val notes: String? get() = sessionExercise.notes
        val completedAt: Long? get() = sessionExercise.completedAt

        // Exercise信息
        val name: UiText get() = exercise.name
        val description: UiText? get() = exercise.description
        val muscleGroup: String get() = exercise.muscleGroup.getDisplayName()
        val equipmentType: String get() = exercise.equipment.firstOrNull()?.getDisplayName() ?: "无器材"

        // 派生状态
        val isCompleted: Boolean get() = completedAt != null
        val completedSetsCount: Int get() = sets.count { it.isCompleted }
        val totalSetsCount: Int get() = sets.size
        val progress: Float get() = if (totalSetsCount > 0) completedSetsCount.toFloat() / totalSetsCount else 0f
    }

    /**
     * 倒计时信息状态
     */
    @Immutable
    data class CountdownInfo(
        val exerciseId: String,
        val restTimeSeconds: Int,
        val remainingSeconds: Int,
        val isActive: Boolean,
        val isPaused: Boolean,
        val customTitle: String? = null,
    )

    /**
     * 滚动状态 - 用于状态恢复
     */
    @Immutable
    data class ScrollState(
        val firstVisibleItemIndex: Int = 0,
        val firstVisibleItemScrollOffset: Int = 0,
        val lastSavedAt: Long = 0L,
    )

    /**
     * Session加载页面状态
     */
    @Immutable
    data class SessionLoadingState(
        val selectedTab: SourceTab = SourceTab.TEMPLATE,
        val templates: ImmutableList<WorkoutTemplate> = persistentListOf(),
        val plans: ImmutableList<WorkoutPlan> = persistentListOf(),
        val drafts: ImmutableList<DraftSession> = persistentListOf(),
        val isLoadingTemplates: Boolean = false,
        val isLoadingPlans: Boolean = false,
        val isLoadingDrafts: Boolean = false,
        val selectedTemplateId: String? = null,
        val selectedPlanId: String? = null,
        val selectedDraftId: String? = null,
        val error: UiText? = null,
    )

    /**
     * 选择来源Tab
     */
    enum class SourceTab {
        TEMPLATE,
        PLAN,
        DRAFT,
        FREESTYLE,
    }

    /**
     * 草稿Session
     */
    @Immutable
    data class DraftSession(
        val id: String,
        val name: String,
        val lastModified: Long,
        val exerciseCount: Int,
        val completedSets: Int,
        val totalSets: Int,
    )

    /**
     * 日级统计数据UI模型
     */
    @Immutable
    data class DailyStatsUiModel(
        val date: kotlinx.datetime.LocalDate,
        val completedSessions: Int,
        val totalVolume: Double,
        val totalDurationSec: Int,
        val completedSets: Int,
        val avgRpe: Double?,
    )

    // ==================== State 定义 ====================

    /**
     * Workout Session 主状态 - 基于训练页面.md的完整状态设计
     */
    @Immutable
    data class State(
        // === 核心训练状态 ===
        val sessionId: String = "",
        val session: WorkoutSession? = null,
        val exercises: ImmutableList<SessionExerciseUiModel> = persistentListOf(),
        val currentExerciseIndex: Int = 0,
        val isLoading: Boolean = false,
        val error: UiText? = null,
        // === 训练进度状态 ===
        val totalExercises: Int = 0,
        val completedExercises: Int = 0,
        val totalSets: Int = 0,
        val completedSetsCount: Int = 0,
        val elapsedTimeMs: Long = 0L,
        val startedAt: Long = 0L,
        val sessionDurationMin: Int = 0,
        // === 滚动状态恢复 ===
        val scrollState: ScrollState = ScrollState(),
        // === UI交互状态 ===
        val isImmersiveMode: Boolean = true, // 全屏沉浸模式
        val showTopBar: Boolean = false, // TopBar显隐状态
        val showActionDialog: Boolean = false, // 动作操作对话框
        val selectedSetForEdit: Pair<Int, Int>? = null, // (exerciseIndex, setIndex)
        val showCompleteWorkoutDialog: Boolean = false,
        val hasUnsavedChanges: Boolean = false,
        // === 会话管理状态 ===
        val isCompleting: Boolean = false,
        val isPaused: Boolean = false,
        val needsRecovery: Boolean = false, // 需要状态恢复标记
        // 🔥 已移除：lastAutoSaveAt - 自动保存功能已被移除
        // === Template版本控制（新增）===
        val originalTemplateVersion: Int? = null, // 原始Template版本号
        val exerciseOverrides: Map<String, Int> = emptyMap(), // exerciseId -> restSecondsOverride
        val countdownInfo: CountdownInfo? = null, // 倒计时状态
        // === 训练源选择 - PRD v2.0 三源设计 ===
        val workoutSourceState: WorkoutSourceState = WorkoutSourceState(),

        // === PRD v2.0 新增：模板切换和版本控制 ===
        val availableTemplateVersions: ImmutableList<WorkoutTemplate> = persistentListOf(), // 可切换的模板/草稿列表
        val showTemplateSwitcher: Boolean = false, // 右上角模板切换器显示状态
        val currentTemplateId: String? = null, // 当前使用的模板ID
        val hasTemplateChanges: Boolean = false, // 是否有未保存的模板修改

    ) : UiState {
        // === 派生状态 ===

        /**
         * 当前练习
         */
        val currentExercise: SessionExerciseUiModel?
            get() = exercises.getOrNull(currentExerciseIndex)

        /**
         * 训练进度百分比
         */
        val progressPercentage: Float
            get() = if (totalSets > 0) completedSetsCount.toFloat() / totalSets else 0f

        /**
         * 是否可以完成训练
         */
        val canCompleteWorkout: Boolean
            get() = exercises.isNotEmpty() && completedSetsCount > 0

        /**
         * 训练时长格式化
         */
        val formattedElapsedTime: String
            get() {
                val seconds = elapsedTimeMs / 1000
                val hours = seconds / 3600
                val minutes = (seconds % 3600) / 60
                val secs = seconds % 60
                return if (hours > 0) {
                    "$hours:${String.format("%02d", minutes)}:${String.format("%02d", secs)}"
                } else {
                    "$minutes:${String.format("%02d", secs)}"
                }
            }
    }

    // ==================== Intent 定义 ====================

    /**
     * 用户意图 - 基于训练页面.md的完整功能设计
     */
    sealed interface Intent : AppIntent {
        // === 会话生命周期管理 ===
        data class LoadSession(
            val sessionId: String,
        ) : Intent

        data class LoadFromTemplate(
            val templateId: String,
            val date: String,
        ) : Intent

        data class LoadFromPlan(
            val planId: String,
            val date: String,
        ) : Intent

        data class StartNewSession(
            val templateId: String,
            val timestamp: Long? = null,
        ) : Intent

        object PauseSession : Intent

        object ResumeSession : Intent

        object FinishSession : Intent

        object CompleteSession : Intent

        object CompleteWorkout : Intent

        // === 训练动作操作 ===
        data class AddExercisesToSession(
            val exercises: List<Exercise>,
            val timestamp: Long? = null,
        ) : Intent

        data class RemoveExerciseFromSession(
            val exerciseIndex: Int,
            val timestamp: Long? = null,
        ) : Intent

        data class UpdateWeight(
            val exerciseIndex: Int,
            val setIndex: Int,
            val weight: Float,
            val timestamp: Long? = null,
        ) : Intent

        data class UpdateReps(
            val exerciseIndex: Int,
            val setIndex: Int,
            val reps: Int,
            val timestamp: Long? = null,
        ) : Intent

        data class ToggleSetComplete(
            val exerciseIndex: Int,
            val setIndex: Int,
            val timestamp: Long? = null,
        ) : Intent

        data class AddSet(
            val exerciseIndex: Int,
            val timestamp: Long? = null,
        ) : Intent

        data class RemoveSet(
            val exerciseIndex: Int,
            val setIndex: Int,
            val timestamp: Long? = null,
        ) : Intent

        data class UpdateNotes(
            val exerciseIndex: Int,
            val notes: String,
            val timestamp: Long? = null,
        ) : Intent

        // === 新增的Session容器专用Intent ===
        data class UpdateExercise(
            val exerciseId: String,
            val updateData: ExerciseUpdateData,
        ) : Intent

        // 🔥 新增：支持完整 sets 数据更新的 Intent（参考 Template 模式）
        data class UpdateExerciseWithSets(
            val exerciseId: String,
            val sessionUpdateData:
            com.example.gymbro.features.workout.json.processor.SessionExerciseUpdateData,
        ) : Intent

        data class CompleteSet(
            val exerciseId: String,
            val setId: String,
        ) : Intent

        // === UI交互 ===
        object ToggleTopBar : Intent // 双击屏幕

        object EnterImmersiveMode : Intent

        object ExitImmersiveMode : Intent

        data class ShowActionDialog(
            val exerciseIndex: Int,
            val setIndex: Int,
        ) : Intent

        object HideActionDialog : Intent

        object ShowCompleteWorkoutDialog : Intent

        object HideCompleteWorkoutDialog : Intent

        data class DailyStatsReadyResult(val stats: DailyStatsUiModel) : Intent

        // === 状态恢复和持久化 ===
        data class SaveScrollPosition(
            val timestamp: Long? = null,
        ) : Intent

        data class RestoreScrollPosition(
            val firstVisibleItemIndex: Int,
            val scrollOffset: Int,
        ) : Intent

        // 🔥 已移除：TriggerAutoSave Intent - 自动保存功能已被移除

        object RecoverFromBackground : Intent

        // === 导航 ===
        object NavigateBack : Intent

        data class NavigateToExerciseDetail(
            val exerciseId: String,
        ) : Intent

        // === 新增的Session容器Intent ===
        object ToggleTimer : Intent

        data class ReviewExercise(
            val exerciseId: String,
        ) : Intent

        data class PreviewExercise(
            val exerciseId: String,
        ) : Intent

        data class ViewStatisticDetail(
            val statType: com.example.gymbro.features.workout.session.internal.components.StatisticType,
        ) : Intent

        // === 错误处理 ===
        object ClearError : Intent

        data class ShowError(
            val error: UiText,
        ) : Intent

        // === 休息时间覆盖（新增）===
        data class OverrideRestTime(
            val exerciseId: String,
            val restSeconds: Int,
        ) : Intent

        object PauseCountdown : Intent

        object ResumeCountdown : Intent

        // === Session → Template 反向更新 ===
        data class ApplyChangesToTemplate(
            val templateId: String,
            val description: String? = null,
        ) : Intent

        // === 内部Intent（EffectHandler与ViewModel通信）===
        data class SessionLoaded(
            val session: WorkoutSession,
        ) : Intent

        /**
         * 会话加载结果（支持错误处理）
         */
        data class OnSessionLoaded(
            val result: ModernResult<WorkoutSession>,
        ) : Intent

        data class NavigateToSessionResult(
            val sessionId: String,
        ) : Intent

        data class SessionSaved(
            val sessionId: String,
            val timestamp: Long? = null,
        ) : Intent

        data class SetUpdated(
            val exerciseIndex: Int,
            val setIndex: Int,
            val set: ExerciseSet,
        ) : Intent

        object SessionLoadError : Intent

        object SessionSaveError : Intent

        // === 训练源选择相关Intent - PRD v2.0 三源设计 ===

        object ShowWorkoutSourceSelector : Intent

        data class SelectWorkoutSourceType(val sourceType: WorkoutSourceType) : Intent

        data class SelectPlan(val plan: WorkoutPlan) : Intent

        data class SelectTemplate(val template: WorkoutTemplate) : Intent

        data class SelectDraft(val draft: WorkoutTemplate) : Intent // 新增：选择草稿

        data class SelectTemplateFromPlan(val template: WorkoutTemplate) : Intent

        object StartSelectedWorkout : Intent

        object BackToPreviousStep : Intent

        // === PRD v2.0 新增：模板切换和版本控制Intent ===

        object ShowTemplateSwitcher : Intent // 显示右上角模板切换器

        object HideTemplateSwitcher : Intent

        data class SwitchToTemplate(val templateId: String) : Intent // 切换到其他模板/草稿

        data class AddExerciseToCurrentTemplate(val exercise: Exercise) : Intent // 动态添加动作到当前模板

        object SaveTemplateChanges : Intent // 保存模板修改

        data class LoadTemplateVersions(val sourceType: WorkoutSourceType) : Intent // 加载可切换的模板列表

        data class TemplatesLoaded(
            val templates: ImmutableList<WorkoutTemplate>,
        ) : Intent

        data class PlansLoaded(
            val plans: ImmutableList<WorkoutPlan>,
        ) : Intent

        data class DraftsLoaded(
            val drafts: ImmutableList<WorkoutTemplate>,
        ) : Intent // 新增：草稿加载完成

        data class PlanTemplatesLoaded(
            val planId: String,
            val templates: ImmutableList<WorkoutTemplate>,
        ) : Intent

        data class TemplateVersionsLoaded(
            val versions: ImmutableList<WorkoutTemplate>,
        ) : Intent // 新增：可切换模板版本加载完成

        object FreestyleModeActivated : Intent
    }

    // ==================== Effect 定义 ====================

    /**
     * 副作用 - 基于训练页面.md的功能要求
     */
    sealed interface Effect : UiEffect {
        // === 数据持久化副作用 ===
        data class SaveSession(
            val session: WorkoutSession,
        ) : Effect

        data class SaveSet(
            val sessionId: String,
            val exerciseId: String,
            val set: ExerciseSet,
        ) : Effect

        data class SaveScrollPosition(
            val firstVisibleItemIndex: Int,
            val scrollOffset: Int,
        ) : Effect

        // === 训练源选择副作用 ===
        data class CreateSessionFromTemplate(
            val templateId: String? = null,
            val date: String = "",
            val template: WorkoutTemplate? = null,
            val fromPlan: WorkoutPlan? = null,
        ) : Effect

        // === 导航副作用 ===
        object NavigateBack : Effect

        data class NavigateToExerciseDetail(
            val exerciseId: String,
        ) : Effect

        data class NavigateToWorkoutSummary(
            val summary: WorkoutSummary,
        ) : Effect

        data class NavigateToSession(
            val sessionId: String,
        ) : Effect

        // === UI副作用 ===
        data class ShowToast(
            val message: UiText,
        ) : Effect

        data class ShowSnackbar(
            val message: UiText,
        ) : Effect

        // === Session → Template 反向更新副作用 ===
        data class ApplyChangesToTemplate(
            val sessionId: String,
            val templateId: String,
            val exerciseOverrides: Map<String, Int>,
            val description: String? = null,
        ) : Effect

        object HideKeyboard : Effect

        object ShowKeyboard : Effect

        object HapticFeedback : Effect

        // === 系统副作用 ===
        object KeepScreenOn : Effect

        object AllowScreenOff : Effect

        object EnableImmersiveMode : Effect

        object DisableImmersiveMode : Effect

        object ReleaseScreenLock : Effect

        // === 恢复副作用 ===
        object TriggerFullRecovery : Effect

        // === 训练源选择副作用 - PRD v2.0 三源设计 ===

        object LoadAvailablePlans : Effect

        object LoadAvailableTemplates : Effect

        object LoadAvailableDrafts : Effect // 新增：加载草稿列表

        data class LoadTemplateDetails(
            val templateId: String,
        ) : Effect

        data class LoadPlanDetails(
            val planId: String,
        ) : Effect

        data class LoadPlanTemplates(
            val planId: String,
        ) : Effect

        // === PRD v2.0 新增：模板切换和版本控制副作用 ===

        data class LoadTemplateVersions(
            val sourceType: WorkoutSourceType,
        ) : Effect // 加载可切换的模板版本列表

        data class SwitchToTemplate(
            val templateId: String,
            val preserveProgress: Boolean = true,
        ) : Effect // 切换到其他模板/草稿

        data class SaveTemplateChanges(
            val templateId: String,
            val changes: Map<String, Any>, // 通用变更数据
        ) : Effect // 保存模板修改

        data class AddExerciseToTemplate(
            val templateId: String,
            val exercise: Exercise,
        ) : Effect // 添加动作到模板

        // === 统计数据副作用 ===
        data class CalculateDailyStats(
            val sessionId: String,
        ) : Effect

        data class ShowDailyStats(
            val stats: DailyStatsUiModel,
        ) : Effect
    }
}
