package com.example.gymbro.features.thinkingbox.domain.model.events

/**
 * SemanticEvent - 语义事件定义（解析层事件）
 *
 * 🎯 核心设计目标:
 * - 由 StreamingThinkingMLParser 输出，供 DomainMapper 消费
 * - 纯解析层事件，不关心UI逻辑
 * - 支持双时序架构的6个统一事件映射
 * - 完全符合 Context-Aware Streaming Pipeline 要求
 */
sealed interface SemanticEvent {

    /**
     * XML 标签开始事件
     * @param name 标签名称 (如 "think", "thinking", "phase", "final")
     * @param attrs 标签属性映射 (如 phase id="1")
     */
    data class TagOpened(
        val name: String,
        val attrs: Map<String, String> = emptyMap(),
    ) : SemanticEvent

    /**
     * XML 标签结束事件
     * @param name 标签名称
     * @param attrs 标签属性（用于传递完整性标记等）
     */
    data class TagClosed(
        val name: String,
        val attrs: Map<String, String> = emptyMap(),
    ) : SemanticEvent

    /**
     * 文本内容发现事件
     * @param text 文本内容（已过滤空白字符）
     */
    data class TextChunk(val text: String) : SemanticEvent

    // ===== 6个统一事件系统（解析层定义）=====

    /**
     * 预思考内容块事件
     *
     * 🔥 统一事件1：对应 <think> 标签内的流式文本内容
     * 映射到 ThinkingEvent.PreThinkChunk → UiState.preThinking
     */
    data class PreThinkChunk(val text: String) : SemanticEvent

    /**
     * 预思考结束事件
     *
     * 🔥 统一事件2：标记 </think> 标签结束或进入正式思考阶段
     * 触发 Context-Aware 状态机的 PRE_THINK → THINKING 状态切换
     *
     * 触发场景：
     * 1. 检测到 </think> 标签结束
     * 2. 在 PRE_THINK 状态下检测到 <thinking> 标签
     * 3. 在 PRE_THINK 状态下检测到正式 <phase id="..."> 标签
     */
    object PreThinkEnd : SemanticEvent

    /**
     * 阶段开始事件
     *
     * 🔥 统一事件3：对应 <phase id="X"> 标签开始
     * 映射到 ThinkingEvent.PhaseStart → 阶段排队和激活
     */
    data class PhaseStart(
        val id: String,
        val title: String? = null,
    ) : SemanticEvent

    /**
     * 阶段内容事件
     *
     * 🔥 统一事件4：对应阶段内的流式文本内容
     * 映射到 ThinkingEvent.PhaseContent → 打字机渲染
     */
    data class PhaseContent(
        val id: String,
        val content: String,
    ) : SemanticEvent

    /**
     * 阶段结束事件
     *
     * 🔥 统一事件5：对应 </phase> 标签结束
     * 映射到 ThinkingEvent.PhaseComplete → 阶段完成标记
     */
    data class PhaseEnd(val id: String) : SemanticEvent

    /**
     * 阶段标题更新事件
     *
     * 🔥 Title缓冲迁移：对应 </title> 标签结束时的完整标题
     * 映射到 ThinkingEvent.PhaseTitleUpdate → 标题更新
     */
    data class PhaseTitleUpdate(
        val phaseId: String,
        val title: String,
    ) : SemanticEvent

    /**
     * 最终答案开始事件
     *
     * 🔥 统一事件6a：对应 <final> 标签开始
     * 映射到 ThinkingEvent.FinalStart → 启动后台异步渲染
     */
    object FinalStart : SemanticEvent

    /**
     * 最终答案流式块事件
     *
     * 🔥 统一事件6b：对应 <final> 标签内的流式文本内容
     * 映射到 ThinkingEvent.FinalChunk → 富文本流式渲染
     */
    data class FinalChunk(val content: String) : SemanticEvent

    /**
     * 最终答案结束事件
     *
     * 🔥 统一事件6c：对应 </final> 标签结束
     * 映射到 ThinkingEvent.FinalComplete → 停止final流式状态
     */
    object FinalEnd : SemanticEvent

    /**
     * 最终答案到达事件（已弃用）
     *
     * 🔥 【弃用】一次性事件，已被 FinalChunk + FinalEnd 替代
     * 保留用于向后兼容，新代码应使用 FinalChunk + FinalComplete
     */
    @Deprecated("使用 FinalChunk + FinalComplete 替代一次性 FinalArrived")
    data class FinalArrived(val markdown: String) : SemanticEvent

    // ===== 辅助事件（非核心6事件）=====

    /**
     * 原始思考内容事件
     *
     * 用于保留 <think>...</think> 内的完整推理草稿
     * 供日志/诊断/复盘使用，不参与UI渲染
     */
    data class RawThinking(val content: String) : SemanticEvent

    /**
     * 原始思考内容块事件
     *
     * 用于流式处理 <think>...</think> 内的内容块
     * 供实时日志记录使用，不参与UI渲染
     */
    data class RawThinkingChunk(val text: String) : SemanticEvent

    /**
     * 原始思考内容结束事件
     *
     * 标记 </think> 标签的结束，触发日志关闭
     */
    object RawThinkingClosed : SemanticEvent

    /**
     * 流结束事件
     *
     * 表示整个 token 流处理完成
     */
    data class StreamFinished(val time: java.time.Instant = java.time.Instant.now()) : SemanticEvent

    /**
     * Function Call 检测事件
     *
     * 🔥 实时检测AI流式输出中的Function Call指令
     * 供AiCoachViewModel立即处理和显示Function Call结果
     */
    data class FunctionCallDetected(
        val functionName: String,
        val arguments: String? = null,
        val isComplete: Boolean = false,
    ) : SemanticEvent

    /**
     * 原始token快照事件
     *
     * 🔥 【关键修复】用于传递累积的原始token内容给Coach模块保存
     * 解决思考内容无法正确传递的问题
     */
    data class TokensSnapshot(
        val tokensContent: String,
    ) : SemanticEvent

    /**
     * 解析错误事件
     *
     * @param error 错误详情
     */
    data class ParseErrorEvent(
        val error: com.example.gymbro.features.thinkingbox.domain.model.ParseError,
    ) : SemanticEvent
}
