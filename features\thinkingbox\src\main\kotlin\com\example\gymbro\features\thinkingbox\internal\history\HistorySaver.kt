package com.example.gymbro.features.thinkingbox.internal.history

import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
import com.example.gymbro.features.thinkingbox.di.ThinkingBoxScope
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * HistorySaver - ThinkingBox历史记录保存器
 *
 * 🔥 根据71thinkbox-choach.md方案实现：
 * - 监听ThinkingEvent，不直接接触RAW token
 * - 写入ROOM数据库的ChatRaw表
 * - 支持历史回放和Memory系统对接
 */
@Singleton
class HistorySaver
@Inject
constructor(
    private val historyRepository: HistoryRepository,
    @ThinkingBoxScope
    private val coroutineScope: CoroutineScope,
) {
    private val TAG = "TB-HISTORY"

    // 🔥 当前消息状态跟踪
    private val activeMessages = mutableMapOf<String, ThinkingMessageState>()

    // 🔥 【多轮对话修复】全局事件流，用于收集来自所有实例的事件
    private val globalEventFlow = kotlinx.coroutines.flow.MutableSharedFlow<ThinkingEvent>()

    // 🔥 【多轮对话修复】确保只启动一次全局监听
    private val isGlobalListenerStarted =
        java.util.concurrent.atomic
            .AtomicBoolean(false)

    init {
        // 🔥 【多轮对话修复】在初始化时启动全局事件监听
        startGlobalEventListener()
    }

    /**
     * 🔥 【多轮对话修复】启动全局事件监听器（只启动一次）
     */
    private fun startGlobalEventListener() {
        if (!isGlobalListenerStarted.compareAndSet(false, true)) {
            return // 已经启动，避免重复
        }

        coroutineScope.launch {
            globalEventFlow.collect { event ->
                try {
                    handleThinkingEvent(event)
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "❌ 处理ThinkingEvent失败: ${event::class.simpleName}")
                }
            }
        }

        Timber.tag(TAG).i("🔥 全局ThinkingEvent监听器已启动")
    }

    /**
     * 🔥 【V2系统提升】注册V2实例事件流到全局监听器
     */
    fun registerInstanceEventFlowV2(
        instanceEventFlow: Flow<ThinkingEvent>,
        messageId: String,
    ) {
        coroutineScope.launch {
            instanceEventFlow.collect { event ->
                handleThinkingEventV2(event, messageId)
            }
        }

        Timber.tag(TAG).d("🔥 注册V2实例事件流: messageId=$messageId")
    }

    /**
     * 🔥 【V1兼容】注册V1实例事件流到全局监听器（已废弃）
     */
    @Deprecated("V1事件系统已废弃，使用registerInstanceEventFlowV2", ReplaceWith("registerInstanceEventFlowV2"))
    fun registerInstanceEventFlow(
        instanceEventFlow: Flow<ThinkingEvent>,
        messageId: String,
    ) {
        Timber.tag(TAG).w("⚠️ [V2系统提升] registerInstanceEventFlow已废弃，建议使用V2事件系统")
        coroutineScope.launch {
            instanceEventFlow.collect { event ->
                // 转发到全局事件流
                globalEventFlow.emit(event)
            }
        }

        Timber.tag(TAG).d("🔥 注册V1实例事件流（已废弃）: messageId=$messageId")
    }

    /**
     * 🔥 【V1兼容】保留原有的startSaving方法（已废弃）
     */
    @Deprecated("V1事件系统已废弃，使用registerInstanceEventFlowV2", ReplaceWith("registerInstanceEventFlowV2"))
    fun startSaving(thinkingEvents: Flow<ThinkingEvent>) {
        Timber.tag(TAG).w("⚠️ [V2系统提升] startSaving已废弃，建议使用V2事件系统")
        coroutineScope.launch {
            thinkingEvents.collect { event ->
                globalEventFlow.emit(event)
            }
        }
    }

    /**
     * 🔥 【V2系统提升】处理V2思考事件
     */
    private suspend fun handleThinkingEventV2(event: ThinkingEvent, messageId: String) {
        Timber.tag(TAG).d("🔥 [V2系统] 处理事件: ${event::class.simpleName}, messageId=$messageId")

        when (event) {
            is ThinkingEvent.FinalArrived -> {
                // 🔥 【时序修复】禁用自动保存，等待Coach模块手动触发
                Timber.tag(TAG).d("🔥 [时序修复] 跳过V2 FinalArrived自动保存，等待Coach模块手动触发")
                // 可以在这里添加V2特有的处理逻辑
            }
            else -> {
                // 其他V2事件暂时不需要特殊处理
                Timber.tag(TAG).v("🔥 [V2系统] 忽略事件: ${event::class.simpleName}")
            }
        }
    }

    /**
     * 🔥 【时序修复】完全禁用HistorySaver的自动保存功能
     *
     * 所有thinking相关数据的保存都由Coach模块在AI消息保存成功后手动触发
     * 这样确保时序正确：AI消息先保存，thinking数据后保存
     */
    private suspend fun handleThinkingEvent(event: ThinkingEvent) {
        when (event) {
            is ThinkingEvent.FinalArrived -> {
                // 🔥 【V2系统统一】处理最终答案到达事件
                Timber
                    .tag(TAG)
                    .d("🔥 [V2系统] 跳过FinalArrived自动保存，等待Coach模块手动触发")
                // 可以在这里添加V2特有的处理逻辑
            }

            is ThinkingEvent.PreThinkEnd -> {
                // 🔥 【V2系统统一】预思考结束事件
                Timber.tag(TAG).d("🔥 [V2系统] 预思考结束")
            }

            is ThinkingEvent.PhaseStart -> {
                // 🔥 【V2系统统一】阶段开始事件
                Timber.tag(TAG).d("🔥 [V2系统] 阶段开始: ${event.id}")
            }

            is ThinkingEvent.PhaseEnd -> {
                // 🔥 【V2系统统一】阶段结束事件
                Timber.tag(TAG).d("🔥 [V2系统] 阶段结束: ${event.id}")
            }

            else -> {
                // 其他事件不需要持久化
                Timber.tag(TAG).v("📝 跳过事件: ${event::class.simpleName}")
            }
        }
    }

    // 🔥 【V2系统清理】旧的事件处理方法已移除，V2系统使用统一的6个事件

    // 🔥 【V2系统清理】旧的ThinkingFinished处理方法已移除

    /**
     * 清理资源
     */
    fun cleanup() {
        activeMessages.clear()
        Timber.tag(TAG).d("🧹 HistorySaver已清理")
    }
}

/**
 * 思考消息状态
 */
private data class ThinkingMessageState(
    val messageId: String,
    val startedAt: Long,
    val status: String,
)
