package com.example.gymbro.features.workout.session

import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.features.workout.session.SessionContract.Effect
import com.example.gymbro.features.workout.session.SessionContract.Intent
import com.example.gymbro.features.workout.session.SessionContract.State
import com.example.gymbro.features.workout.session.internal.effect.SessionEffectHandler
import com.example.gymbro.features.workout.session.internal.reducer.SessionReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
// JSON 处理已委托给 /features/workout/json/ 目录下的统一组件

/**
 * Workout Session ViewModel - 基于训练页面.md的MVI 2.0协调器
 *
 * 核心职责：
 * 1. 作为MVI架构的协调中心，管理Intent→Reducer→Effect→EffectHandler数据流
 * 2. 维护训练会话的完整状态，包括倒计时、AI窗口、滚动位置等
 * 3. 处理复杂的状态恢复机制，确保任意时刻锁屏/切后台后完全恢复
 * 4. 集成AI助手功能，与Coach模块共用ChatFlow
 * 5. 管理全局倒计时覆盖层和沉浸式训练体验
 * 6. 提供60fps流畅动效和<3秒启动恢复性能
 *
 * 设计原则：
 * - MVI 2.0标准：严格遵循Intent→Reducer→Effect→EffectHandler单向数据流
 * - 结构化并发：使用viewModelScope管理协程生命周期
 * - 状态不变性：使用ImmutableList确保状态安全
 * - 错误处理：完善的异常捕获和用户友好的错误提示
 * - 性能优化：避免不必要的状态更新和重组
 * - 架构合规：遵循GymBro项目的MVI黄金标准
 */
@HiltViewModel
internal class SessionViewModel @Inject constructor(
    private val sessionReducer: SessionReducer,
    private val effectHandler: SessionEffectHandler,
    private val logger: com.example.gymbro.core.logging.Logger,
) : BaseMviViewModel<Intent, State, Effect>(State()) {

    companion object {
        private const val TAG = "SessionViewModel"
    }

    // 提供Reducer实例给BaseMviViewModel
    override val reducer: Reducer<Intent, State, Effect> = sessionReducer

    // JSON 处理已委托给 /features/workout/json/ 目录下的统一组件

    init {
        Timber.tag(TAG).d("🚀 SessionViewModel初始化 - MVI 2.0架构")

        // 初始化EffectHandler - 遵循Golden Standard
        initializeEffectHandler()

        Timber.tag(TAG).d("✅ SessionViewModel初始化完成")
    }

    // ==================== Effect处理 ====================

    /**
     * 初始化EffectHandler - 遵循MVI黄金标准
     */
    override fun initializeEffectHandler() {
        // 初始化EffectHandler
        effectHandler.initialize(
            scope = handlerScope,
            intentSender = ::dispatch,
        )

        // 启动Effect监听
        handlerScope.launch {
            effect.collect { effect ->
                try {
                    Timber.tag(TAG).d("🎯 处理Effect: ${effect::class.simpleName}")
                    effectHandler.handleEffect(effect)
                } catch (exception: Exception) {
                    Timber.tag(TAG).e(exception, "❌ Effect处理异常: ${effect::class.simpleName}")
                }
            }
        }
    }

    // ==================== 便捷API方法 ====================

    /**
     * 便捷方法：加载训练会话
     */
    fun loadSession(sessionId: String) {
        dispatch(Intent.LoadSession(sessionId))
    }

    /**
     * 便捷方法：从模板创建会话
     */
    fun loadFromTemplate(templateId: String, date: String = "") {
        dispatch(Intent.LoadFromTemplate(templateId, date))
    }

    /**
     * 便捷方法：开始新的训练会话
     */
    fun startNewSession(templateId: String) {
        dispatch(Intent.StartNewSession(templateId))
    }

    /**
     * 便捷方法：暂停训练会话
     */
    fun pauseSession() {
        dispatch(Intent.PauseSession)
    }

    /**
     * 便捷方法：恢复训练会话
     */
    fun resumeSession() {
        dispatch(Intent.ResumeSession)
    }

    /**
     * 便捷方法：完成训练会话
     */
    fun finishSession() {
        dispatch(Intent.FinishSession)
    }

    /**
     * 便捷方法：更新重量
     */
    fun updateWeight(exerciseIndex: Int, setIndex: Int, weight: Float) {
        dispatch(Intent.UpdateWeight(exerciseIndex, setIndex, weight))
    }

    /**
     * 便捷方法：更新次数
     */
    fun updateReps(exerciseIndex: Int, setIndex: Int, reps: Int) {
        dispatch(Intent.UpdateReps(exerciseIndex, setIndex, reps))
    }

    /**
     * 便捷方法：切换组完成状态
     */
    fun toggleSetComplete(exerciseIndex: Int, setIndex: Int) {
        dispatch(Intent.ToggleSetComplete(exerciseIndex, setIndex))
    }

    // 倒计时功能已迁移到全局悬浮倒计时系统

    /**
     * 便捷方法：进入沉浸模式
     */
    fun enterImmersiveMode() {
        dispatch(Intent.EnterImmersiveMode)
    }

    /**
     * 便捷方法：退出沉浸模式
     */
    fun exitImmersiveMode() {
        dispatch(Intent.ExitImmersiveMode)
    }

    /**
     * 便捷方法：保存滚动位置
     */
    fun saveScrollPosition() {
        dispatch(Intent.SaveScrollPosition())
    }

    /**
     * 便捷方法：从后台恢复
     */
    fun recoverFromBackground() {
        dispatch(Intent.RecoverFromBackground)
    }

    /**
     * 便捷方法：清除错误
     */
    fun clearError() {
        dispatch(Intent.ClearError)
    }

    // ==================== 状态查询和调试 ====================

    /**
     * 获取当前状态摘要 - 用于调试和监控
     */
    fun getStateSummary(): String {
        return buildString {
            append("SessionState(")
            append("sessionId=${currentState.sessionId}, ")
            append("isLoading=${currentState.isLoading}, ")
            append("isPaused=${currentState.isPaused}, ")
            append("exercises=${currentState.exercises.size}, ")
            append("totalSets=${currentState.totalSets}, ")
            append("completedSets=${currentState.completedSetsCount}, ")

            append("immersiveMode=${currentState.isImmersiveMode}, ")
            append("hasError=${currentState.error != null}")
            append(")")
        }
    }

    /**
     * 获取训练进度百分比
     */
    fun getWorkoutProgress(): Float {
        return if (currentState.totalSets > 0) {
            currentState.completedSetsCount.toFloat() / currentState.totalSets.toFloat()
        } else {
            0f
        }
    }

    /**
     * 检查是否处于沉浸模式
     */
    fun isInImmersiveMode(): Boolean {
        return currentState.isImmersiveMode
    }

    // ==================== 生命周期管理 ====================

    override fun onCleared() {
        super.onCleared()
        Timber.tag(TAG).d("🧹 SessionViewModel清理开始")

        try {
            // 清理EffectHandler资源
            effectHandler.cleanup()

            // 保存当前状态（如果需要）
            if (currentState.session != null && !currentState.isCompleting) {
                // 触发自动保存
                // 🔥 已移除：TriggerAutoSave 调用 - 自动保存功能已被移除
            }

            Timber.tag(TAG).d("✅ SessionViewModel清理完成")
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "❌ SessionViewModel清理异常")
        }
    }
}
