package com.example.gymbro.features.workout.shared.components.exercise

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.overlay.floating.LocalFloatingCountdownService
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.shared.animation.CrossModuleAnimations
import com.example.gymbro.features.workout.shared.components.keypad.KeypadContract
import com.example.gymbro.features.workout.shared.utils.NumericInputLogic
import com.example.gymbro.shared.models.exercise.ExerciseDto
import com.example.gymbro.shared.models.exercise.ExerciseSetDto
import com.example.gymbro.shared.models.workout.session.SessionExerciseState
import com.example.gymbro.shared.models.workout.session.SessionExerciseStateFactory
import timber.log.Timber
import java.util.UUID

/**
 * 动作组件使用模式
 */
enum class ExerciseComponentMode {
    SESSION, // 训练会话模式：显示倒计时按钮
    TEMPLATE, // 模板编辑模式：使用 ExerciseCardContent 的完整表格编辑UI
}

/**
 * 训练动作主组件 - 完全自洽版本 v4.0
 *
 * 🔥 核心设计原则：组件级别的完全自洽性
 * - 内部完整处理所有 Keypad 相关逻辑，包括显示、输入、数据写入
 * - 组件与 JSON 一对一映射，支持完整的数据处理
 * - 内部集成一键更改功能的完整实现
 *
 * 特性：
 * - 纯数据驱动的输入输出：接收完整 Exercise JSON，输出更新后的完整 JSON
 * - Session状态感知：根据Session状态智能调整显示和行为
 * - 内部 Keypad 集成：组件内部完整处理所有数字输入逻辑
 * - 一键更改功能：内部实现批量修改功能
 * - 双状态显示：缩略图（todolist）和详细功能
 * - 微妙动画强化：点击反馈、状态转换、倒计时都有精细动画
 *
 * @param exercise 训练动作数据（完整的 JSON 对象）
 * @param mode 组件使用模式：SESSION（训练会话）或TEMPLATE（模板编辑）
 * @param exerciseState Session中的动作状态，用于智能调整显示和行为
 * @param initialDisplayMode 初始显示模式，默认为COMPACT
 * @param autoCollapseOnComplete 完成后是否自动收起，Session中建议为false
 * @param allowManualToggle 是否允许手动切换显示模式
 * @param useGlobalCountdown 使用全局悬浮倒计时替换本地倒计时
 * @param onExerciseComplete 动作完成回调
 * @param onExerciseUpdate 动作更新回调（输出完整的更新后 JSON）
 * @param onDisplayModeChange 显示模式变化回调
 * @param onRestTimerStart 休息计时开始回调
 * @param onRestTimeChange 休息时间变更回调（TEMPLATE模式专用）
 * @param modifier 修饰符
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkoutExerciseComponent(
    exercise: ExerciseDto,
    mode: ExerciseComponentMode = ExerciseComponentMode.SESSION,
    exerciseState: SessionExerciseState = SessionExerciseStateFactory.default(),
    initialDisplayMode: ExerciseDisplayMode = ExerciseDisplayMode.COMPACT,
    autoCollapseOnComplete: Boolean = false,
    allowManualToggle: Boolean = true,
    useGlobalCountdown: Boolean = true,
    onExerciseComplete: (ExerciseDto) -> Unit = {},
    onExerciseUpdate: (ExerciseDto) -> Unit = {},
    onDisplayModeChange: (ExerciseDisplayMode) -> Unit = {},
    onRestTimerStart: (Int) -> Unit = {},
    onRestTimeChange: ((Int) -> Unit)? = null,
    modifier: Modifier = Modifier,
) {
    // 根据Session状态智能调整显示模式
    val effectiveDisplayMode =
        remember(exerciseState, initialDisplayMode) {
            when {
                exerciseState.isCurrentExercise && !exerciseState.isCompleted ->
                    ExerciseDisplayMode.EXPANDED

                exerciseState.isCompleted ->
                    ExerciseDisplayMode.COMPACT

                else ->
                    initialDisplayMode
            }
        }

    // 🔥 组件内部 Keypad 状态管理 - 实现完全自洽性 + 防冲突机制
    val componentId = remember { UUID.randomUUID().toString() }
    var showKeypad by remember { mutableStateOf(false) }
    var keypadInputValue by remember { mutableStateOf("") }
    var currentEditingField by remember { mutableStateOf<KeypadContract.InputTargetField?>(null) }

    // 🔥 内部 Keypad 显示控制 - 增强防冲突逻辑
    val showKeypadInternal = remember {
        {
                targetField: KeypadContract.InputTargetField, currentValue: String ->
            // 🔥 防冲突检查：如果已经有keypad显示，先关闭现有的
            if (showKeypad) {
                Timber.w("🔧 [KEYPAD-CONFLICT] 组件$componentId 检测到keypad冲突，关闭现有keypad")
                showKeypad = false
                currentEditingField = null
                keypadInputValue = ""
            }

            currentEditingField = targetField
            // 🔥 修复：重量字段默认从个位数开始编辑，去掉小数点
            keypadInputValue = when {
                currentValue == "0" || currentValue == "0.0" -> ""
                targetField.fieldType == KeypadContract.InputFieldType.WEIGHT && currentValue.contains(".") -> {
                    // 重量字段：只显示整数部分，小数点需要手动添加
                    currentValue.substringBefore(".").let { intPart ->
                        if (intPart == "0") "" else intPart
                    }
                }
                else -> currentValue
            }
            showKeypad = true
            Timber.d(
                "🔧 [WorkoutExerciseComponent] 组件$componentId 显示数字键盘: field=$targetField, 原始值=$currentValue, 键盘初始值=$keypadInputValue",
            )
        }
    }

    // 🔥 内部 Keypad 隐藏控制 - 增强日志
    val hideKeypadInternal = remember {
        {
            showKeypad = false
            currentEditingField = null
            keypadInputValue = ""
            Timber.d("🔧 [WorkoutExerciseComponent] 组件$componentId 隐藏内部Keypad")
        }
    }

    // 🔥 简化：直接在 onQuickModify 中处理批量修改，避免复杂的函数定义

    var state by remember(exercise.id) {
        // 🔥 核心修复：移除targetSets.size依赖，避免组数变化时状态重置
        // 🔥 问题分析：remember(exercise.id, exercise.targetSets.size)中的size依赖
        // 会导致用户添加/删除组时触发状态重置，丢失用户输入的数据
        // 🔥 解决方案：只保留exercise.id作为remember的key，确保组件状态稳定
        val initialSets = exercise.targetSets // 直接使用传入的数据，不做任何修改

        mutableStateOf(
            WorkoutExerciseState(
                exercise = exercise,
                currentSets = initialSets,
                displayMode = effectiveDisplayMode,
                autoCollapseOnComplete = autoCollapseOnComplete,
                allowManualToggle = allowManualToggle,
            ),
        )
    }

    // 🔥 修复数据循环覆盖问题：移除自动同步逻辑
    // 问题：当组件内部更新数据并通知父组件后，父组件传回的数据会触发这个LaunchedEffect
    // 导致组件内部的数据被外部数据覆盖，造成数据丢失
    // 解决方案：只在组件初始化时同步一次数据，之后完全由组件内部管理状态

    // LaunchedEffect(exercise.targetSets) {
    //     // 移除：这个逻辑会导致数据循环覆盖
    // }

    // 🔥 修复数据持久化问题：移除自动默认值创建，避免覆盖用户数据
    // 问题分析：LaunchedEffect 会在组件重组时触发，可能覆盖用户已编辑的数据
    // 解决方案：只在组件真正需要显示数据时才创建默认值，通过 UI 层面处理空数据状态

    // 注释掉自动创建默认数据的逻辑，改为在 UI 层面处理空数据显示
    // LaunchedEffect(state.currentSets.isEmpty()) {
    //     if (state.currentSets.isEmpty() && exercise.targetSets.isEmpty()) {
    //         // 移除：自动创建默认组可能覆盖用户数据
    //     }
    // }

    // 🔥 核心修复：实现状态防抖同步机制，避免用户编辑过程中被外部数据覆盖
    LaunchedEffect(exercise.id, exercise.targetSets.hashCode()) {
        // 🔥 防抖机制：只在组件真正需要同步时才执行更新
        // 判断条件：1. 内部状态为空但外部有数据（初次加载）
        //          2. 外部数据发生重大变化且与内部数据不一致（如从其他地方加载了新数据）
        val shouldSync = when {
            // 情况1：初次加载时，内部没有数据但外部有数据
            state.currentSets.isEmpty() && exercise.targetSets.isNotEmpty() -> {
                Timber.d("🔧 [WorkoutExerciseComponent] 初次加载同步: ${exercise.name}")
                true
            }
            // 情况2：完全重新加载的情况（如切换不同的exercise）
            state.exercise.id != exercise.id -> {
                Timber.d("🔧 [WorkoutExerciseComponent] Exercise ID变更同步: ${exercise.name}")
                true
            }
            // 其他情况：保持组件状态自治，不进行同步
            else -> {
                Timber.d("🔧 [WorkoutExerciseComponent] 跳过同步，保持用户数据: ${exercise.name}")
                false
            }
        }
        
        if (shouldSync) {
            state = state.copy(
                exercise = exercise,
                currentSets = exercise.targetSets,
            )
        }
    }

    // 🔥 Phase 1 Fix: 移除导致循环覆盖的LaunchedEffect
    // 原因：监控exercise.targetSets.size变化会在onExerciseUpdate回调后触发
    // 导致外部数据覆盖内部已修改的数据，造成weight值重置为0.0

    // LaunchedEffect(exercise.targetSets.size) {
    //     Timber.d("🔧 [PARAM-DEBUG] 外部exercise参数变化: 组数=${exercise.targetSets.size}, 内部state组数=${state.currentSets.size}")
    //     if (exercise.targetSets.size < state.currentSets.size) {
    //         Timber.w("🔧 [PARAM-DEBUG] ⚠️ 外部数据比内部数据少！可能发生了数据回滚")
    //     }
    // }

    // 🔥 移除：智能数据同步逻辑，防止数据流循环覆盖
    // 问题：当组件内部添加新组并通知父组件后，外部数据可能还是旧的3组数据
    // 这个LaunchedEffect会导致新添加的第4组被外部的3组数据覆盖
    // 解决方案：完全依赖组件内部状态管理，不再同步外部数据

    // LaunchedEffect(exercise.targetSets.size) {
    //     // 移除：这个逻辑在数据流循环中会导致新添加的组被覆盖
    // }

    // 🔥 直接写入 JSON 的实时更新函数 - 重新设计为真正的键盘逻辑
    val updateTargetFieldDirectly = remember {
        {
                inputValue: String ->
            currentEditingField?.let { field ->
                try {
                    // 🔥 修复：智能处理重量字段的小数点逻辑
                    val newValue = when {
                        inputValue.isEmpty() || inputValue == "." -> 0f
                        field.fieldType == KeypadContract.InputFieldType.WEIGHT -> {
                            // 重量字段：如果输入不包含小数点，保持原有小数部分
                            if (!inputValue.contains(".")) {
                                // 输入的是整数部分，需要保持原来的小数部分
                                val currentSet = state.currentSets.getOrNull(field.setIndex)
                                val originalDecimal = currentSet?.weight?.let { weight ->
                                    val decimalPart = weight - weight.toInt()
                                    if (decimalPart > 0) decimalPart else 0f
                                } ?: 0f
                                inputValue.toIntOrNull()?.toFloat()?.plus(originalDecimal) ?: 0f
                            } else {
                                // 输入包含小数点，直接解析
                                inputValue.toFloatOrNull() ?: 0f
                            }
                        }
                        else -> inputValue.toFloatOrNull() ?: 0f
                    }

                    // 🔥 修复：使用 ExerciseJsonProcessor 进行单个数据单元更新
                    // 获取目标组的 setId，添加防护逻辑
                    val targetSetId = if (field.setIndex < state.currentSets.size) {
                        state.currentSets[field.setIndex].id
                    } else {
                        // 🔥 修复：组索引超出范围时，可能是添加组后状态还未同步
                        // 使用默认ID或等待下次recomposition
                        Timber.w(
                            "🔧 [WorkoutExerciseComponent] 组索引暂时超出范围: setIndex=${field.setIndex}, 总组数=${state.currentSets.size}，使用回退策略",
                        )
                        // 使用生成的临时ID，或者直接更新原始输入框
                        "temp_set_${field.setIndex}"
                    }

                    // 将当前 exercise 转换为 JSON
                    val currentJson = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                        state.exercise.toJson() // 🔥 关键修复：使用最新的组件状态数据
                    }

                    // 🔥 新增：详细调试JSON转换过程
                    Timber.d("🔧 [JSON-DEBUG] 转换前state.exercise: 组数=${state.exercise.targetSets.size}")
                    state.exercise.targetSets.forEachIndexed { index, set ->
                        Timber.d(
                            "🔧 [JSON-DEBUG] 转换前组${index + 1}: id=${set.id}, weight=${set.weight}, reps=${set.reps}",
                        )
                    }
                    Timber.d("🔧 [JSON-DEBUG] 转换后JSON长度: ${currentJson.length}")

                    Timber.d(
                        "🔧 [WorkoutExerciseComponent] 单个数据单元更新开始: 组${field.setIndex + 1}, 字段=${field.fieldType}, setId=$targetSetId, 值=$newValue",
                    )

                    // 🔥 关键修复：使用 JSON 处理器进行单个字段更新
                    val updatedJson = when (field.fieldType) {
                        KeypadContract.InputFieldType.WEIGHT -> {
                            com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.updateExerciseWeight(
                                currentJson,
                                targetSetId,
                                newValue,
                            )
                        }
                        KeypadContract.InputFieldType.REPS -> {
                            com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.updateExerciseReps(
                                currentJson,
                                targetSetId,
                                newValue.toInt().coerceAtLeast(0),
                            )
                        }
                        KeypadContract.InputFieldType.REST_TIME -> {
                            // 🔥 新增：REST_TIME字段更新前的详细日志
                            Timber.d(
                                "🔥 [REST-DEBUG] 开始更新REST_TIME: setId=$targetSetId, 新值=${newValue.toInt().coerceIn(
                                    10,
                                    600,
                                )}s",
                            )
                            val originalSet = state.currentSets.find { it.id == targetSetId }
                            Timber.d("🔥 [REST-DEBUG] 原始休息时间: ${originalSet?.restTimeSeconds}s")

                            com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.updateExerciseSetRestTime(
                                currentJson,
                                targetSetId,
                                newValue.toInt().coerceIn(10, 600),
                            )
                        }
                    }

                    // 🔥 新增：调试JSON更新后的结果
                    Timber.d("🔧 [JSON-DEBUG] 更新后JSON长度: ${updatedJson.length}")

                    // 🔥 从更新后的 JSON 重新解析完整的 Exercise 数据
                    val rawUpdatedExercise = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                        fromJson(updatedJson)
                    } ?: state.exercise // 🔥 修复：使用当前状态的exercise而不是原始exercise

                    // 🔥 Phase 1 Fix: 添加数据完整性验证，防止weight值被错误重置
                    val validatedExercise = rawUpdatedExercise?.let { exercise ->
                        exercise.copy(
                            targetSets = exercise.targetSets.mapIndexed { index, set ->
                                val originalSet = state.currentSets.getOrNull(index)
                                // 验证weight值是否被错误重置为0
                                if (set.weight == 0f && originalSet != null && originalSet.weight > 0f) {
                                    Timber.w("🔧 [WEIGHT-VALIDATION] 检测到weight值被重置，恢复原值: ${originalSet.weight}")
                                    set.copy(weight = originalSet.weight)
                                } else {
                                    set
                                }
                            },
                        )
                    } ?: state.exercise

                    // 🔥 新增：详细调试JSON反序列化结果
                    Timber.d("🔧 [JSON-DEBUG] 反序列化后exercise: 组数=${validatedExercise?.targetSets?.size}")
                    validatedExercise?.targetSets?.forEachIndexed { index, set ->
                        Timber.d(
                            "🔧 [JSON-DEBUG] 验证后组${index + 1}: id=${set.id}, weight=${set.weight}, reps=${set.reps}, rest=${set.restTimeSeconds}s",
                        )
                    }

                    // 🔥 新增：如果是REST_TIME字段，特别验证休息时间是否正确更新
                    currentEditingField?.let { field ->
                        if (field.fieldType == KeypadContract.InputFieldType.REST_TIME) {
                            val updatedSet = validatedExercise?.targetSets?.find { it.id == targetSetId }
                            Timber.d(
                                "🔥 [REST-DEBUG] REST_TIME更新验证: 目标组的休息时间=${updatedSet?.restTimeSeconds}s",
                            )
                        }
                    }

                    // 更新组件内部状态
                    state = state.copy(
                        exercise = validatedExercise,
                        currentSets = validatedExercise.targetSets,
                    )

                    // 🔥 Phase 2 Fix: 专门处理REST_TIME字段的UI强制刷新
                    currentEditingField?.let { field ->
                        if (field.fieldType == KeypadContract.InputFieldType.REST_TIME) {
                            // 强制触发UI重新组合，确保休息时间变化立即生效
                            Timber.d("🔥 [REST-UI-SYNC] 强制触发REST_TIME UI刷新")
                            // 强制更新UI状态，确保休息时间立即显示新值
                            state = state.copy(
                                exercise = validatedExercise,
                                currentSets = validatedExercise.targetSets,
                                lastRestTimeUpdate = System.currentTimeMillis(), // 添加时间戳强制刷新
                            )
                        }
                    }

                    // 🔥 调试日志：验证单个数据单元更新结果
                    Timber.d(
                        "🔧 [WorkoutExerciseComponent] 单个数据单元更新完成: 动作=${validatedExercise.name}, 组数=${validatedExercise.targetSets.size}",
                    )
                    val updatedSet = validatedExercise.targetSets.getOrNull(field.setIndex)
                    if (updatedSet != null) {
                        Timber.d(
                            "🔧 [WorkoutExerciseComponent] 更新后组${field.setIndex + 1}数据: weight=${updatedSet.weight}, reps=${updatedSet.reps}, rest=${updatedSet.restTimeSeconds}s",
                        )
                    }

                    // 🔥 调试日志：验证 onExerciseUpdate 调用
                    Timber.d(
                        "🔧 [DEBUG-SAVE] WorkoutExerciseComponent.updateTargetFieldDirectly 即将调用 onExerciseUpdate:",
                    )
                    Timber.d(
                        "🔧 [DEBUG-SAVE] 动作=${validatedExercise.name}, targetSets=${validatedExercise.targetSets.size}",
                    )
                    validatedExercise.targetSets.forEachIndexed { index, set ->
                        Timber.d(
                            "🔧 [DEBUG-SAVE] 输出组${index + 1}: weight=${set.weight}, reps=${set.reps}, rest=${set.restTimeSeconds}s, id=${set.id}",
                        )
                    }

                    // 通知父组件：单个数据单元已更新
                    onExerciseUpdate(validatedExercise)

                    Timber.d(
                        "🔧 [DEBUG-SAVE] onExerciseUpdate 调用完成: field=${field.fieldType}, setId=$targetSetId, value=$newValue",
                    )
                } catch (e: Exception) {
                    Timber.e(e, "🔧 [WorkoutExerciseComponent] 实时更新JSON失败: input=$inputValue")
                }
            }
        }
    }

    // 🔥 内部 Keypad 数据应用逻辑 - 确认时使用
    val applyKeypadValueInternal = remember {
        {
            // 确认时隐藏键盘
            hideKeypadInternal()
        }
    }

    // 全局悬浮倒计时服务（仅在SESSION模式下启用）
    val floatingCountdownService =
        if (useGlobalCountdown && mode == ExerciseComponentMode.SESSION) {
            runCatching { LocalFloatingCountdownService.current }.getOrNull()
        } else {
            null
        }

    // 全局悬浮倒计时集成 - 监听组完成状态变化（仅SESSION模式）
    LaunchedEffect(state.currentSets) {
        // 检查是否有新完成的组需要启动倒计时
        val lastCompletedSet =
            state.currentSets
                .filter { it.isCompleted }
                .maxByOrNull { it.completedAt ?: 0L }

        if (lastCompletedSet != null &&
            mode == ExerciseComponentMode.SESSION &&
            useGlobalCountdown &&
            floatingCountdownService != null &&
            state.exercise.restTimeSeconds > 0
        ) {
            // 启动全局悬浮倒计时
            floatingCountdownService.startFloating(
                title = "组间休息 - ${state.exercise.name}",
                seconds = state.exercise.restTimeSeconds,
                onComplete = {
                    // 倒计时完成回调（可以添加额外逻辑，如通知等）
                },
            )
        }
    }

    // 清理全局倒计时（当组件销毁时，仅SESSION模式）
    DisposableEffect(Unit) {
        onDispose {
            if (useGlobalCountdown && mode == ExerciseComponentMode.SESSION) {
                floatingCountdownService?.stopFloating()
            }
        }
    }

    // 🔥 新增：监听动作完成状态变化，自动切换显示模式
    LaunchedEffect(state.allSetsCompleted, autoCollapseOnComplete) {
        if (state.allSetsCompleted && autoCollapseOnComplete && state.displayMode == ExerciseDisplayMode.EXPANDED) {
            // 动作完成且启用自动收起，切换到 COMPACT 模式
            state = WorkoutExerciseHandler.handleIntent(
                WorkoutExerciseIntent.ChangeDisplayMode(ExerciseDisplayMode.COMPACT),
                state,
            )
            onDisplayModeChange(ExerciseDisplayMode.COMPACT)
            onExerciseComplete(state.exercise.copy(targetSets = state.currentSets))
        }
    }

    // 动画配置
    val animationSpec =
        spring<IntSize>(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium,
        )

    Card(
        modifier =
        modifier
            .fillMaxWidth()
            .animateContentSize(animationSpec),
        elevation =
        CardDefaults.cardElevation(
            defaultElevation = if (state.displayMode == ExerciseDisplayMode.EXPANDED) Tokens.Elevation.Medium else Tokens.Elevation.Small,
        ),
        colors =
        CardDefaults.cardColors(
            containerColor = MaterialTheme.workoutColors.cardBackground,
        ),
    ) {
        // 使用CrossModuleAnimations实现内容切换动画
        CrossModuleAnimations.AnimatedSwap(
            targetState = state.displayMode,
            animationType = CrossModuleAnimations.SwapAnimationType.CROSSFADE,
            label = "ExerciseDisplayMode",
        ) { displayMode ->
            when (displayMode) {
                ExerciseDisplayMode.COMPACT -> {
                    CompactExerciseView(
                        state = state,
                        exerciseState = exerciseState,
                        onExpand = {
                            state =
                                WorkoutExerciseHandler.handleIntent(
                                    WorkoutExerciseIntent.ChangeDisplayMode(ExerciseDisplayMode.EXPANDED),
                                    state,
                                )
                            onDisplayModeChange(ExerciseDisplayMode.EXPANDED)
                        },
                    )
                }
                ExerciseDisplayMode.EXPANDED -> {
                    // 🔥 根据 mode 选择不同的展开视图
                    when (mode) {
                        ExerciseComponentMode.SESSION, ExerciseComponentMode.TEMPLATE -> {
                            // 使用原有的展开视图 (集成计算器)
                            ExpandedExerciseView(
                                state = state,
                                mode = mode,
                                onCollapse = {
                                    state =
                                        WorkoutExerciseHandler.handleIntent(
                                            WorkoutExerciseIntent.ChangeDisplayMode(ExerciseDisplayMode.COMPACT),
                                            state,
                                        )
                                    onDisplayModeChange(ExerciseDisplayMode.COMPACT)
                                },
                                onWeightChange = { setId, weight ->
                                    // 🔥 修复数据冲突：使用统一的单个数据单元更新机制
                                    val currentJson = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                                        state.exercise.toJson() // 🔥 关键修复：使用最新的组件状态数据
                                    }
                                    val updatedJson = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.updateExerciseWeight(
                                        currentJson,
                                        setId,
                                        weight,
                                    )
                                    val updatedExercise = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                                        fromJson(updatedJson)
                                    } ?: state.exercise // 🔥 修复：使用state.exercise而不是原始exercise参数

                                    // 更新组件状态
                                    state = state.copy(
                                        exercise = updatedExercise,
                                        currentSets = updatedExercise.targetSets,
                                    )

                                    // 🔥 调试日志：验证 onWeightChange 的 onExerciseUpdate 调用
                                    Timber.d(
                                        "🔧 [DEBUG-SAVE] WorkoutExerciseComponent.onWeightChange 即将调用 onExerciseUpdate:",
                                    )
                                    Timber.d("🔧 [DEBUG-SAVE] setId=$setId, weight=$weight")
                                    Timber.d(
                                        "🔧 [DEBUG-SAVE] 动作=${updatedExercise.name}, targetSets=${updatedExercise.targetSets.size}",
                                    )
                                    updatedExercise.targetSets.forEachIndexed { index, set ->
                                        Timber.d(
                                            "🔧 [DEBUG-SAVE] 输出组${index + 1}: weight=${set.weight}, reps=${set.reps}, rest=${set.restTimeSeconds}s, id=${set.id}",
                                        )
                                    }

                                    // 通知父组件：单个数据单元已更新
                                    onExerciseUpdate(updatedExercise)

                                    Timber.d("🔧 [DEBUG-SAVE] onWeightChange onExerciseUpdate 调用完成")
                                },
                                onRepsChange = { setId, reps ->
                                    // 🔥 修复数据冲突：使用统一的单个数据单元更新机制
                                    val currentJson = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                                        state.exercise.toJson() // 🔥 关键修复：使用最新的组件状态数据
                                    }
                                    val updatedJson = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.updateExerciseReps(
                                        currentJson,
                                        setId,
                                        reps,
                                    )
                                    val updatedExercise = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                                        fromJson(updatedJson)
                                    } ?: state.exercise // 🔥 修复：使用state.exercise而不是原始exercise参数

                                    // 更新组件状态
                                    state = state.copy(
                                        exercise = updatedExercise,
                                        currentSets = updatedExercise.targetSets,
                                    )

                                    // 🔥 调试日志：验证 onRepsChange 的 onExerciseUpdate 调用
                                    Timber.d(
                                        "🔧 [DEBUG-SAVE] WorkoutExerciseComponent.onRepsChange 即将调用 onExerciseUpdate:",
                                    )
                                    Timber.d("🔧 [DEBUG-SAVE] setId=$setId, reps=$reps")
                                    Timber.d(
                                        "🔧 [DEBUG-SAVE] 动作=${updatedExercise.name}, targetSets=${updatedExercise.targetSets.size}",
                                    )
                                    updatedExercise.targetSets.forEachIndexed { index, set ->
                                        Timber.d(
                                            "🔧 [DEBUG-SAVE] 输出组${index + 1}: weight=${set.weight}, reps=${set.reps}, rest=${set.restTimeSeconds}s, id=${set.id}",
                                        )
                                    }

                                    // 通知父组件：单个数据单元已更新
                                    onExerciseUpdate(updatedExercise)

                                    Timber.d("🔧 [DEBUG-SAVE] onRepsChange onExerciseUpdate 调用完成")
                                },
                                onToggleComplete = { setId ->
                                    // 🔥 修复数据冲突：使用统一的单个数据单元更新机制
                                    val targetSet = state.currentSets.find { it.id == setId }
                                    if (targetSet != null) {
                                        val newCompletionStatus = !targetSet.isCompleted
                                        val currentJson = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                                            state.exercise.toJson() // 🔥 关键修复：使用最新的组件状态数据
                                        }
                                        val updatedJson = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.updateExerciseSetCompletion(
                                            currentJson,
                                            setId,
                                            newCompletionStatus,
                                        )
                                        val updatedExercise = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                                            fromJson(updatedJson)
                                        } ?: state.exercise // 🔥 修复：使用state.exercise而不是原始exercise参数

                                        // 更新组件状态
                                        state = state.copy(
                                            exercise = updatedExercise,
                                            currentSets = updatedExercise.targetSets,
                                        )

                                        // 🔥 调试日志：验证 onToggleComplete 的 onExerciseUpdate 调用
                                        Timber.d(
                                            "🔧 [DEBUG-SAVE] WorkoutExerciseComponent.onToggleComplete 即将调用 onExerciseUpdate:",
                                        )
                                        Timber.d(
                                            "🔧 [DEBUG-SAVE] setId=$setId, completed=$newCompletionStatus",
                                        )
                                        Timber.d(
                                            "🔧 [DEBUG-SAVE] 动作=${updatedExercise.name}, targetSets=${updatedExercise.targetSets.size}",
                                        )
                                        updatedExercise.targetSets.forEachIndexed { index, set ->
                                            Timber.d(
                                                "🔧 [DEBUG-SAVE] 输出组${index + 1}: weight=${set.weight}, reps=${set.reps}, rest=${set.restTimeSeconds}s, completed=${set.isCompleted}, id=${set.id}",
                                            )
                                        }

                                        // 通知父组件：单个数据单元已更新
                                        onExerciseUpdate(updatedExercise)

                                        Timber.d("🔧 [DEBUG-SAVE] onToggleComplete onExerciseUpdate 调用完成")
                                    }
                                },
                                onAddSet = {
                                    // 🔥 WK跟踪：增加组数按钮点击事件
                                    Timber.d("🔥 [WK-ADD-SET] 用户点击增加组数按钮")

                                    // 🔥 修复：添加组操作增强版，支持50组上限并确保数据完整性
                                    val currentSetCount = state.currentSets.size

                                    Timber.d("🔥 [WK-ADD-SET] 当前组数: $currentSetCount")

                                    // 检查组数上限
                                    if (currentSetCount >= 50) {
                                        Timber.w("🔥 [WK-ADD-SET] 已达到最大组数限制: 50组")
                                        Timber.w("🔧 [WorkoutExerciseComponent] 已达到最大组数限制: 50组")
                                        return@ExpandedExerciseView
                                    }

                                    Timber.d("🔥 [WK-ADD-SET] 开始添加新组")
                                    Timber.d(
                                        "🔧 [WorkoutExerciseComponent] onAddSet 开始: 当前组数=$currentSetCount",
                                    )

                                    val lastSet = state.currentSets.lastOrNull()
                                    val newSetId = UUID.randomUUID().toString()

                                    Timber.d("🔥 [WK-ADD-SET] 生成新组ID: $newSetId")

                                    // 🔥 修复：直接创建新组，确保customSets数据完整
                                    val newSet = ExerciseSetDto(
                                        id = newSetId,
                                        weight = lastSet?.weight ?: 0f,
                                        reps = lastSet?.reps ?: 10,
                                        restTimeSeconds = lastSet?.restTimeSeconds ?: 90,
                                        isCompleted = false,
                                        completedAt = null,
                                    )

                                    Timber.d(
                                        "🔥 [WK-ADD-SET] 创建新组: weight=${newSet.weight}, reps=${newSet.reps}, rest=${newSet.restTimeSeconds}s",
                                    )

                                    // 🔥 修复：一次性更新所有相关状态，确保数据一致性
                                    val updatedSets = state.currentSets + newSet
                                    val updatedExercise = state.exercise.copy(targetSets = updatedSets)

                                    // 🔥 新增：详细日志验证新组数据
                                    Timber.d("🔥 [REST-DEBUG] 新组添加前验证:")
                                    updatedSets.forEachIndexed { index, set ->
                                        Timber.d(
                                            "🔥 [REST-DEBUG] 组${index + 1}: rest=${set.restTimeSeconds}s, id=${set.id}",
                                        )
                                    }

                                    state = state.copy(
                                        currentSets = updatedSets,
                                        exercise = updatedExercise,
                                    )

                                    Timber.d("🔥 [WK-ADD-SET] 更新组件状态: 新组数=${updatedSets.size}")
                                    Timber.d(
                                        "🔥 [WK-ADD-SET] 同步exercise数据: targetSets=${updatedExercise.targetSets.size}",
                                    )

                                    // 🔥 新增：验证状态一致性
                                    Timber.d(
                                        "🔥 [WK-ADD-SET] 状态验证: currentSets=${state.currentSets.size}, exercise.targetSets=${state.exercise.targetSets.size}",
                                    )

                                    // 🔥 详细日志记录添加的新组信息
                                    Timber.d(
                                        "🔧 [DEBUG-SAVE] WorkoutExerciseComponent.onAddSet 成功添加组:",
                                    )
                                    Timber.d("🔧 [DEBUG-SAVE] 新组数: ${updatedSets.size}")
                                    Timber.d(
                                        "🔧 [DEBUG-SAVE] 动作=${updatedExercise.name}, targetSets=${updatedExercise.targetSets.size}",
                                    )
                                    updatedExercise.targetSets.forEachIndexed { index, set ->
                                        Timber.d(
                                            "🔧 [DEBUG-SAVE] 组${index + 1}: weight=${set.weight}, reps=${set.reps}, rest=${set.restTimeSeconds}s, id=${set.id}",
                                        )
                                    }

                                    // 通知父组件更新
                                    Timber.d("🔥 [WK-ADD-SET] 准备通知父组件更新")
                                    onExerciseUpdate(updatedExercise)
                                    Timber.d("🔥 [WK-ADD-SET] 父组件更新通知完成")

                                    Timber.d(
                                        "🔧 [WorkoutExerciseComponent] onAddSet 添加组完成: 新组数=${updatedSets.size}",
                                    )
                                    Timber.d("🔥 [WK-ADD-SET] 增加组数操作完成成功")
                                },
                                onRestTimeChange =
                                if (mode == ExerciseComponentMode.TEMPLATE) {
                                    { newRestTime ->
                                        state =
                                            state.copy(
                                                exercise = state.exercise.copy(restTimeSeconds = newRestTime),
                                            )
                                        onRestTimeChange?.invoke(newRestTime)
                                        onExerciseUpdate(state.exercise)
                                    }
                                } else {
                                    null
                                },
                                onShowKeypad = showKeypadInternal, // 🔥 传递内部 Keypad 处理函数
                                onBatchModifyCallback = null, // 🔥 移除批量修改回调，改为在 keypad 中直接处理
                                currentEditingField = currentEditingField, // 🔥 传递当前编辑字段状态
                                keypadInputValue = keypadInputValue, // 🔥 传递键盘输入缓冲值
                            )
                        }
                    }
                }
            }
        }

        // 🔥 Bottom Sheet Keypad 显示逻辑 - 独立的底部弹出键盘
        if (showKeypad) {
            ModalBottomSheet(
                onDismissRequest = { hideKeypadInternal() },
                modifier = Modifier.fillMaxWidth(),
                containerColor = MaterialTheme.workoutColors.cardBackground,
                contentColor = MaterialTheme.workoutColors.accentSecondary,
                dragHandle = {
                    Surface(
                        modifier = Modifier
                            .width(32.dp)
                            .height(4.dp),
                        color = MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.4f),
                        shape = RoundedCornerShape(2.dp),
                    ) {}
                },
            ) {
                // 纯数字键盘组件 - 无任何显示栏
                com.example.gymbro.features.workout.shared.components.keypad.WorkoutKeypad(
                    onKeyPressed = { key ->
                        when (key) {
                            NumericInputLogic.KeypadKey.OK -> {
                                // 完成键：关闭键盘
                                hideKeypadInternal()
                            }
                            NumericInputLogic.KeypadKey.DEL -> {
                                // 删除键：删除最后一个字符，立即更新原始输入框
                                if (keypadInputValue.isNotEmpty()) {
                                    keypadInputValue = keypadInputValue.dropLast(1)
                                    updateTargetFieldDirectly(keypadInputValue)
                                }
                            }
                            NumericInputLogic.KeypadKey.CLEAR -> {
                                // 清空键：清空输入，立即更新原始输入框
                                keypadInputValue = ""
                                updateTargetFieldDirectly(keypadInputValue)
                            }
                            // 🔥 修复：智能小数点处理逻辑
                            NumericInputLogic.KeypadKey.DOT -> {
                                // 小数点键：仅在重量字段且没有小数点时添加
                                if (currentEditingField?.fieldType == KeypadContract.InputFieldType.WEIGHT &&
                                    !keypadInputValue.contains(".")
                                ) {
                                    // 如果输入为空，添加"0."
                                    keypadInputValue = if (keypadInputValue.isEmpty()) {
                                        "0."
                                    } else {
                                        // 在当前输入后添加小数点
                                        "$keypadInputValue."
                                    }
                                    updateTargetFieldDirectly(keypadInputValue)
                                }
                            }
                            else -> {
                                // 数字键：直接追加到输入值，立即更新原始输入框
                                val digit = when (key) {
                                    NumericInputLogic.KeypadKey.D0 -> "0"
                                    NumericInputLogic.KeypadKey.D1 -> "1"
                                    NumericInputLogic.KeypadKey.D2 -> "2"
                                    NumericInputLogic.KeypadKey.D3 -> "3"
                                    NumericInputLogic.KeypadKey.D4 -> "4"
                                    NumericInputLogic.KeypadKey.D5 -> "5"
                                    NumericInputLogic.KeypadKey.D6 -> "6"
                                    NumericInputLogic.KeypadKey.D7 -> "7"
                                    NumericInputLogic.KeypadKey.D8 -> "8"
                                    NumericInputLogic.KeypadKey.D9 -> "9"
                                    else -> ""
                                }
                                if (digit.isNotEmpty()) {
                                    // 🔥 修复：小数点后最多1位数字的逻辑
                                    val shouldAddDigit = if (keypadInputValue.contains(".")) {
                                        // 如果已经有小数点，检查小数点后是否已经有1位数字
                                        val decimalPart = keypadInputValue.substringAfter(".")
                                        decimalPart.isEmpty() // 只有当小数点后没有数字时才允许添加
                                    } else {
                                        // 没有小数点，直接允许添加
                                        true
                                    }

                                    if (shouldAddDigit && keypadInputValue.length < 10) {
                                        keypadInputValue += digit
                                        updateTargetFieldDirectly(keypadInputValue)
                                    }
                                }
                            }
                        }
                        Timber.d("🔧 [WorkoutExerciseComponent] 键盘输入: $keypadInputValue -> 直接更新原始输入框")
                    },
                    onQuickModify = { direction ->
                        // 🔥 修复：整列复制 - 将当前组数据复制到所有上面或下面的组
                        currentEditingField?.let { field ->
                            Timber.d(
                                "🔧 [WorkoutExerciseComponent] 整列复制: fieldType=${field.fieldType}, direction=$direction, 当前组=${field.setIndex + 1}",
                            )

                            try {
                                val currentSets = state.currentSets.toMutableList()
                                if (currentSets.isNotEmpty() && field.setIndex < currentSets.size) {
                                    val currentSet = currentSets[field.setIndex]

                                    // 确定要复制的目标组范围
                                    val targetIndices = when (direction) {
                                        KeypadContract.ModifyDirection.UP -> {
                                            // 向上：复制到所有上面的组 (0 到 currentIndex-1)
                                            (0 until field.setIndex).toList()
                                        }
                                        KeypadContract.ModifyDirection.DOWN -> {
                                            // 向下：复制到所有下面的组 (currentIndex+1 到 最后)
                                            ((field.setIndex + 1) until currentSets.size).toList()
                                        }
                                    }

                                    if (targetIndices.isNotEmpty()) {
                                        // 批量复制到目标组
                                        targetIndices.forEach { targetIndex ->
                                            val targetSet = currentSets[targetIndex]

                                            // 根据字段类型复制对应的数据
                                            val updatedTargetSet = when (field.fieldType) {
                                                KeypadContract.InputFieldType.WEIGHT -> {
                                                    targetSet.copy(weight = currentSet.weight)
                                                }
                                                KeypadContract.InputFieldType.REPS -> {
                                                    targetSet.copy(reps = currentSet.reps)
                                                }
                                                KeypadContract.InputFieldType.REST_TIME -> {
                                                    targetSet.copy(
                                                        restTimeSeconds = currentSet.restTimeSeconds,
                                                    )
                                                }
                                            }

                                            // 更新目标组
                                            currentSets[targetIndex] = updatedTargetSet
                                        }

                                        // 🔥 修复：同时更新 state 和通知父组件
                                        state = state.copy(currentSets = currentSets)

                                        // 🔥 关键修复：使用state.exercise而不是原始exercise参数，确保数据一致性
                                        val updatedExercise = state.exercise.copy(
                                            targetSets = currentSets,
                                            restTimeSeconds = currentSets.firstOrNull()?.restTimeSeconds ?: state.exercise.restTimeSeconds,
                                        )

                                        // 🔥 新增：使用JSON处理器确保数据完整性
                                        try {
                                            val currentJson = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                                                updatedExercise.toJson()
                                            }
                                            val finalExercise = com.example.gymbro.features.workout.json.processor.ExerciseJsonProcessor.run {
                                                fromJson(currentJson)
                                            } ?: updatedExercise

                                            // 更新组件状态以确保一致性
                                            state = state.copy(
                                                exercise = finalExercise,
                                                currentSets = finalExercise.targetSets,
                                            )

                                            onExerciseUpdate(finalExercise)
                                        } catch (e: Exception) {
                                            Timber.e(e, "🔧 [WorkoutExerciseComponent] 整列复制JSON处理失败，使用降级方案")
                                            onExerciseUpdate(updatedExercise)
                                        }

                                        val directionText = if (direction == KeypadContract.ModifyDirection.UP) "上面所有组" else "下面所有组"
                                        val fieldText = when (field.fieldType) {
                                            KeypadContract.InputFieldType.WEIGHT -> "重量${currentSet.weight}kg"
                                            KeypadContract.InputFieldType.REPS -> "次数${currentSet.reps}次"
                                            KeypadContract.InputFieldType.REST_TIME -> "休息${currentSet.restTimeSeconds}秒"
                                        }
                                        Timber.d(
                                            "🔧 [WorkoutExerciseComponent] 整列复制成功: 从第${field.setIndex + 1}组复制${fieldText}到$directionText(共${targetIndices.size}组)",
                                        )
                                    } else {
                                        val directionText = if (direction == KeypadContract.ModifyDirection.UP) "上面" else "下面"
                                        Timber.w(
                                            "🔧 [WorkoutExerciseComponent] 无可复制的目标组: 当前第${field.setIndex + 1}组，${directionText}没有其他组",
                                        )
                                    }
                                }
                            } catch (e: Exception) {
                                Timber.e(e, "🔧 [WorkoutExerciseComponent] 整列复制失败")
                            }

                            // 关闭键盘，因为复制后不需要继续编辑
                            hideKeypadInternal()
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Tokens.Spacing.Medium),
                )
            }
        }
    }
}

@GymBroPreview
@Composable
private fun WorkoutExerciseComponentSessionAwarePreview() {
    val sampleExercise = ExerciseDto(
        id = "exercise1",
        name = "卧推",
        targetSets = listOf(
            ExerciseSetDto(
                id = "set1",
                weight = 50.0f,
                reps = 12,
                isCompleted = false,
            ),
            ExerciseSetDto(
                id = "set2",
                weight = 50.0f,
                reps = 10,
                isCompleted = false,
            ),
        ),
        restTimeSeconds = 60,
        notes = "",
    )

    GymBroTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        ) {
            Text(
                text = "Session状态感知的WorkoutExerciseComponent",
                style = MaterialTheme.typography.titleMedium,
            )

            // 当前训练状态 - 自动展开
            WorkoutExerciseComponent(
                exercise = sampleExercise,
                exerciseState = SessionExerciseStateFactory.current(),
                onExerciseComplete = {},
                onExerciseUpdate = {},
                onDisplayModeChange = {},
                onRestTimerStart = {},
            )

            // 已完成状态 - 保持紧凑
            WorkoutExerciseComponent(
                exercise = sampleExercise,
                exerciseState = SessionExerciseStateFactory.completed(),
                onExerciseComplete = {},
                onExerciseUpdate = {},
                onDisplayModeChange = {},
                onRestTimerStart = {},
            )
        }
    }
}

@GymBroPreview
@Composable
private fun WorkoutExerciseComponentExpandedPreview() {
    val sampleExercise = ExerciseDto(
        id = "exercise1",
        name = "卧推",
        targetSets = listOf(
            ExerciseSetDto(
                id = "set1",
                weight = 50.0f,
                reps = 12,
                isCompleted = false,
            ),
            ExerciseSetDto(
                id = "set2",
                weight = 50.0f,
                reps = 10,
                isCompleted = true,
                completedAt = 1640995200000,
            ),
        ),
        restTimeSeconds = 60,
        notes = "",
    )

    GymBroTheme {
        WorkoutExerciseComponent(
            exercise = sampleExercise,
            initialDisplayMode = ExerciseDisplayMode.EXPANDED,
            onExerciseComplete = {},
            onExerciseUpdate = {},
            onDisplayModeChange = {},
        )
    }
}

@GymBroPreview
@Composable
private fun WorkoutExerciseComponentDecimalEditPreview() {
    val sampleExercise = ExerciseDto(
        id = "exercise1",
        name = "杠铃卧推",
        targetSets = listOf(
            ExerciseSetDto(
                id = "set1",
                weight = 52.5f, // 🔥 演示：包含小数点的重量
                reps = 12,
                isCompleted = false,
            ),
            ExerciseSetDto(
                id = "set2",
                weight = 55.0f, // 🔥 演示：整数重量
                reps = 10,
                isCompleted = false,
            ),
        ),
        restTimeSeconds = 90,
        notes = "重量编辑演示：点击重量字段时默认从个位数开始编辑",
    )

    GymBroTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        ) {
            Text(
                text = "小数点编辑逻辑演示",
                style = MaterialTheme.typography.titleMedium,
            )

            Text(
                text = "• 点击52.5kg -> 键盘显示'52'\n• 点击55kg -> 键盘显示'55'\n• 需要小数点时手动点击'.'键",
                style = MaterialTheme.typography.bodySmall,
            )

            WorkoutExerciseComponent(
                exercise = sampleExercise,
                mode = ExerciseComponentMode.TEMPLATE,
                initialDisplayMode = ExerciseDisplayMode.EXPANDED,
                onExerciseComplete = {},
                onExerciseUpdate = {},
                onDisplayModeChange = {},
                onRestTimeChange = { newTime ->
                    WorkoutLogUtils.Exercise.debug("休息时间更新: ${newTime}秒")
                },
            )
        }
    }
}
