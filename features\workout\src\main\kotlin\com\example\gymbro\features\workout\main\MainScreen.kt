package com.example.gymbro.features.workout.main

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.base.scaffold.WorkoutScaffold
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.workout.calendar.internal.components.CalendarSection
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.main.internal.components.*
import kotlinx.coroutines.launch
import kotlinx.datetime.LocalDate

/**
 * 训练主屏幕 v2.1 - 嵌套滚动修复版
 *
 * 🎯 重构要点:
 * - 使用GymBroScaffold统一布局，集成主题系统
 * - 组件拆分为internal/components，使用designSystem通用组件
 * - 优化MVI架构，减少重组和提升性能
 * - 集成MotionSpecs.Workout动画规格
 * - 遵循UI统一4.0规范
 * - ✅ 修复嵌套滚动容器冲突问题（Column+verticalScroll → LazyColumn+item）
 * - ✅ 集成日历功能
 * - ✅ 符合Box+LazyColumn+Surface设计规范
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun HomeScreen(
    onNavigateToSession: (String) -> Unit,
    onNavigateToTemplates: () -> Unit,
    onNavigateToPlans: () -> Unit,
    onNavigateToStats: () -> Unit,
    onNavigateToExerciseLibrary: () -> Unit,
    onNavigateToCalendar: () -> Unit, // 🆕 导航到专用日历界面
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: HomeViewModel = hiltViewModel(),
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val hapticFeedback = LocalHapticFeedback.current

    // 🎯 State投射 - 减少不必要的重组
    val isLoading by remember { derivedStateOf { state.isLoading } }
    val hasError by remember { derivedStateOf { state.errorCode != null } }
    val selectedDate by remember { derivedStateOf { state.selectedDate } }

    // 🎯 MVI Effect处理 - 标准模式，防止重复触发
    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            WorkoutLogUtils.Template.debug("🎬 收到 Effect: ${effect::class.simpleName}, isLoading=$isLoading")
            when (effect) {
                // ✅ 数据加载相关Effect - 由EffectHandler处理，UI层不需要处理
                is HomeContract.Effect.LoadInitialDataEffect,
                is HomeContract.Effect.LoadSpecificDateDataEffect,
                is HomeContract.Effect.StartQuickWorkoutEffect,
                is HomeContract.Effect.StartWithTemplateEffect,
                -> {
                    // 这些Effect由EffectHandler处理，UI层不需要额外操作
                    WorkoutLogUtils.Template.debug("📋 数据加载Effect，由EffectHandler处理")
                }

                // 导航相关Effect
                is HomeContract.Effect.NavigateToActiveWorkout -> {
                    WorkoutLogUtils.Template.debug("🚀 NavigateToActiveWorkout, isLoading=$isLoading")
                    if (!isLoading) { // 🔧 修复：防止加载时重复导航
                        WorkoutLogUtils.Template.debug("✅ 执行导航到ActiveWorkout")
                        onNavigateToSession(effect.sessionId)
                    } else {
                        WorkoutLogUtils.Template.warn("❌ 导航被阻止，因为isLoading=true")
                    }
                }
                is HomeContract.Effect.NavigateToTemplates -> {
                    WorkoutLogUtils.Template.debug("🚀 NavigateToTemplates, isLoading=$isLoading")
                    if (!isLoading) { // 🔧 修复：防止加载时重复导航
                        WorkoutLogUtils.Template.debug("✅ 执行导航到Templates")
                        onNavigateToTemplates()
                    } else {
                        WorkoutLogUtils.Template.warn("❌ 导航被阻止，因为isLoading=true")
                    }
                }
                is HomeContract.Effect.NavigateToStats -> {
                    WorkoutLogUtils.Template.debug("🚀 NavigateToStats, isLoading=$isLoading")
                    if (!isLoading) { // 🔧 修复：防止加载时重复导航
                        WorkoutLogUtils.Template.debug("✅ 执行导航到Stats")
                        onNavigateToStats()
                    } else {
                        WorkoutLogUtils.Template.warn("❌ 导航被阻止，因为isLoading=true")
                    }
                }
                is HomeContract.Effect.NavigateToPlans -> {
                    WorkoutLogUtils.Template.debug("🚀 NavigateToPlans, isLoading=$isLoading")
                    if (!isLoading) { // 🔧 修复：防止加载时重复导航
                        WorkoutLogUtils.Template.debug("✅ 执行导航到Plans")
                        onNavigateToPlans()
                    } else {
                        WorkoutLogUtils.Template.warn("❌ 导航被阻止，因为isLoading=true")
                    }
                }
                is HomeContract.Effect.NavigateToExerciseLibrary -> {
                    WorkoutLogUtils.Template.debug("🚀 NavigateToExerciseLibrary, isLoading=$isLoading")
                    if (!isLoading) { // 🔧 修复：防止加载时重复导航
                        WorkoutLogUtils.Template.debug("✅ 执行导航到ExerciseLibrary")
                        onNavigateToExerciseLibrary()
                    } else {
                        WorkoutLogUtils.Template.warn("❌ 导航被阻止，因为isLoading=true")
                    }
                }
                is HomeContract.Effect.NavigateToCalendar -> {
                    if (!isLoading) { // 🔧 修复：防止加载时重复导航
                        onNavigateToCalendar()
                    }
                }
                is HomeContract.Effect.NavigateToSubscription -> {
                    // TODO: 实现订阅页面导航
                }

                // UI反馈相关Effect
                is HomeContract.Effect.ShowSnackbar -> {
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar(
                            message = effect.messageCode, // 暂时简化，后续可改为stringResource
                        )
                    }
                }
                is HomeContract.Effect.HapticFeedback -> {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                }
                is HomeContract.Effect.ShowTemplateDialog -> {
                    // TODO: 添加模板选择对话框
                }
                is HomeContract.Effect.ShowSaveTemplateDialog -> {
                    // TODO: 添加保存模板对话框
                }
            }
        }
    }

    // 🎯 使用WorkoutScaffold替代自定义布局
    // 🔧 修复：集成LOGO显示和订阅功能
    WorkoutScaffold(
        title = UiText.DynamicString("今日训练"),
        onNavigateBack = onNavigateBack,
        isLoading = isLoading,
        snackbarHostState = snackbarHostState,
        modifier = modifier,
    ) { paddingValues ->
        // 🎯 主要内容 - 使用组件拆分
        HomeMainContent(
            state = state,
            paddingValues = paddingValues,
            onStartWorkout = {
                viewModel.dispatch(HomeContract.Intent.StartQuickWorkout)
            },
            onTemplatesClick = {
                viewModel.dispatch(HomeContract.Intent.NavigateToTemplates)
            },
            onPlansClick = {
                viewModel.dispatch(HomeContract.Intent.NavigateToPlans)
            },
            onStatsClick = {
                viewModel.dispatch(HomeContract.Intent.NavigateToStats)
            },
            onLibraryClick = {
                viewModel.dispatch(HomeContract.Intent.NavigateToExerciseLibrary)
            },
            onDateSelected = { date ->
                viewModel.dispatch(HomeContract.Intent.SelectDate(date))
            },
            onCalendarClick = {
                // 🔧 修复：添加触觉反馈和状态检查
                if (!isLoading) {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                    viewModel.dispatch(HomeContract.Intent.NavigateToCalendar)
                }
            },
            onRefresh = {
                viewModel.dispatch(HomeContract.Intent.RefreshData)
            },
            onNavigateToSession = onNavigateToSession,
        )
    }
}

@Suppress("ktlint:standard:function-naming")
/**
 * 主页内容组件 - Box+LazyColumn+Surface规范布局
 *
 * ✅ 修复：使用LazyColumn+item()替代Column+verticalScroll，解决嵌套滚动冲突
 * ✅ 功能：开始训练=session、动作库=exercise-library、训练模板=template、训练计划=plan、统计功能
 * ✅ 集成：日历功能完整集成
 */
@Composable
private fun HomeMainContent(
    state: HomeContract.State,
    paddingValues: PaddingValues,
    onStartWorkout: () -> Unit,
    onTemplatesClick: () -> Unit,
    onPlansClick: () -> Unit,
    onStatsClick: () -> Unit,
    onLibraryClick: () -> Unit,
    onDateSelected: (LocalDate) -> Unit,
    onCalendarClick: () -> Unit, // 🆕 导航到专用日历界面的回调
    onRefresh: () -> Unit,
    onNavigateToSession: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(paddingValues),
        contentPadding = PaddingValues(horizontal = Tokens.Spacing.Medium),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
    ) {
        // 1. ✅ Session - 开始训练
        item {
            QuickStartCard(
                onStartWorkout = onStartWorkout,
                isLoading = state.isLoading, // 🔧 修复：使用正确的加载状态
            )
        }

        // 2. ✅ 日历功能集成 - 增强版（唯一实现）
        item {
            CalendarSection(
                calendarEntries = state.calendarEntries,
                totalCompletedWeight = state.totalCompletedWeight,
                completedTrainingDays = state.completedTrainingDays,
                totalTrainingDays = state.totalTrainingDays,
                selectedDate = state.selectedDate,
                isLoading = state.isLoadingCalendar,
                onDateSelected = onDateSelected,
                onCalendarClick = onCalendarClick, // 🆕 传递日历点击回调
            )
        }

        // 3. ✅ Template - 训练模板
        item {
            TemplateActionCard(
                onTemplatesClick = onTemplatesClick,
            )
        }

        // 4. ✅ Plan - 训练计划
        item {
            PlanActionCard(
                onPlansClick = onPlansClick,
            )
        }

        // 5. ✅ 动作库
        item {
            LibraryActionCard(
                onLibraryClick = onLibraryClick,
            )
        }

        // 6. ✅ Stats - 训练统计
        item {
            TrainingStatsSection(
                userStats = state.statistics,
                unifiedStats = null,
                isLoading = state.isLoadingStats,
                onStatsClick = onStatsClick,
            )
        }

        // ✅ 最近训练会话（保留在底部）
        item {
            RecentSessionsCard(
                sessions = state.recentSessions,
                isLoading = state.isLoading,
                onSessionClick = { sessionId ->
                    // 🔧 修复：正确导航到训练会话详情
                    onNavigateToSession(sessionId)
                },
            )
        }
    }
}

// ===== Preview Functions =====

@GymBroPreview
@Composable
private fun HomeScreenPreview() {
    GymBroTheme {
        Surface {
            HomeScreen(
                onNavigateToSession = { },
                onNavigateToTemplates = { },
                onNavigateToPlans = { },
                onNavigateToStats = { },
                onNavigateToExerciseLibrary = { },
                onNavigateToCalendar = { },
                onNavigateBack = { },
            )
        }
    }
}
