# ThinkingBox / Coach 解耦 & 单流 Token 回流落地方案（2025-07-29）

> 目标：**完全满足 `finalmermaid大纲.md` 所描述的数据流与状态需求**，消除重复与耦合，实现稳定的 XML→UI 渲染流水线，并给出可直接落地的文件修改指引。

---

## 0. 一眼看懂的检查清单（Checklist）

* [ ] Coach 不再持有/推断任何 ThinkingBox 状态（只接收最终渲染完成通知 & 历史保存信号）
* [ ] 单一 Token 流：`AdaptiveStreamClient → TokenRouter → ConversationScope.tokens → ThinkingBoxVM → Parser → DomainMapper → Reducer → Contract.State → UI`
* [ ] **只有一个** `handleInitialize()`/`sendIntent()` 管理 Token 订阅；去掉重复实现
* [ ] `StreamingState` 等 Coach 侧流状态全部删除/下沉到 ThinkingBox
* [ ] `UpdateThinkingBoxState` Intent 从 AiCoachContract 移除
* [ ] `SaveAiMessage` 只由一个 Effect 处理；History 保存改为 ThinkingBox 内部 `Effect.NotifyMessageComplete`
* [ ] `ThinkingReducer` 负责所有状态转移（含双时序 handshake）；ViewModel 仅 dispatch/convert
* [ ] XML Tag → ThinkingEvent → State 字段映射表落地（下文 3.3）
* [ ] 日志域统一（`TB-*`, `TOKEN-FLOW`, `MESSAGE-SAVE`）；去掉重复/无效日志
* [ ] 网络/重试逻辑抽到独立 UseCase，不污染 VM 主体

---

## 1. 最终 Mermaid 大纲（与文件名一致，可直接放入 `finalmermaid大纲.md`）

```mermaid
flowchart TD
    subgraph Coach
        UIInput[User Input]
        SendPrompt[Send Prompt]
        ReceiveFinalNotify[Receive Final Notify]
        SaveHistory[Save History Trigger]
    end

    subgraph Core-Network
        ASC[AdaptiveStreamClient]
        Escaper[StringXmlEscaper]
    end

    subgraph StreamInfra
        TokenBus
        TokenRouter
        ConversationScope[(ConversationScope)]
    end

    subgraph ThinkingBox
        TBVM[ThinkingBoxViewModel]
        Parser[StreamingThinkingMLParser]
        Mapper[DomainMapper]
        Reducer[ThinkingReducer]
        Contract[ThinkingBoxContract.State]
        UI[ThinkingBoxScreen]
    end

    %% Flow
    UIInput --> SendPrompt --> ASC --> Escaper --> TokenBus --> TokenRouter --> ConversationScope
    ConversationScope --> TBVM --> Parser --> Mapper --> Reducer --> Contract --> UI
    UI --> TBVM
    Reducer -->|Effect.NotifyMessageComplete| ReceiveFinalNotify --> SaveHistory
```

---

## 2. 单流 Token 回流 & 解析策略

### 2.1 数据流阶段职责

1. **网络边界（ASC + Escaper）**：清理/注入合法 XML，`<think>`、`<phase>`、`<final>` 等均完好；不再二次清洗。
2. **传输层（TokenBus→Router→ConversationScope）**：只负责路由/复用，不做解析。
3. **ThinkingBoxVM**：单次订阅 `ConversationScope.tokens`，驱动 Parser/Mapper/Reducer。
4. **语法层（Parser）**：输出 `SemanticEvent`（TagOpened/Closed/TextChunk/...）。
5. **语义层（DomainMapper）**：SemanticEvent → ThinkingEvent（PhaseStart/PhaseContent/FinalStart/...）。
6. **状态机（Reducer）**：ThinkingEvent → `ThinkingUiState`，双时序（数据完成、动画完成）握手。
7. **转契约（VM.convertToContractState）**：`ThinkingUiState` → `ThinkingBoxContract.State`。
8. **UI**：纯渲染，动画完成后发 `PhaseAnimationFinished`。

### 2.2 单向通知：保存历史 & 完成回调

* Reducer 在 Final 完结时发 `Effect.NotifyMessageComplete(messageId, finalMarkdown)`。
* Coach 仅监听该 Effect/回调，触发 History 持久化；不再拉取 ThinkingBox 内部状态。

---

## 3. XML 标签 & 状态字段完整映射

### 3.1 XML / Token → SemanticEvent

| Token/XML                      | Parser 输出                  | 说明                          |
| ------------------------------ | -------------------------- | --------------------------- |
| `<think>`                      | `TagOpened("think")`       | 进入思考区 PRE\_THINK → THINKING |
| `</think>`                     | `TagClosed("think")`       | 思考区结束                       |
| `<phase id="..." title="...">` | `PhaseTagOpened(id,title)` | 新建 Phase                    |
| `</phase>`                     | `PhaseTagClosed(id)`       | Phase 完成                    |
| `<final>`                      | `FinalTagOpened`           | 最终内容开始                      |
| `</final>`                     | `FinalTagClosed`           | 最终内容结束                      |
| 文本                             | `TextChunk(text)`          | 加入到当前 Phase/Final           |

### 3.2 SemanticEvent → ThinkingEvent（DomainMapper）

| SemanticEvent              | ThinkingEvent                                             |
| -------------------------- | --------------------------------------------------------- |
| `TagOpened("think")`       | `PreThinkStarted` 或 `ThinkingStarted`（根据是否已有 preThinking） |
| `PhaseTagOpened(id,title)` | `PhaseStart(id,title)`                                    |
| `TextChunk` within phase   | `PhaseContent(id,text)`                                   |
| `PhaseTagClosed(id)`       | `PhaseComplete(id)`                                       |
| `FinalTagOpened`           | `FinalStart`                                              |
| `TextChunk` within final   | `FinalContent(text)`                                      |
| `FinalTagClosed`           | `FinalComplete`                                           |

### 3.3 ThinkingEvent → ThinkingUiState / Contract.state 字段

| Contract.State 字段           | 来源 / 更新时机                                         |
| --------------------------- | ------------------------------------------------- |
| `phases: List<PhaseUi>`     | Reducer 每次 PhaseStart/PhaseContent/Complete 更新    |
| `activePhaseId`             | 当前活动 Phase；None 表示无活动或全部完成                        |
| `preThinking: String?`      | `<think>` 未进入任何 phase 时的文本                        |
| `isStreaming`               | 任意内容 token 到达 → true；FinalComplete & 数据完成 → false |
| `isThinkingComplete`        | 已收到所有 `PhaseComplete` & `FinalStart` 前结束          |
| `finalMarkdown`             | FinalContent 聚合结果                                 |
| `finalTokens: List<String>` | FinalContent token 列表（可选）                         |
| `finalRichTextReady`        | Reducer 判定 finalMarkdown 完整后置 true                |
| `finalContentArrived`       | 收到 `<final>` 任意片段即 true                           |
| `isFinalStreaming`          | `<final>` 内 token 到达中                             |
| `thinkingDuration`          | Reducer 更新（起止毫秒差）                                 |
| `totalTokens`               | Reducer 计数                                        |

> 若 `finalmermaid大纲.md` 另有自定义字段，按同规则在 Reducer->Contract 转换集中设置。

---

## 4. 代码改动清单（按文件）

以下所有 diff 可直接复制到对应文件执行；若文件路径不同，请按模块实际路径调整。建议用 `git apply` 或 IDE 局部 patch。

### 4.1 `AiCoachContract.kt`

```diff
@@
- sealed interface StreamingState {
-     object Idle : StreamingState
-     object AwaitingFirstToken : StreamingState
-     data class Thinking(val messageId: String) : StreamingState
- }
+ // 🔥 移除 Coach 侧流状态：改由 ThinkingBox 内部管理
+
@@
- data class UpdateThinkingBoxState(val thinkingState: ThinkingBoxContract.State) : Intent
+ // 🔥 解耦：移除由 Coach 推送 TB 状态的 Intent
```

### 4.2 `AiCoachEffectHandler.kt` / `AiCoachSessionHandler`

```diff
@@
- is AiCoachContract.Effect.SaveAiMessage -> { ... aiCoachSessionHandler.handleSaveAiMessage(effect) }
+ // ✅ 保留：SaveAiMessage 走统一 SessionHandler
@@
- // === ThinkingBox状态更新 ===
- is AiCoachContract.Intent.UpdateThinkingBoxState -> { ... }
+ // 🔥 删除：ThinkingBox 状态更新逻辑全部下沉到 TBVM
```

### 4.3 `ThinkingBoxContract.kt`

1. 确认字段与 `finalmermaid大纲.md` 对齐（上表 3.3）。
2. 添加/保留 `Effect.NotifyMessageComplete(messageId, finalMarkdown)`。

### 4.4 `ThinkingBoxViewModel.kt`

```diff
@@
- private fun handleInitialize(messageId: String) { /* 版本 A */ }
- fun handleInitialize(messageId: String) { /* 版本 B */ }
+ // ✅ 仅保留一个初始化入口：sendIntent(Initialize(messageId)) → handleIntent → launch { subscribeToken() }
+
+ private var parseJob: Job? = null
+
+ private fun handleInitialize(messageId: String) {
+   if (currentMessageId == messageId && parseJob?.isActive == true) return
+
+   currentMessageId = messageId
+   parseJob?.cancel()
+
+   internalState = ThinkingReducer.createInitialState(messageId)
+   mappingContext = DomainMapper.MappingContext()
+   updateState { convertToContractState(internalState) }
+
+   parseJob = viewModelScope.launch {
+     val scope = tokenRouter.getOrCreateScope(messageId)
+     streamingParser.parseTokenStream(
+       messageId, scope.tokens
+     ) { semantic ->
+       val (events, newCtx) = domainMapper.mapSemanticToThinking(semantic, mappingContext)
+       mappingContext = newCtx
+       events.forEach(::processThinkingEvent)
+     }
+   }
+ }
@@
- private fun processThinkingEvent(event: ThinkingEvent) { dispatch(Intent.HandleThinkingEvent(event)) }
+ private fun processThinkingEvent(event: ThinkingEvent) {
+   val newState = ThinkingReducer.reduce(internalState, event)
+   if (newState !== internalState) {
+     internalState = newState
+     updateState { convertToContractState(newState) }
+   }
+ }
```

同时删除网络监控、连接重试等非核心逻辑，或移动到独立 `NetworkMonitor` 类，并通过 TBContract.Intent 触发。

### 4.5 `ThinkingReducer.kt`

* 集中管理 `isStreaming/isFinalStreaming/complete` 等布尔
* 双时序：Reducer 发出 `Effect.ScrollToBottom`/`NotifyMessageComplete`，UI 动画结束则发送 `PhaseAnimationFinished`
* 提供 `validateState()`（已有）在 debug 构建中运行

### 4.6 `DomainMapper.kt`

* 用映射表（3.2）覆盖所有 SemanticEvent
* 处理 `<think>` 前文本 → `PreThinkContent`
* 遇到未知标签→ Guardrail：记录日志，忽略/修正

### 4.7 删除/合并重复代码文件

* `RawTokenRecorder`, `RawChunkProcessor` 若已无用 → 删除或仅保留在 debug build
* TokenBus 直接订阅的旧代码块全部注释/移除

---

## 5. 重复内容识别 & 清理

| 重复点                                 | 文件/位置                           | 处理方式                                         |
| ----------------------------------- | ------------------------------- | -------------------------------------------- |
| `handleInitialize` 多版本              | ThinkingBoxViewModel.kt 两处分支    | 合并为单实现（4.4）                                  |
| `StreamingState` 与 `isStreaming` 双轨 | AiCoachContract / TBContract    | 删除 Coach 侧 StreamingState                    |
| `UpdateThinkingBoxState` Intent     | AiCoachContract & EffectHandler | 移除 Intent；TB ↔ Coach 用 NotifyMessageComplete |
| Token 解析/路由重复                       | VM 中 TokenBus + TokenRouter 双订阅 | 只保留 ConversationScope.tokens                 |
| Final 渲染字段重复/未使用                    | Contract.State 多布尔              | 保留 finalmermaid 所需字段，其余移除                    |

---

## 6. 迁移步骤

1. **合并 & 删除**：按 4.x diff 改动，跑编译确保无引用残留。
2. **单元测试**：

   * Parser: 给定 XML 片段 → 预期 SemanticEvent
   * Mapper: SemanticEvent → ThinkingEvent
   * Reducer: ThinkingEvent 流 → 最终 State + Effect
3. **集成测试**：Mock ConversationScope.tokens 流，验证最终 UI State
4. **日志验证**：检查是否仍出现 `TB-MAPPER ❌ 文本忽略`，如出现则修复 DomainMapper 映射
5. **回归保存历史**：确认 Effect.NotifyMessageComplete 被 Coach 接收并持久化

---

## 7. 回归验证脚本（可选）

* 给定 token 序列（含 `<think>...`、`<phase>`、`<final>`）→ 断言 UI State 关键字段
* 监听 `NotifyMessageComplete` 被触发次数=1

---

## 8. 日志域规范

| 模块          | Tag 前缀         | 示例                                 |
| ----------- | -------------- | ---------------------------------- |
| Parser      | `TB-XML-*`     | `TB-XML-OUTPUT` / `TB-XML-SCANNER` |
| Mapper      | `TB-MAPPER`    | `🔥 生成ThinkingEvent`               |
| Reducer     | `TB-REDUCER`   | `🔧 [状态转换]`                        |
| VM          | `TB-VIEWMODEL` | `🚀 [核心启动]`                        |
| TokenFlow   | `TOKEN-FLOW`   | `🔥 收到第N个token事件`                  |
| MessageSave | `MESSAGE-SAVE` | 统一保存日志                             |

---

## 9. 附录：接口/数据类模板

### 9.1 ThinkingEvent（节选）

```kotlin
sealed interface ThinkingEvent {
    data object PreThinkStarted : ThinkingEvent
    data class PreThinkContent(val text: String) : ThinkingEvent
    data class PhaseStart(val id: String, val title: String?) : ThinkingEvent
    data class PhaseContent(val id: String, val text: String) : ThinkingEvent
    data class PhaseComplete(val id: String) : ThinkingEvent
    data object FinalStart : ThinkingEvent
    data class FinalContent(val text: String) : ThinkingEvent
    data object FinalComplete : ThinkingEvent
}
```

### 9.2 Reducer -> Contract 转换

```kotlin
private fun convertToContractState(s: ThinkingUiState) = ThinkingBoxContract.State(
    phases = s.phases.values.map { it.toUi() },
    activePhaseId = s.activePhaseId,
    preThinking = s.preThinking,
    isStreaming = s.isStreaming,
    isThinkingComplete = s.isThinkingComplete,
    finalMarkdown = s.finalMarkdown,
    finalTokens = s.finalTokens,
    finalRichTextReady = s.finalRichTextReady,
    finalContentArrived = s.finalContentArrived,
    isFinalStreaming = s.isFinalStreaming,
    thinkingDuration = s.thinkingDuration,
    totalTokens = s.totalTokens,
    error = null,
    isLoading = false,
)
```

---

> 如需我直接提交 PR 或生成完整 patch 文件，请告诉我你的仓库结构和分支策略。
