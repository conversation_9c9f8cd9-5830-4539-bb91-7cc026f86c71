package com.example.gymbro.features.workout.session.internal.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.ExerciseSet
import com.example.gymbro.features.workout.session.SelectionStep
import com.example.gymbro.features.workout.session.SessionContract
import com.example.gymbro.features.workout.session.SessionContract.CountdownInfo
import com.example.gymbro.features.workout.session.SessionContract.Effect
import com.example.gymbro.features.workout.session.SessionContract.Intent
import com.example.gymbro.features.workout.session.SessionContract.State
import com.example.gymbro.shared.models.exercise.MuscleGroup
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.datetime.Clock
import kotlinx.datetime.todayIn
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
// JSON 处理已委托给 /features/workout/json/ 目录下的统一组件

/**
 * Workout Session Reducer - 基于训练页面.md的MVI 2.0纯函数状态管理
 *
 * 核心职责：
 * 1. 处理所有Intent并产生新的State和Effect
 * 2. 确保状态变更的纯函数性和可预测性
 * 3. 管理复杂的训练会话状态转换
 * 4. 支持多实例倒计时、AI窗口、滚动状态恢复
 * 5. 提供完整的错误处理和恢复机制
 *
 * 设计原则：
 * - 纯函数：相同输入产生相同输出，无副作用
 * - 不可变性：使用ImmutableList确保状态安全
 * - 单一职责：每个方法只处理一种Intent
 * - 错误容错：优雅处理异常情况
 * - 性能优化：避免不必要的状态拷贝
 */
@Singleton
internal class SessionReducer
@Inject
constructor() : Reducer<Intent, State, Effect> {
    companion object {
        private const val TAG = "SessionReducer"
        // 🔥 已移除：AUTO_SAVE_INTERVAL_MS - 自动保存功能已被移除
    }

    // JSON 处理已委托给 /features/workout/json/ 目录下的统一组件

    override fun reduce(
        intent: Intent,
        currentState: State,
    ): ReduceResult<State, Effect> {
        Timber.tag(TAG).d("🔄 Reducing Intent: ${intent::class.simpleName}")

        return when (intent) {
            // === 会话生命周期管理 ===
            is Intent.LoadSession -> handleLoadSession(intent, currentState)
            is Intent.LoadFromTemplate -> handleLoadFromTemplate(intent, currentState)
            is Intent.LoadFromPlan -> handleLoadFromPlan(intent, currentState)
            is Intent.StartNewSession -> handleStartNewSession(intent, currentState)
            is Intent.PauseSession -> handlePauseSession(currentState)
            is Intent.ResumeSession -> handleResumeSession(currentState)
            is Intent.FinishSession -> handleFinishSession(currentState)
            is Intent.CompleteSession -> handleCompleteSession(currentState)
            is Intent.CompleteWorkout -> handleCompleteWorkout(currentState)

            // === 训练动作操作 ===
            is Intent.AddExercisesToSession -> handleAddExercisesToSession(intent, currentState)
            is Intent.RemoveExerciseFromSession -> handleRemoveExerciseFromSession(intent, currentState)
            is Intent.UpdateWeight -> handleUpdateWeight(intent, currentState)
            is Intent.UpdateReps -> handleUpdateReps(intent, currentState)
            is Intent.ToggleSetComplete -> handleToggleSetComplete(intent, currentState)
            is Intent.AddSet -> handleAddSet(intent, currentState)
            is Intent.RemoveSet -> handleRemoveSet(intent, currentState)
            is Intent.UpdateNotes -> handleUpdateNotes(intent, currentState)

            // === 倒计时控制 ===
            is Intent.OverrideRestTime -> handleOverrideRestTime(intent, currentState)
            Intent.PauseCountdown -> handlePauseCountdown(currentState)
            Intent.ResumeCountdown -> handleResumeCountdown(currentState)

            // === Session → Template 反向更新 ===
            is Intent.ApplyChangesToTemplate -> handleApplyChangesToTemplate(intent, currentState)

            // === UI交互 ===
            is Intent.ToggleTopBar -> handleToggleTopBar(currentState)
            is Intent.EnterImmersiveMode -> handleEnterImmersiveMode(currentState)
            is Intent.ExitImmersiveMode -> handleExitImmersiveMode(currentState)
            is Intent.ShowActionDialog -> handleShowActionDialog(intent, currentState)
            is Intent.HideActionDialog -> handleHideActionDialog(currentState)
            is Intent.ShowCompleteWorkoutDialog -> handleShowCompleteWorkoutDialog(currentState)
            is Intent.HideCompleteWorkoutDialog -> handleHideCompleteWorkoutDialog(currentState)

            // === 状态恢复和持久化 ===
            is Intent.SaveScrollPosition -> handleSaveScrollPosition(intent, currentState)
            is Intent.RestoreScrollPosition -> handleRestoreScrollPosition(intent, currentState)
            // 🔥 已移除：TriggerAutoSave Intent 处理 - 自动保存功能已被移除
            is Intent.RecoverFromBackground -> handleRecoverFromBackground(currentState)

            // === 新增的Session容器Intent ===
            is Intent.UpdateExercise -> handleUpdateExercise(intent, currentState)
            is Intent.UpdateExerciseWithSets -> handleUpdateExerciseWithSets(
                intent,
                currentState,
            ) // 🔥 新增
            is Intent.CompleteSet -> handleCompleteSet(intent, currentState)
            is Intent.ToggleTimer -> handleToggleTimer(currentState)
            is Intent.ReviewExercise -> handleReviewExercise(intent, currentState)
            is Intent.PreviewExercise -> handlePreviewExercise(intent, currentState)
            is Intent.ViewStatisticDetail -> handleViewStatisticDetail(intent, currentState)

            // === 导航 ===
            is Intent.NavigateBack -> handleNavigateBack(currentState)
            is Intent.NavigateToExerciseDetail -> handleNavigateToExerciseDetail(intent, currentState)

            // === 错误处理 ===
            is Intent.ClearError -> handleClearError(currentState)
            is Intent.ShowError -> handleShowError(intent, currentState)

            // === 内部Intent（EffectHandler与ViewModel通信）===
            is Intent.SessionLoaded -> handleSessionLoaded(intent, currentState)
            is Intent.OnSessionLoaded -> handleOnSessionLoaded(intent, currentState)
            is Intent.NavigateToSessionResult -> handleNavigateToSessionResult(intent, currentState)
            is Intent.SessionSaved -> handleSessionSaved(intent, currentState)
            is Intent.SetUpdated -> handleSetUpdated(intent, currentState)
            is Intent.DailyStatsReadyResult -> handleDailyStatsReadyResult(intent, currentState)

            is Intent.SessionLoadError -> handleSessionLoadError(currentState)
            is Intent.SessionSaveError -> handleSessionSaveError(currentState)

            // === 训练源选择相关Intent ===
            is Intent.ShowWorkoutSourceSelector -> handleShowWorkoutSourceSelector(currentState)
            is Intent.SelectWorkoutSourceType -> handleSelectWorkoutSourceType(intent, currentState)
            is Intent.SelectPlan -> handleSelectPlan(intent, currentState)
            is Intent.SelectTemplate -> handleSelectTemplate(intent, currentState)
            is Intent.SelectTemplateFromPlan -> handleSelectTemplateFromPlan(intent, currentState)
            is Intent.StartSelectedWorkout -> handleStartSelectedWorkout(currentState)
            is Intent.BackToPreviousStep -> handleBackToPreviousStep(currentState)
            is Intent.TemplatesLoaded -> handleTemplatesLoaded(intent, currentState)
            is Intent.PlansLoaded -> handlePlansLoaded(intent, currentState)
            is Intent.PlanTemplatesLoaded -> handlePlanTemplatesLoaded(intent, currentState)
            is Intent.FreestyleModeActivated -> handleFreestyleModeActivated(currentState)

            // === PRD v2.0 新增Intent处理 ===
            is Intent.SelectDraft -> handleSelectDraft(intent, currentState)
            is Intent.ShowTemplateSwitcher -> handleShowTemplateSwitcher(currentState)
            is Intent.HideTemplateSwitcher -> handleHideTemplateSwitcher(currentState)
            is Intent.SwitchToTemplate -> handleSwitchToTemplate(intent, currentState)
            is Intent.AddExerciseToCurrentTemplate -> handleAddExerciseToCurrentTemplate(
                intent,
                currentState,
            )
            is Intent.SaveTemplateChanges -> handleSaveTemplateChanges(currentState)
            is Intent.LoadTemplateVersions -> handleLoadTemplateVersions(intent, currentState)
            is Intent.DraftsLoaded -> handleDraftsLoaded(intent, currentState)
            is Intent.TemplateVersionsLoaded -> handleTemplateVersionsLoaded(intent, currentState)
        }
    }

    // ==================== 会话生命周期管理 ====================

    private fun handleLoadSession(
        intent: Intent.LoadSession,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState =
            currentState.copy(
                isLoading = true,
                error = null,
                sessionId = intent.sessionId,
                needsRecovery = true,
            )
        return ReduceResult.stateOnly(newState)
    }

    private fun handleLoadFromTemplate(
        intent: Intent.LoadFromTemplate,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState =
            currentState.copy(
                isLoading = true,
                error = null,
            )

        // 🔥 关键修复：触发模板转换 Effect
        val effect = Effect.CreateSessionFromTemplate(
            templateId = intent.templateId,
            date = intent.date,
        )

        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleStartNewSession(
        intent: Intent.StartNewSession,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState =
            currentState.copy(
                isLoading = true,
                error = null,
                startedAt = intent.timestamp ?: System.currentTimeMillis(),
            )
        return ReduceResult.stateOnly(newState)
    }

    private fun handlePauseSession(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(isPaused = true)
        return ReduceResult.stateOnly(newState)
    }

    private fun handleResumeSession(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(isPaused = false)
        return ReduceResult.stateOnly(newState)
    }

    private fun handleFinishSession(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(isCompleting = true)

        val effects = mutableListOf<Effect>()

        // 保存会话
        currentState.session?.let { session ->
            effects.add(Effect.SaveSession(session))
            // 触发日级统计计算
            effects.add(Effect.CalculateDailyStats(session.id))
        }

        return if (effects.isNotEmpty()) {
            ReduceResult.withEffects(newState, effects)
        } else {
            ReduceResult.stateOnly(newState)
        }
    }

    // ==================== 训练动作操作 ====================

    private fun handleAddExercisesToSession(
        intent: Intent.AddExercisesToSession,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val currentExercises = currentState.exercises.toMutableList()
        val newExercises = mutableListOf<SessionContract.SessionExerciseUiModel>()

        intent.exercises.forEach { exercise ->
            // 创建默认的训练组
            val defaultSets =
                listOf(
                    ExerciseSet(
                        id = UUID.randomUUID().toString(),
                        exerciseId = exercise.id,
                        sessionId = currentState.sessionId,
                        order = 0,
                        weight = 0f,
                        reps = 8,
                        isCompleted = false,
                        createdAt = intent.timestamp ?: System.currentTimeMillis(),
                    ),
                ).toImmutableList()

            // 创建SessionExercise
            val sessionExercise =
                com.example.gymbro.domain.workout.model.session.SessionExercise(
                    id = UUID.randomUUID().toString(),
                    sessionId = currentState.sessionId,
                    exerciseId = exercise.id,
                    order = currentExercises.size + newExercises.size,
                    sets = defaultSets,
                    notes = null,
                    completedAt = null,
                    createdAt = intent.timestamp ?: System.currentTimeMillis(),
                )

            // 创建UI模型
            val uiModel =
                SessionContract.SessionExerciseUiModel(
                    sessionExercise = sessionExercise,
                    exercise = exercise,
                )

            newExercises.add(uiModel)
        }

        currentExercises.addAll(newExercises)

        // 重新计算统计数据
        val totalSets = currentExercises.sumOf { it.sets.size }
        val completedSetsCount = currentExercises.sumOf { ex -> ex.sets.count { it.isCompleted } }
        val totalExercises = currentExercises.size
        val completedExercises = currentExercises.count { it.isCompleted }

        val newState =
            currentState.copy(
                exercises = currentExercises.toImmutableList(),
                totalExercises = totalExercises,
                completedExercises = completedExercises,
                totalSets = totalSets,
                completedSetsCount = completedSetsCount,
                hasUnsavedChanges = true,
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )

        // 保存会话状态
        val effect =
            currentState.session?.let { session ->
                Effect.SaveSession(
                    session.copy(
                        exercises = currentExercises.map { it.sessionExercise }.toImmutableList(),
                    ),
                )
            }

        return if (effect != null) {
            ReduceResult.withEffect(newState, effect)
        } else {
            ReduceResult.stateOnly(newState)
        }
    }

    private fun handleRemoveExerciseFromSession(
        intent: Intent.RemoveExerciseFromSession,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val currentExercises = currentState.exercises.toMutableList()

        if (intent.exerciseIndex !in currentExercises.indices) {
            return ReduceResult.stateOnly(currentState)
        }

        currentExercises.removeAt(intent.exerciseIndex)

        // 重新计算统计数据
        val totalSets = currentExercises.sumOf { it.sets.size }
        val completedSetsCount = currentExercises.sumOf { ex -> ex.sets.count { it.isCompleted } }
        val totalExercises = currentExercises.size
        val completedExercises = currentExercises.count { it.isCompleted }

        val newState =
            currentState.copy(
                exercises = currentExercises.toImmutableList(),
                totalExercises = totalExercises,
                completedExercises = completedExercises,
                totalSets = totalSets,
                completedSetsCount = completedSetsCount,
                hasUnsavedChanges = true,
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )

        // 保存会话状态
        val effect =
            currentState.session?.let { session ->
                Effect.SaveSession(
                    session.copy(
                        exercises = currentExercises.map { it.sessionExercise }.toImmutableList(),
                    ),
                )
            }

        return if (effect != null) {
            ReduceResult.withEffect(newState, effect)
        } else {
            ReduceResult.stateOnly(newState)
        }
    }

    private fun handleUpdateWeight(
        intent: Intent.UpdateWeight,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val exercise =
            currentState.exercises.getOrNull(intent.exerciseIndex) ?: return ReduceResult.stateOnly(
                currentState,
            )
        val sets = exercise.sets.toMutableList()
        val set = sets.getOrNull(intent.setIndex) ?: return ReduceResult.stateOnly(currentState)

        val updatedSet = set.copy(weight = intent.weight)
        sets[intent.setIndex] = updatedSet

        val updatedExercise =
            exercise.copy(
                sessionExercise =
                exercise.sessionExercise.copy(
                    sets = sets.toImmutableList(),
                ),
            )

        val updatedExercises = currentState.exercises.toMutableList()
        updatedExercises[intent.exerciseIndex] = updatedExercise

        val newState =
            currentState.copy(
                exercises = updatedExercises.toImmutableList(),
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )

        val effect = Effect.SaveSet(currentState.sessionId, exercise.exerciseId, updatedSet)
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleUpdateReps(
        intent: Intent.UpdateReps,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val exercise =
            currentState.exercises.getOrNull(intent.exerciseIndex) ?: return ReduceResult.stateOnly(
                currentState,
            )
        val sets = exercise.sets.toMutableList()
        val set = sets.getOrNull(intent.setIndex) ?: return ReduceResult.stateOnly(currentState)

        val updatedSet = set.copy(reps = intent.reps)
        sets[intent.setIndex] = updatedSet

        val updatedExercise =
            exercise.copy(
                sessionExercise =
                exercise.sessionExercise.copy(
                    sets = sets.toImmutableList(),
                ),
            )

        val updatedExercises = currentState.exercises.toMutableList()
        updatedExercises[intent.exerciseIndex] = updatedExercise

        val newState =
            currentState.copy(
                exercises = updatedExercises.toImmutableList(),
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )

        val effect = Effect.SaveSet(currentState.sessionId, exercise.exerciseId, updatedSet)
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleToggleSetComplete(
        intent: Intent.ToggleSetComplete,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val exercise =
            currentState.exercises.getOrNull(intent.exerciseIndex) ?: return ReduceResult.stateOnly(
                currentState,
            )
        val sets = exercise.sets.toMutableList()
        val set = sets.getOrNull(intent.setIndex) ?: return ReduceResult.stateOnly(currentState)

        val updatedSet =
            set.copy(
                isCompleted = !set.isCompleted,
                // 移除completedAt参数，因为ExerciseSet中没有这个属性
            )
        sets[intent.setIndex] = updatedSet

        val updatedExercise =
            exercise.copy(
                sessionExercise =
                exercise.sessionExercise.copy(
                    sets = sets.toImmutableList(),
                ),
            )

        val updatedExercises = currentState.exercises.toMutableList()
        updatedExercises[intent.exerciseIndex] = updatedExercise

        // 重新计算完成的组数
        val completedSetsCount = updatedExercises.sumOf { ex -> ex.sets.count { it.isCompleted } }

        val newState =
            currentState.copy(
                exercises = updatedExercises.toImmutableList(),
                completedSetsCount = completedSetsCount,
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )

        val effect = Effect.SaveSet(currentState.sessionId, exercise.exerciseId, updatedSet)
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleAddSet(
        intent: Intent.AddSet,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val exercise =
            currentState.exercises.getOrNull(intent.exerciseIndex) ?: return ReduceResult.stateOnly(
                currentState,
            )
        val sets = exercise.sets.toMutableList()

        // 创建新的空组，基于最后一组的重量
        val lastSet = sets.lastOrNull()
        val newSet =
            ExerciseSet(
                id = UUID.randomUUID().toString(),
                exerciseId = exercise.exerciseId,
                sessionId = currentState.sessionId,
                order = sets.size,
                weight = lastSet?.weight ?: 0f,
                reps = lastSet?.reps ?: 8,
                isCompleted = false,
                createdAt = intent.timestamp ?: System.currentTimeMillis(),
            )

        sets.add(newSet)

        val updatedExercise =
            exercise.copy(
                sessionExercise =
                exercise.sessionExercise.copy(
                    sets = sets.toImmutableList(),
                ),
            )

        val updatedExercises = currentState.exercises.toMutableList()
        updatedExercises[intent.exerciseIndex] = updatedExercise

        val newState =
            currentState.copy(
                exercises = updatedExercises.toImmutableList(),
                totalSets = currentState.totalSets + 1,
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )

        val effect = Effect.SaveSet(currentState.sessionId, exercise.exerciseId, newSet)
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleRemoveSet(
        intent: Intent.RemoveSet,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val exercise =
            currentState.exercises.getOrNull(intent.exerciseIndex) ?: return ReduceResult.stateOnly(
                currentState,
            )
        if (exercise.sets.size <= 1) return ReduceResult.stateOnly(currentState) // 至少保留一组

        val sets = exercise.sets.toMutableList()
        if (intent.setIndex !in sets.indices) return ReduceResult.stateOnly(currentState)

        sets.removeAt(intent.setIndex)

        val updatedExercise =
            exercise.copy(
                sessionExercise =
                exercise.sessionExercise.copy(
                    sets = sets.toImmutableList(),
                ),
            )

        val updatedExercises = currentState.exercises.toMutableList()
        updatedExercises[intent.exerciseIndex] = updatedExercise

        // 重新计算总组数和完成组数
        val totalSets = updatedExercises.sumOf { it.sets.size }
        val completedSetsCount = updatedExercises.sumOf { ex -> ex.sets.count { it.isCompleted } }

        val newState =
            currentState.copy(
                exercises = updatedExercises.toImmutableList(),
                totalSets = totalSets,
                completedSetsCount = completedSetsCount,
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )

        return ReduceResult.stateOnly(newState)
    }

    private fun handleUpdateNotes(
        intent: Intent.UpdateNotes,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val updatedExercises = currentState.exercises.toMutableList()
        val exercise =
            updatedExercises.getOrNull(intent.exerciseIndex) ?: return ReduceResult.stateOnly(currentState)

        val updatedExercise =
            exercise.copy(
                sessionExercise = exercise.sessionExercise.copy(notes = intent.notes),
            )
        updatedExercises[intent.exerciseIndex] = updatedExercise

        val newState =
            currentState.copy(
                exercises = updatedExercises.toImmutableList(),
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )

        return ReduceResult.stateOnly(newState)
    }

    // ==================== UI交互 ====================

    private fun handleToggleTopBar(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(showTopBar = !currentState.showTopBar)
        return ReduceResult.stateOnly(newState)
    }

    private fun handleEnterImmersiveMode(currentState: State): ReduceResult<State, Effect> {
        val newState =
            currentState.copy(
                isImmersiveMode = true,
                showTopBar = false,
            )

        val effect = Effect.EnableImmersiveMode
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleExitImmersiveMode(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(isImmersiveMode = false)

        val effect = Effect.DisableImmersiveMode
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleShowActionDialog(
        intent: Intent.ShowActionDialog,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState =
            currentState.copy(
                showActionDialog = true,
                selectedSetForEdit = Pair(intent.exerciseIndex, intent.setIndex),
            )
        return ReduceResult.stateOnly(newState)
    }

    private fun handleHideActionDialog(currentState: State): ReduceResult<State, Effect> {
        val newState =
            currentState.copy(
                showActionDialog = false,
                selectedSetForEdit = null,
            )
        return ReduceResult.stateOnly(newState)
    }

    // ==================== 状态恢复和持久化 ====================

    private fun handleSaveScrollPosition(
        intent: Intent.SaveScrollPosition,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val now = intent.timestamp ?: System.currentTimeMillis()
        val newScrollState = currentState.scrollState.copy(lastSavedAt = now)

        val newState = currentState.copy(scrollState = newScrollState)

        val effect =
            Effect.SaveScrollPosition(
                currentState.scrollState.firstVisibleItemIndex,
                currentState.scrollState.firstVisibleItemScrollOffset,
            )
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleRestoreScrollPosition(
        intent: Intent.RestoreScrollPosition,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newScrollState =
            currentState.scrollState.copy(
                firstVisibleItemIndex = intent.firstVisibleItemIndex,
                firstVisibleItemScrollOffset = intent.scrollOffset,
            )

        val newState = currentState.copy(scrollState = newScrollState)
        return ReduceResult.stateOnly(newState)
    }

    // 🔥 已移除：handleTriggerAutoSave 函数 - 自动保存功能已被移除

    private fun handleRecoverFromBackground(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(needsRecovery = false)

        val effect = Effect.TriggerFullRecovery
        return ReduceResult.withEffect(newState, effect)
    }

    // ==================== 导航 ====================

    private fun handleNavigateBack(currentState: State): ReduceResult<State, Effect> {
        val effect = Effect.NavigateBack
        return ReduceResult.withEffect(currentState, effect)
    }

    private fun handleNavigateToExerciseDetail(
        intent: Intent.NavigateToExerciseDetail,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val effect = Effect.NavigateToExerciseDetail(intent.exerciseId)
        return ReduceResult.withEffect(currentState, effect)
    }

    // ==================== 错误处理 ====================

    private fun handleClearError(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(error = null)
        return ReduceResult.stateOnly(newState)
    }

    private fun handleShowError(
        intent: Intent.ShowError,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState = currentState.copy(error = intent.error)
        return ReduceResult.stateOnly(newState)
    }

    // ==================== 内部Intent处理 ====================

    private fun handleSessionLoaded(
        intent: Intent.SessionLoaded,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val session = intent.session

        val exercises: ImmutableList<SessionContract.SessionExerciseUiModel> =
            session.exercises
                .mapIndexed { _, sessionExercise ->
                    // Exercise详情应该在会话加载时已经包含在sessionExercise中
                    // 如果缺少详情，使用基本信息创建Exercise对象
                    SessionContract.SessionExerciseUiModel(
                        sessionExercise = sessionExercise,
                        exercise = createBasicExercise(sessionExercise.exerciseId),
                    )
                }.toImmutableList()

        val totalSets = exercises.sumOf { exerciseUiModel -> exerciseUiModel.sessionExercise.sets.size }
        val completedSetsCount =
            exercises.sumOf { exerciseUiModel ->
                exerciseUiModel.sessionExercise.sets.count { exerciseSet -> exerciseSet.isCompleted }
            }
        val completedExercises =
            exercises.count { exerciseUiModel ->
                exerciseUiModel.sessionExercise.sets.all { exerciseSet -> exerciseSet.isCompleted }
            }

        val newState =
            currentState.copy(
                session = session,
                exercises = exercises,
                totalExercises = exercises.size,
                completedExercises = completedExercises,
                totalSets = totalSets,
                completedSetsCount = completedSetsCount,
                isLoading = false,
                error = null,
            )

        return ReduceResult.stateOnly(newState)
    }

    /**
     * 处理会话加载结果（支持错误处理）
     */
    private fun handleOnSessionLoaded(
        intent: Intent.OnSessionLoaded,
        currentState: State,
    ): ReduceResult<State, Effect> {
        return when (intent.result) {
            is ModernResult.Success -> {
                // 成功加载会话，使用现有的 handleSessionLoaded 逻辑
                handleSessionLoaded(Intent.SessionLoaded(intent.result.data), currentState)
            }
            is ModernResult.Error -> {
                val newState = currentState.copy(
                    isLoading = false,
                    error = UiText.DynamicString("创建训练会话失败: ${intent.result.error}"),
                )
                ReduceResult.stateOnly(newState)
            }
            is ModernResult.Loading -> {
                val newState = currentState.copy(isLoading = true, error = null)
                ReduceResult.stateOnly(newState)
            }
        }
    }

    /**
     * 处理导航到 Session 结果
     */
    private fun handleNavigateToSessionResult(
        intent: Intent.NavigateToSessionResult,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 触发导航到 Session 界面的 Effect
        val effect = Effect.NavigateToSession(intent.sessionId)
        return ReduceResult.withEffect(currentState, effect)
    }

    private fun handleSessionSaved(
        intent: Intent.SessionSaved,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState =
            currentState.copy(
                // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
            )
        return ReduceResult.stateOnly(newState)
    }

    private fun handleSetUpdated(
        intent: Intent.SetUpdated,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 这个Intent通常由EffectHandler发送，用于更新UI状态
        val exercises = currentState.exercises.toMutableList()
        val exercise =
            exercises.getOrNull(intent.exerciseIndex) ?: return ReduceResult.stateOnly(currentState)

        val sets = exercise.sets.toMutableList()
        if (intent.setIndex in sets.indices) {
            sets[intent.setIndex] = intent.set

            val updatedExercise =
                exercise.copy(
                    sessionExercise = exercise.sessionExercise.copy(sets = sets.toImmutableList()),
                )
            exercises[intent.exerciseIndex] = updatedExercise
        }

        val newState = currentState.copy(exercises = exercises.toImmutableList())
        return ReduceResult.stateOnly(newState)
    }

    private fun handleSessionLoadError(currentState: State): ReduceResult<State, Effect> {
        val newState =
            currentState.copy(
                isLoading = false,
                error =
                com.example.gymbro.core.ui.text.UiText
                    .DynamicString("训练会话加载失败"),
            )
        return ReduceResult.stateOnly(newState)
    }

    private fun handleSessionSaveError(currentState: State): ReduceResult<State, Effect> {
        val effect =
            Effect.ShowToast(
                com.example.gymbro.core.ui.text.UiText
                    .DynamicString("训练数据保存失败"),
            )
        return ReduceResult.withEffect(currentState, effect)
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建基本Exercise对象，用于缺少详情时的降级处理
     */
    private fun createBasicExercise(
        exerciseId: String,
    ): com.example.gymbro.domain.exercise.model.Exercise =
        com.example.gymbro.domain.exercise.model.Exercise(
            id = exerciseId,
            name =
            com.example.gymbro.core.ui.text.UiText
                .DynamicString("训练动作"),
            description =
            com.example.gymbro.core.ui.text.UiText
                .DynamicString("基础训练动作"),
            muscleGroup = com.example.gymbro.shared.models.exercise.MuscleGroup.CHEST,
            equipment = persistentListOf(),
        )

    // ==================== 缺失的处理方法 ====================

    private fun handleCompleteSession(currentState: State): ReduceResult<State, Effect> {
        // CompleteSession: 实际完成Session，更新状态为COMPLETED并保存
        val session = currentState.session ?: return ReduceResult.stateOnly(currentState)

        val completedSession = session.copy(
            status = com.example.gymbro.domain.workout.model.session.WorkoutSession.Status.COMPLETED,
            endTime = Clock.System.now().toEpochMilliseconds(),
        )

        val newState = currentState.copy(
            isCompleting = true,
            session = completedSession,
        )

        val effect = Effect.SaveSession(completedSession)
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleCompleteWorkout(currentState: State): ReduceResult<State, Effect> {
        // CompleteWorkout: 显示完成确认对话框
        val newState = currentState.copy(showCompleteWorkoutDialog = true)
        return ReduceResult.stateOnly(newState)
    }

    private fun handleShowCompleteWorkoutDialog(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(showCompleteWorkoutDialog = true)
        return ReduceResult.stateOnly(newState)
    }

    private fun handleHideCompleteWorkoutDialog(currentState: State): ReduceResult<State, Effect> {
        val newState = currentState.copy(showCompleteWorkoutDialog = false)
        return ReduceResult.stateOnly(newState)
    }

    // ==================== 新增的Session容器Intent处理 ====================

    private fun handleUpdateExercise(
        intent: Intent.UpdateExercise,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 实现动作更新逻辑
        val session = currentState.session ?: return ReduceResult.stateOnly(currentState)

        // 更新指定动作的信息
        val updatedExercises = session.exercises.map { exercise ->
            if (exercise.exerciseId == intent.exerciseId) {
                exercise.copy(
                    name = intent.updateData.name ?: exercise.name,
                    // notes属性不在ExerciseUpdateData中，SessionExercise的notes需要单独的Intent处理
                    // ExerciseUpdateData主要用于动作库信息更新，不包含Session特定的notes
                )
            } else {
                exercise
            }
        }

        val updatedSession = session.copy(exercises = updatedExercises.toImmutableList())
        val newState = currentState.copy(session = updatedSession)

        // 保存更新后的会话
        return ReduceResult.withEffect(
            newState,
            Effect.SaveSession(updatedSession),
        )
    }

    // 🔥 新增：处理完整 sets 数据更新（参考 Template 模式的成功经验）
    private fun handleUpdateExerciseWithSets(
        intent: Intent.UpdateExerciseWithSets,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val session = currentState.session ?: return ReduceResult.stateOnly(currentState)

        // 找到要更新的动作
        val exerciseIndex = session.exercises.indexOfFirst { it.exerciseId == intent.exerciseId }
        if (exerciseIndex == -1) {
            timber.log.Timber.w("🔧 [SessionReducer] 未找到动作: ${intent.exerciseId}")
            return ReduceResult.stateOnly(currentState)
        }

        val currentExercise = session.exercises[exerciseIndex]
        val sessionUpdateData = intent.sessionUpdateData

        // 🔥 修复数据覆盖问题：正确处理所有组的数据更新
        // 问题：原逻辑只处理传入的 sets，会丢失其他组的数据
        // 解决：确保所有组的数据都被正确保留和更新

        val maxSetsCount = maxOf(currentExercise.sets.size, sessionUpdateData.sets.size)
        val updatedSets = (0 until maxSetsCount).map { index ->
            val sessionSet = sessionUpdateData.sets.getOrNull(index)
            val existingSet = currentExercise.sets.getOrNull(index)

            when {
                // 有新数据且有现有数据：更新现有数据
                sessionSet != null && existingSet != null -> {
                    existingSet.copy(
                        weight = sessionSet.weight?.toFloat(),
                        reps = sessionSet.reps ?: existingSet.reps,
                        isCompleted = sessionSet.isCompleted,
                        updatedAt = System.currentTimeMillis(),
                    )
                }
                // 有新数据但没有现有数据：创建新数据
                sessionSet != null && existingSet == null -> {
                    ExerciseSet(
                        id = sessionSet.id,
                        exerciseId = currentExercise.exerciseId,
                        order = index + 1,
                        weight = sessionSet.weight?.toFloat(),
                        reps = sessionSet.reps ?: 0,
                        isCompleted = sessionSet.isCompleted,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis(),
                    )
                }
                // 没有新数据但有现有数据：保留现有数据
                sessionSet == null && existingSet != null -> {
                    existingSet
                }
                // 都没有：不应该发生，但为了安全创建默认数据
                else -> {
                    ExerciseSet(
                        id = "set_${index + 1}_${System.currentTimeMillis()}",
                        exerciseId = currentExercise.exerciseId,
                        order = index + 1,
                        weight = 0f,
                        reps = 0,
                        isCompleted = false,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis(),
                    )
                }
            }
        }

        timber.log.Timber.d(
            "🔧 [SessionReducer] 数据更新: 原有${currentExercise.sets.size}组, 新数据${sessionUpdateData.sets.size}组, 最终${updatedSets.size}组",
        )

        // 更新动作数据
        val updatedExercise = currentExercise.copy(
            restSeconds = sessionUpdateData.restTimeSeconds,
            notes = sessionUpdateData.notes,
            sets = updatedSets.toImmutableList(),
        )

        // 更新 session 中的动作列表
        val updatedExercises = session.exercises.toMutableList()
        updatedExercises[exerciseIndex] = updatedExercise

        val updatedSession = session.copy(exercises = updatedExercises.toImmutableList())

        // 重新计算统计数据
        val totalSets = updatedExercises.sumOf { it.sets.size }
        val completedSetsCount = updatedExercises.sumOf { ex -> ex.sets.count { it.isCompleted } }

        val newState = currentState.copy(
            session = updatedSession,
            exercises = updatedExercises.mapIndexed { index, exercise ->
                // 保持现有的 SessionExerciseUiModel，只更新对应的 sessionExercise
                val existingUiModel = currentState.exercises.getOrNull(index)
                existingUiModel?.copy(
                    sessionExercise = exercise,
                ) ?: SessionContract.SessionExerciseUiModel(
                    sessionExercise = exercise,
                    exercise = existingUiModel?.exercise ?: com.example.gymbro.domain.exercise.model.Exercise(
                        id = exercise.exerciseId,
                        name = com.example.gymbro.core.ui.text.UiText.DynamicString(exercise.name),
                        muscleGroup = MuscleGroup.CHEST, // 默认值
                        equipment = emptyList(),
                        description = com.example.gymbro.core.ui.text.UiText.DynamicString(""),
                    ),
                )
            }.toImmutableList(),
            totalSets = totalSets,
            completedSetsCount = completedSetsCount,
            hasUnsavedChanges = true,
            // 🔥 已移除：lastAutoSaveAt 赋值 - 自动保存功能已被移除
        )

        timber.log.Timber.d(
            "🔧 [SessionReducer] 更新动作成功: ${intent.exerciseId}, sets=${sessionUpdateData.sets.size}",
        )

        // 触发自动保存
        return ReduceResult.withEffect(newState, Effect.SaveSession(updatedSession))
    }

    private fun handleCompleteSet(
        intent: Intent.CompleteSet,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 实现组完成逻辑
        val session = currentState.session ?: return ReduceResult.stateOnly(currentState)

        // 找到对应的动作和组
        val updatedExercises = session.exercises.map { exercise ->
            if (exercise.exerciseId == intent.exerciseId) {
                val updatedSets = exercise.sets.map { set ->
                    if (set.id == intent.setId) {
                        set.copy(
                            isCompleted = true,
                            // 简化实现：只标记完成状态，重量和次数由其他Intent处理
                        )
                    } else {
                        set
                    }
                }
                exercise.copy(sets = updatedSets.toImmutableList())
            } else {
                exercise
            }
        }

        val updatedSession = session.copy(exercises = updatedExercises.toImmutableList())
        val newState = currentState.copy(session = updatedSession)

        // 保存会话并触发UI更新
        return ReduceResult.withEffect(
            newState,
            Effect.SaveSession(updatedSession),
        )
    }

    private fun handleToggleTimer(currentState: State): ReduceResult<State, Effect> {
        // 计时器功能已简化 - 使用现有的countdownInfo状态
        val currentCountdown = currentState.countdownInfo

        val newCountdownInfo = if (currentCountdown?.isActive == true) {
            // 暂停计时器
            currentCountdown.copy(isActive = false)
        } else {
            // 启动新的计时器（默认90秒休息时间）
            CountdownInfo(
                exerciseId = currentState.currentExercise?.sessionExercise?.exerciseId ?: "",
                restTimeSeconds = 90,
                remainingSeconds = 90,
                isActive = true,
                isPaused = false,
                customTitle = "组间休息",
            )
        }

        return ReduceResult.stateOnly(
            currentState.copy(countdownInfo = newCountdownInfo),
        )
    }

    private fun handleReviewExercise(
        intent: Intent.ReviewExercise,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 动作回顾功能已移除 - 专注当前训练流程
        return ReduceResult.stateOnly(currentState)
    }

    private fun handlePreviewExercise(
        intent: Intent.PreviewExercise,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 动作预览功能已移除 - 专注当前训练流程
        return ReduceResult.stateOnly(currentState)
    }

    private fun handleViewStatisticDetail(
        intent: Intent.ViewStatisticDetail,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 统计详情查看功能已移除 - 专注当前训练流程
        return ReduceResult.stateOnly(currentState)
    }

    // ==================== 倒计时控制处理 ====================

    private fun handleOverrideRestTime(
        intent: Intent.OverrideRestTime,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 更新exerciseOverrides映射
        val updatedOverrides = currentState.exerciseOverrides.toMutableMap()
        updatedOverrides[intent.exerciseId] = intent.restSeconds

        // 如果有活跃倒计时且是同一个动作，更新倒计时信息
        val updatedCountdownInfo =
            currentState.countdownInfo?.let { countdownInfo ->
                if (countdownInfo.exerciseId == intent.exerciseId) {
                    countdownInfo.copy(restTimeSeconds = intent.restSeconds)
                } else {
                    countdownInfo
                }
            }

        val newState =
            currentState.copy(
                exerciseOverrides = updatedOverrides,
                countdownInfo = updatedCountdownInfo,
            )

        return ReduceResult.stateOnly(newState)
    }

    private fun handlePauseCountdown(currentState: State): ReduceResult<State, Effect> {
        val countdownInfo = currentState.countdownInfo ?: return ReduceResult.stateOnly(currentState)

        val updatedCountdownInfo = countdownInfo.copy(isPaused = true)
        val newState = currentState.copy(countdownInfo = updatedCountdownInfo)

        return ReduceResult.stateOnly(newState)
    }

    private fun handleResumeCountdown(currentState: State): ReduceResult<State, Effect> {
        val countdownInfo = currentState.countdownInfo ?: return ReduceResult.stateOnly(currentState)

        val updatedCountdownInfo = countdownInfo.copy(isPaused = false)
        val newState = currentState.copy(countdownInfo = updatedCountdownInfo)

        return ReduceResult.stateOnly(newState)
    }

    // ==================== Session → Template 反向更新 ====================

    private fun handleApplyChangesToTemplate(
        intent: Intent.ApplyChangesToTemplate,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val effect = Effect.ApplyChangesToTemplate(
            sessionId = currentState.sessionId,
            templateId = intent.templateId,
            exerciseOverrides = currentState.exerciseOverrides,
            description = intent.description,
        )
        return ReduceResult.withEffect(currentState, effect)
    }

    // ==================== Plan加载处理 ====================

    private fun handleLoadFromPlan(
        intent: Intent.LoadFromPlan,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState = currentState.copy(
            isLoading = true,
            error = null,
        )

        // 实现从Plan加载session的逻辑
        // 触发Effect来加载Plan的详细信息和模板
        return ReduceResult.withEffect(
            newState,
            Effect.LoadPlanTemplates(intent.planId),
        )
    }

    // ==================== 训练源选择Intent处理 ====================

    private fun handleTemplatesLoaded(
        intent: Intent.TemplatesLoaded,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            availableTemplates = intent.templates,
            isLoadingTemplates = false,
        )

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    private fun handlePlansLoaded(
        intent: Intent.PlansLoaded,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            availablePlans = intent.plans,
            isLoadingPlans = false,
        )

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    private fun handlePlanTemplatesLoaded(
        intent: Intent.PlanTemplatesLoaded,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            planTemplates = intent.templates,
            isLoadingTemplates = false,
        )

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    private fun handleFreestyleModeActivated(
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 即兴训练模式激活处理 - 简化实现
        // 创建一个空的训练会话用于即兴训练
        val freestyleSession = createEmptyFreestyleSession()

        return ReduceResult.stateOnly(
            currentState.copy(
                session = freestyleSession,
                isLoading = false,
            ),
        )
    }

    private fun handleDailyStatsReadyResult(
        intent: Intent.DailyStatsReadyResult,
        currentState: State,
    ): ReduceResult<State, Effect> {
        // 处理日级统计数据准备完成
        val newState = currentState.copy(
            isCompleting = false, // 完成统计计算，结束completing状态
        )

        // 显示统计数据
        return ReduceResult.withEffect(
            newState,
            Effect.ShowDailyStats(intent.stats),
        )
    }

    private fun createEmptyFreestyleSession(): com.example.gymbro.domain.workout.model.session.WorkoutSession {
        return com.example.gymbro.domain.workout.model.session.WorkoutSession(
            id = "freestyle_${kotlinx.datetime.Clock.System.now().toEpochMilliseconds()}",
            date = kotlinx.datetime.Clock.System.todayIn(kotlinx.datetime.TimeZone.currentSystemDefault()),
            templateId = null,
            name = "即兴训练",
            description = "自由训练模式",
            startTime = kotlinx.datetime.Clock.System.now().toEpochMilliseconds(),
            endTime = null,
            isCompleted = false,
            plannedDate = null,
            plannedTemplateId = null,
            status = com.example.gymbro.domain.workout.model.session.WorkoutSession.Status.IN_PROGRESS,
            completionTimestamp = null,
            exercises = persistentListOf(),
            userId = "default_user",
        )
    }

    // ==================== 训练源选择Intent处理 ====================

    private fun handleShowWorkoutSourceSelector(
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            isShowingSelector = true,
            currentStep = SelectionStep.SOURCE_TYPE,
            // 🔧 修复：设置所有数据源的加载状态为 true
            isLoadingPlans = true,
            isLoadingTemplates = true,
            isLoadingDrafts = true,
            error = null, // 清除之前的错误
        )

        return ReduceResult.withEffects(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
            listOf(
                Effect.LoadAvailablePlans,
                Effect.LoadAvailableTemplates,
                Effect.LoadAvailableDrafts, // 新增：加载草稿列表
            ),
        )
    }

    private fun handleSelectWorkoutSourceType(
        intent: Intent.SelectWorkoutSourceType,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            selectedSourceType = intent.sourceType,
            currentStep = SelectionStep.SPECIFIC_SELECTION,
        )

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    private fun handleSelectPlan(
        intent: Intent.SelectPlan,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            selectedPlan = intent.plan,
            isLoadingTemplates = true,
        )

        return ReduceResult.withEffect(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
            Effect.LoadPlanTemplates(intent.plan.id),
        )
    }

    private fun handleSelectTemplate(
        intent: Intent.SelectTemplate,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            selectedTemplate = intent.template,
            currentStep = SelectionStep.CONFIRM,
        )

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    private fun handleSelectTemplateFromPlan(
        intent: Intent.SelectTemplateFromPlan,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            selectedTemplate = intent.template,
            currentStep = SelectionStep.CONFIRM,
        )

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    private fun handleStartSelectedWorkout(
        currentState: State,
    ): ReduceResult<State, Effect> {
        val selectedTemplate = currentState.workoutSourceState.selectedTemplate
        val selectedDraft = currentState.workoutSourceState.selectedDraft
        val selectedPlan = currentState.workoutSourceState.selectedPlan

        return when {
            selectedTemplate != null -> {
                // 🔧 修复：设置加载状态并触发从模板创建会话
                val newState = currentState.copy(
                    isLoading = true,
                    workoutSourceState = currentState.workoutSourceState.copy(
                        isShowingSelector = false, // 隐藏选择器
                    ),
                )
                ReduceResult.withEffect(
                    newState,
                    Effect.CreateSessionFromTemplate(
                        template = selectedTemplate,
                        fromPlan = selectedPlan,
                    ),
                )
            }
            selectedDraft != null -> {
                // 🔧 修复：设置加载状态并触发从草稿创建会话
                val newState = currentState.copy(
                    isLoading = true,
                    workoutSourceState = currentState.workoutSourceState.copy(
                        isShowingSelector = false, // 隐藏选择器
                    ),
                )
                ReduceResult.withEffect(
                    newState,
                    Effect.CreateSessionFromTemplate(
                        template = selectedDraft,
                        fromPlan = selectedPlan,
                    ),
                )
            }
            else -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        workoutSourceState = currentState.workoutSourceState.copy(
                            error = UiText.DynamicString("请选择一个模板或草稿"),
                        ),
                    ),
                )
            }
        }
    }

    private fun handleBackToPreviousStep(
        currentState: State,
    ): ReduceResult<State, Effect> {
        val currentStep = currentState.workoutSourceState.currentStep
        val newStep = when (currentStep) {
            com.example.gymbro.features.workout.session.SelectionStep.SPECIFIC_SELECTION ->
                com.example.gymbro.features.workout.session.SelectionStep.SOURCE_TYPE
            com.example.gymbro.features.workout.session.SelectionStep.CONFIRM ->
                com.example.gymbro.features.workout.session.SelectionStep.SPECIFIC_SELECTION
            else -> currentStep
        }

        val newWorkoutSourceState = when (newStep) {
            com.example.gymbro.features.workout.session.SelectionStep.SOURCE_TYPE -> {
                currentState.workoutSourceState.copy(
                    currentStep = newStep,
                    selectedPlan = null,
                    selectedTemplate = null,
                    planTemplates = persistentListOf(),
                )
            }
            com.example.gymbro.features.workout.session.SelectionStep.SPECIFIC_SELECTION -> {
                currentState.workoutSourceState.copy(
                    currentStep = newStep,
                    selectedTemplate = null,
                )
            }
            else -> currentState.workoutSourceState.copy(currentStep = newStep)
        }

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    // ==================== PRD v2.0 新增Intent处理方法 ====================

    private fun handleSelectDraft(
        intent: Intent.SelectDraft,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            selectedDraft = intent.draft,
            selectedTemplate = intent.draft, // 草稿也是模板类型
            currentStep = SelectionStep.CONFIRM,
        )

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    private fun handleShowTemplateSwitcher(
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState = currentState.copy(
            showTemplateSwitcher = true,
        )

        // 加载可切换的模板列表
        val effect = Effect.LoadTemplateVersions(
            sourceType = currentState.workoutSourceState.selectedSourceType,
        )

        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleHideTemplateSwitcher(
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState = currentState.copy(
            showTemplateSwitcher = false,
        )

        return ReduceResult.stateOnly(newState)
    }

    private fun handleSwitchToTemplate(
        intent: Intent.SwitchToTemplate,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState = currentState.copy(
            isLoading = true,
            currentTemplateId = intent.templateId,
            hasUnsavedChanges = true, // 标记有未保存的进度切换
        )

        val effect = Effect.SwitchToTemplate(
            templateId = intent.templateId,
            preserveProgress = true, // 保留当前训练进度
        )

        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleAddExerciseToCurrentTemplate(
        intent: Intent.AddExerciseToCurrentTemplate,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val currentTemplateId = currentState.currentTemplateId

        if (currentTemplateId == null) {
            return ReduceResult.stateOnly(currentState)
        }

        // 首先添加到当前session
        val addToSessionResult = handleAddExercisesToSession(
            Intent.AddExercisesToSession(listOf(intent.exercise)),
            currentState,
        )

        // 然后触发保存到模板的Effect
        val effects = mutableListOf<Effect>()

        // 添加原有的Effects（如果有的话）
        if (addToSessionResult.effects != null) {
            effects.addAll(addToSessionResult.effects)
        }

        // 添加保存到模板的Effect
        effects.add(
            Effect.AddExerciseToTemplate(
                templateId = currentTemplateId,
                exercise = intent.exercise,
            ),
        )

        val newState = addToSessionResult.newState.copy(
            hasUnsavedChanges = true,
        )

        return ReduceResult.withEffects(newState, effects)
    }

    private fun handleSaveTemplateChanges(
        currentState: State,
    ): ReduceResult<State, Effect> {
        val currentTemplateId = currentState.currentTemplateId

        if (currentTemplateId == null || !currentState.hasUnsavedChanges) {
            return ReduceResult.stateOnly(currentState)
        }

        val newState = currentState.copy(
            isLoading = true,
        )

        // 构造变更数据 - 基于当前session状态
        val changes = buildMap<String, Any> {
            put("exercises", currentState.exercises.map { it.sessionExercise })
            put("lastModified", System.currentTimeMillis())
            currentState.session?.let { session ->
                put("sessionData", session)
            }
        }

        val effect = Effect.SaveTemplateChanges(
            templateId = currentTemplateId,
            changes = changes,
        )

        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleLoadTemplateVersions(
        intent: Intent.LoadTemplateVersions,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState = currentState.copy(
            isLoading = true, // 使用通用的loading状态
        )

        val effect = Effect.LoadTemplateVersions(
            sourceType = intent.sourceType,
        )

        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleDraftsLoaded(
        intent: Intent.DraftsLoaded,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newWorkoutSourceState = currentState.workoutSourceState.copy(
            availableDrafts = intent.drafts,
            isLoadingDrafts = false,
        )

        return ReduceResult.stateOnly(
            currentState.copy(workoutSourceState = newWorkoutSourceState),
        )
    }

    private fun handleTemplateVersionsLoaded(
        intent: Intent.TemplateVersionsLoaded,
        currentState: State,
    ): ReduceResult<State, Effect> {
        val newState = currentState.copy(
            availableTemplateVersions = intent.versions,
            isLoading = false, // 完成加载
        )

        return ReduceResult.stateOnly(newState)
    }

    // ==================== 计算器处理函数 ====================
}
