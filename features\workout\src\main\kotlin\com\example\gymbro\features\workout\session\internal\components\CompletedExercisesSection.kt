package com.example.gymbro.features.workout.session.internal.components

import com.example.gymbro.features.workout.shared.animation.CrossModuleAnimations.standardContentSizeAnimation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.workout.session.SessionContract.SessionExerciseUiModel
import com.example.gymbro.features.workout.session.internal.util.SessionStrings
// 删除未使用的 JSON 导入，JSON 处理已委托给统一组件
import com.example.gymbro.features.workout.session.internal.util.toExerciseDto
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.shared.components.exercise.ExerciseDisplayMode
import com.example.gymbro.features.workout.shared.components.exercise.WorkoutExerciseComponent
import com.example.gymbro.shared.models.workout.session.SessionExerciseStateFactory

/**
 * 已完成动作区域组件
 *
 * 显示已完成的训练动作，支持展开/收起功能
 * 默认显示3个动作，超过时显示"显示更多"按钮
 * 使用WorkoutExerciseComponent的COMPACT模式显示
 *
 * @param completedExercises 已完成的动作列表
 * @param onExerciseReview 动作回顾回调，当用户点击展开动作时触发
 * @param modifier 修饰符
 */
@Composable
internal fun CompletedExercisesSection(
    completedExercises: List<SessionExerciseUiModel>,
    onExerciseReview: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    // 如果没有已完成的动作，不显示此区域
    if (completedExercises.isEmpty()) return

    var isExpanded by remember { mutableStateOf(false) }
    val maxVisibleItems = 3

    SessionSectionContainer(modifier = modifier) {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .standardContentSizeAnimation(),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            // 区域标题
            CompletedSectionHeader(
                completedCount = completedExercises.size,
                isExpanded = isExpanded,
                onToggleExpanded = { isExpanded = !isExpanded },
            )

            // 动作列表
            SessionSectionContent {
                val visibleExercises =
                    if (isExpanded) {
                        completedExercises
                    } else {
                        completedExercises.take(maxVisibleItems)
                    }

                visibleExercises.forEachIndexed { index, exercise ->
                    WorkoutExerciseComponent(
                        exercise = exercise.toExerciseDto(),
                        exerciseState = SessionExerciseStateFactory.completed(orderIndex = index),
                        initialDisplayMode = ExerciseDisplayMode.COMPACT,
                        autoCollapseOnComplete = true,
                        allowManualToggle = true,
                        onDisplayModeChange = { mode ->
                            if (mode == ExerciseDisplayMode.EXPANDED) {
                                onExerciseReview(exercise.exerciseId)
                            }
                        },
                    )
                }

                // 显示更多按钮
                if (completedExercises.size > maxVisibleItems && !isExpanded) {
                    ShowMoreButton(
                        remainingCount = completedExercises.size - maxVisibleItems,
                        onClick = { isExpanded = true },
                    )
                }
            }
        }
    }
}

/**
 * 已完成区域标题组件
 *
 * @param completedCount 已完成动作数量
 * @param isExpanded 是否展开
 * @param onToggleExpanded 切换展开状态回调
 */
@Composable
private fun CompletedSectionHeader(
    completedCount: Int,
    isExpanded: Boolean,
    onToggleExpanded: () -> Unit,
) {
    SessionSectionHeader(
        title = SessionStrings.completedExercisesTitle,
        subtitle = SessionStrings.completedExercisesCount(completedCount),
        icon = Icons.Default.CheckCircle,
        isExpandable = true,
        isExpanded = isExpanded,
        onToggleExpanded = onToggleExpanded,
    )
}

// ===== Preview Functions =====

@GymBroPreview
@Composable
private fun CompletedExercisesSectionPreview() {
    GymBroTheme {
        // 模拟已完成动作数据
        val mockExercises =
            remember {
                // 这里应该使用真实的SessionExerciseUiModel数据
                // 为了Preview，我们创建空列表
                emptyList<SessionExerciseUiModel>()
            }

        CompletedExercisesSection(
            completedExercises = mockExercises,
            onExerciseReview = { exerciseId ->
                WorkoutLogUtils.Exercise.debug("Review exercise: $exerciseId")
            },
        )
    }
}

@GymBroPreview
@Composable
private fun CompletedSectionHeaderPreview() {
    GymBroTheme {
        CompletedSectionHeader(
            completedCount = 5,
            isExpanded = false,
            onToggleExpanded = {},
        )
    }
}
