package com.example.gymbro.features.thinkingbox.api

/**
 * ThinkingBox 跨模块协议定义
 * 
 * 🔥 【P0修复】定义Coach与ThinkingBox之间的最小接口
 * 根据finalmermaid大纲v4要求，实现完全解耦的通信协议
 */

/**
 * Coach -> ThinkingBox 启动流式处理
 */
data class StartStream(
    val sessionId: String,
    val userMessageId: String,
    val aiResponseId: String,
    val prompt: String,
)

/**
 * ThinkingBox -> Coach 消息完成回调
 */
data class MessageComplete(
    val aiResponseId: String,
    val finalMarkdown: String,
    val tokensSnapshot: String = "",
    val thinkingNodes: String? = null, // 若需要审计/调试，可选字段
)

/**
 * ThinkingBox -> Coach 消息完成回调接口
 */
fun interface OnMessageComplete {
    fun invoke(messageId: String, finalMarkdown: String)
}

/**
 * ThinkingBox 公共API接口
 * 
 * 🔥 【P0修复】Coach仅通过此接口与ThinkingBox交互
 */
interface ThinkingBoxApi {
    /**
     * 启动流式处理
     */
    fun startStream(request: StartStream)
    
    /**
     * 设置消息完成回调
     */
    fun setOnMessageComplete(callback: OnMessageComplete)
    
    /**
     * 重置状态
     */
    fun reset()
}
