package com.example.gymbro.features.thinkingbox

import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.domain.parser.XmlStreamScanner
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingReducer
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Test

/**
 * V2→V1提升验证测试
 *
 * 验证所有核心组件在V2→V1提升后能正常工作：
 * 1. StreamingThinkingMLParser - 3-state parser功能
 * 2. DomainMapper - 事件映射功能
 * 3. ThinkingReducer - 状态管理功能
 * 4. 6个统一事件系统
 */
class V2ToV1PromotionValidationTest {

    @Test
    fun `should validate StreamingThinkingMLParser 3-state parser functionality`() = runTest {
        // 创建解析器实例
        val xmlScanner = XmlStreamScanner()
        val parser = StreamingThinkingMLParser(xmlScanner)

        // 收集解析事件
        val events = mutableListOf<SemanticEvent>()

        // 测试XML输入
        val testTokens = flowOf(
            "<think>预思考内容</think>",
            "<thinking>",
            "<phase id=\"1\">阶段1内容</phase>",
            "</thinking>",
            "<final>最终答案</final>",
        )

        // 执行解析
        parser.parseTokenStream(
            messageId = "test-001",
            tokens = testTokens,
            onEvent = { event -> events.add(event) },
        )

        // 验证解析结果
        assertTrue("应该产生解析事件", events.isNotEmpty())

        // 验证预思考事件
        val preThinkEvents = events.filterIsInstance<SemanticEvent.PreThinkChunk>()
        assertTrue("应该有预思考事件", preThinkEvents.isNotEmpty())

        // 验证阶段事件
        val phaseOpenEvents = events.filterIsInstance<SemanticEvent.TagOpened>()
            .filter { it.name == "phase" }
        assertTrue("应该有阶段开始事件", phaseOpenEvents.isNotEmpty())

        // 验证最终答案事件
        val finalEvents = events.filterIsInstance<SemanticEvent.FinalArrived>()
        assertTrue("应该有最终答案事件", finalEvents.isNotEmpty())

        println("✅ StreamingThinkingMLParser 3-state parser 验证通过")
    }

    @Test
    fun `should validate DomainMapper event mapping functionality`() {
        // 创建映射器实例
        val mapper = DomainMapper()

        // 测试语义事件映射
        val semanticEvent = SemanticEvent.PreThinkChunk("测试预思考内容")
        val context = DomainMapper.MappingContext()

        // 执行映射
        val result = mapper.mapSemanticToThinking(semanticEvent, context)

        // 验证映射结果
        assertNotNull("映射结果不应为空", result)
        assertTrue("应该产生ThinkingEvent", result.events.isNotEmpty())

        // 验证事件类型
        val thinkingEvent = result.events.first()
        assertTrue("应该是PreThinkChunk事件", thinkingEvent is ThinkingEvent.PreThinkChunk)

        // 验证事件内容
        if (thinkingEvent is ThinkingEvent.PreThinkChunk) {
            assertEquals("内容应该匹配", "测试预思考内容", thinkingEvent.content)
        }

        println("✅ DomainMapper 事件映射功能验证通过")
    }

    @Test
    fun `should validate ThinkingReducer state management functionality`() {
        // 创建初始状态
        val initialState = ThinkingReducer.ThinkingUiState()

        // 测试事件处理
        val event = ThinkingEvent.PhaseStart(id = "test-phase", title = "测试阶段")
        val newState = ThinkingReducer.reduce(initialState, event)

        // 验证状态更新
        assertNotNull("新状态不应为空", newState)
        assertEquals("活跃阶段ID应该更新", "test-phase", newState.activePhaseId)
        assertTrue("阶段映射应该包含新阶段", newState.phases.containsKey("test-phase"))

        // 验证阶段内容
        val phase = newState.phases["test-phase"]
        assertNotNull("阶段不应为空", phase)
        assertEquals("阶段标题应该匹配", "测试阶段", phase?.title)

        println("✅ ThinkingReducer 状态管理功能验证通过")
    }

    @Test
    fun `should validate 6 unified events system`() {
        // 验证6个统一事件都存在且可实例化
        val events = listOf(
            ThinkingEvent.PreThinkChunk("预思考内容"),
            ThinkingEvent.PreThinkEnd,
            ThinkingEvent.PhaseStart("phase-1", "阶段1"),
            ThinkingEvent.PhaseContent("phase-1", "阶段内容"),
            ThinkingEvent.PhaseEnd("phase-1"),
            ThinkingEvent.FinalArrived("最终答案"),
        )

        // 验证事件数量
        assertEquals("应该有6个统一事件", 6, events.size)

        // 验证每个事件类型
        assertTrue("PreThinkChunk事件", events[0] is ThinkingEvent.PreThinkChunk)
        assertTrue("PreThinkEnd事件", events[1] is ThinkingEvent.PreThinkEnd)
        assertTrue("PhaseStart事件", events[2] is ThinkingEvent.PhaseStart)
        assertTrue("PhaseContent事件", events[3] is ThinkingEvent.PhaseContent)
        assertTrue("PhaseEnd事件", events[4] is ThinkingEvent.PhaseEnd)
        assertTrue("FinalArrived事件", events[5] is ThinkingEvent.FinalArrived)

        println("✅ 6个统一事件系统验证通过")
    }

    @Test
    fun `should validate complete data flow integration`() = runTest {
        // 创建完整的数据流组件
        val xmlScanner = XmlStreamScanner()
        val parser = StreamingThinkingMLParser(xmlScanner)
        val mapper = DomainMapper()
        var mappingContext = DomainMapper.MappingContext()
        var uiState = ThinkingReducer.ThinkingUiState()

        // 测试完整数据流
        val testTokens = flowOf("<phase id=\"integration-test\">集成测试内容</phase>")

        // 执行完整流程
        parser.parseTokenStream(
            messageId = "integration-test",
            tokens = testTokens,
            onEvent = { semanticEvent ->
                // SemanticEvent → ThinkingEvent
                val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                mappingContext = mappingResult.context

                // ThinkingEvent → UiState
                mappingResult.events.forEach { thinkingEvent ->
                    uiState = ThinkingReducer.reduce(uiState, thinkingEvent)
                }
            },
        )

        // 验证最终状态
        assertNotNull("最终UI状态不应为空", uiState)
        assertEquals("活跃阶段应该是integration-test", "integration-test", uiState.activePhaseId)
        assertTrue("应该包含测试阶段", uiState.phases.containsKey("integration-test"))

        println("✅ 完整数据流集成验证通过")
    }

    @Test
    fun `should validate no V2 components remain`() {
        // 验证不再有V2组件的引用
        // 这个测试主要是编译时验证，如果有V2引用会编译失败

        // 验证使用的都是V1组件
        val parser = StreamingThinkingMLParser(XmlStreamScanner())
        val mapper = DomainMapper()
        val reducer = ThinkingReducer

        assertNotNull("Parser应该可用", parser)
        assertNotNull("Mapper应该可用", mapper)
        assertNotNull("Reducer应该可用", reducer)

        println("✅ V2组件清理验证通过 - 只使用V1组件")
    }
}
