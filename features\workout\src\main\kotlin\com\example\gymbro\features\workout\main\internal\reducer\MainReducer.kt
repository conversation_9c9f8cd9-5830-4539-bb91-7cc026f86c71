package com.example.gymbro.features.workout.main.internal.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.workout.model.calendar.TodayWorkoutInfo
import com.example.gymbro.domain.workout.model.session.WorkoutSession
import com.example.gymbro.domain.workout.model.stats.UserWorkoutStatistics
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.main.HomeContract
import kotlinx.collections.immutable.ImmutableList
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Workout模块状态Reducer - MVI 2.0 标准实现
 * 遵循BaseMviViewModel要求，实现Reducer接口，支持Effect生成
 */
@Singleton
internal class HomeReducer
@Inject
constructor() : Reducer<HomeContract.Intent, HomeContract.State, HomeContract.Effect> {

    /**
     * 根据当前状态和Intent生成新状态和Effect - MVI 2.0标准
     */
    override fun reduce(
        intent: HomeContract.Intent,
        currentState: HomeContract.State,
    ): ReduceResult<HomeContract.State, HomeContract.Effect> =
        when (intent) {
            // ✅ 修正：数据加载 Intent 现在生成 Effect
            is HomeContract.Intent.LoadInitialData,
            is HomeContract.Intent.RefreshData,
            ->
                ReduceResult.withEffect(
                    currentState.copy(
                        isLoading = true,
                        isLoadingStats = true, // 🔧 修复：设置所有相关的加载状态
                        isLoadingCalendar = true,
                        isLoadingTodayInfo = true,
                        isLoadingTemplates = true,
                        errorCode = null,
                    ),
                    HomeContract.Effect.LoadInitialDataEffect(currentState.selectedDate),
                )

            // 错误清除
            is HomeContract.Intent.ClearError ->
                ReduceResult.stateOnly(
                    currentState.copy(
                        errorCode = null,
                    ),
                )

            // ✅ 修正：日期选择现在生成 Effect
            is HomeContract.Intent.SelectDate ->
                ReduceResult.withEffect(
                    currentState.copy(
                        selectedDate = intent.date,
                        isLoadingCalendar = true,
                        isLoadingTodayInfo = true,
                    ),
                    HomeContract.Effect.LoadSpecificDateDataEffect(intent.date),
                )

            is HomeContract.Intent.ChangeMonth -> {
                // 月份变化，需要重新加载日历数据
                ReduceResult.stateOnly(
                    currentState.copy(
                        isLoadingCalendar = true,
                    ),
                )
            }

            // 数据加载结果处理
            is HomeContract.Intent.OnUserLoaded -> ReduceResult.stateOnly(
                handleUserLoadResult(currentState, intent.result),
            )

            is HomeContract.Intent.OnStatsLoaded -> ReduceResult.stateOnly(
                handleStatsLoadResult(currentState, intent.result),
            )

            is HomeContract.Intent.OnEnhancedCalendarLoaded -> ReduceResult.stateOnly(
                handleEnhancedCalendarLoadResult(currentState, intent),
            )

            is HomeContract.Intent.OnTemplatesLoaded -> ReduceResult.stateOnly(
                handleTemplatesLoadResult(currentState, intent.result),
            )

            is HomeContract.Intent.OnTodayWorkoutLoaded -> ReduceResult.stateOnly(
                handleTodayWorkoutLoadResult(currentState, intent.result),
            )

            is HomeContract.Intent.OnRecentSessionsLoaded -> ReduceResult.stateOnly(
                handleRecentSessionsLoadResult(currentState, intent.result),
            )

            is HomeContract.Intent.OnWorkoutStarted -> handleWorkoutStartResult(
                currentState,
                intent.result,
            )

            // 🔥 关键修复：导航相关Intent - 生成对应的Effect
            is HomeContract.Intent.NavigateToTemplates -> {
                WorkoutLogUtils.Template.debug(
                    "🔍 NavigateToTemplates Intent处理，当前isLoading=${currentState.isLoading}",
                )
                WorkoutLogUtils.Template.debug(
                    "🔍 详细状态: stats=${currentState.isLoadingStats}, today=${currentState.isLoadingTodayInfo}, calendar=${currentState.isLoadingCalendar}, templates=${currentState.isLoadingTemplates}",
                )
                ReduceResult.withEffect(currentState, HomeContract.Effect.NavigateToTemplates)
            }

            is HomeContract.Intent.NavigateToStats -> {
                WorkoutLogUtils.Template.debug(
                    "🔍 NavigateToStats Intent处理，当前isLoading=${currentState.isLoading}",
                )
                ReduceResult.withEffect(currentState, HomeContract.Effect.NavigateToStats)
            }

            is HomeContract.Intent.NavigateToPlans -> {
                WorkoutLogUtils.Template.debug(
                    "🔍 NavigateToPlans Intent处理，当前isLoading=${currentState.isLoading}",
                )
                ReduceResult.withEffect(currentState, HomeContract.Effect.NavigateToPlans)
            }

            is HomeContract.Intent.NavigateToExerciseLibrary ->
                ReduceResult.withEffect(currentState, HomeContract.Effect.NavigateToExerciseLibrary)

            is HomeContract.Intent.NavigateToCalendar ->
                ReduceResult.withEffect(currentState, HomeContract.Effect.NavigateToCalendar)

            // ✅ 修正：开始训练现在生成 Effect
            is HomeContract.Intent.StartQuickWorkout ->
                ReduceResult.withEffect(currentState, HomeContract.Effect.StartQuickWorkoutEffect)

            is HomeContract.Intent.StartWithTemplate ->
                ReduceResult.withEffect(
                    currentState,
                    HomeContract.Effect.StartWithTemplateEffect(intent.templateId),
                )
            is HomeContract.Intent.CompleteWorkout,
            is HomeContract.Intent.SaveAsTemplate,
            is HomeContract.Intent.ShowMoreOptions,
            is HomeContract.Intent.ShowSubscriptionOptions,
            -> ReduceResult.stateOnly(currentState)
        }

    private fun handleUserLoadResult(
        currentState: HomeContract.State,
        result: ModernResult<UserProfile?>,
    ): HomeContract.State =
        when (result) {
            is ModernResult.Success ->
                currentState.copy(
                    user = result.data,
                    isLoading = !isAllCoreDataLoaded(
                        currentState.copy(user = result.data),
                    ), // 🔧 修复：逻辑错误，应该是取反
                )
            is ModernResult.Error ->
                currentState.copy(
                    errorCode = HomeContract.ErrorCodes.USER_LOAD_FAILED,
                    isLoading = false,
                )
            is ModernResult.Loading -> currentState.copy(isLoading = true)
        }

    private fun handleStatsLoadResult(
        currentState: HomeContract.State,
        result: ModernResult<UserWorkoutStatistics>,
    ): HomeContract.State =
        when (result) {
            is ModernResult.Success ->
                currentState.copy(
                    statistics = result.data,
                    isLoadingStats = false,
                    isLoading = !isAllCoreDataLoaded(
                        currentState.copy(
                            statistics = result.data,
                            isLoadingStats = false,
                        ),
                    ), // 🔧 修复：逻辑错误，应该是取反
                )
            is ModernResult.Error ->
                currentState.copy(
                    errorCode = HomeContract.ErrorCodes.STATS_LOAD_FAILED,
                    isLoadingStats = false,
                    isLoading = false,
                )
            is ModernResult.Loading -> currentState.copy(isLoadingStats = true)
        }

    private fun handleEnhancedCalendarLoadResult(
        currentState: HomeContract.State,
        intent: HomeContract.Intent.OnEnhancedCalendarLoaded,
    ): HomeContract.State =
        currentState.copy(
            calendarEntries = intent.calendarEntries,
            totalCompletedWeight = intent.totalCompletedWeight,
            completedTrainingDays = intent.completedTrainingDays,
            totalTrainingDays = intent.totalTrainingDays,
            isLoadingCalendar = false, // 🔧 修复：更新日历加载状态
            isLoading = !isAllCoreDataLoaded(
                currentState.copy(
                    calendarEntries = intent.calendarEntries,
                    totalCompletedWeight = intent.totalCompletedWeight,
                    completedTrainingDays = intent.completedTrainingDays,
                    totalTrainingDays = intent.totalTrainingDays,
                    isLoadingCalendar = false,
                ),
            ), // 🔧 修复：逻辑错误，应该是取反
        )

    private fun handleTemplatesLoadResult(
        currentState: HomeContract.State,
        result: ModernResult<ImmutableList<WorkoutTemplate>>,
    ): HomeContract.State =
        when (result) {
            is ModernResult.Success ->
                currentState.copy(
                    userTemplates = result.data,
                    isLoadingTemplates = false, // 🔧 修复：更新模板加载状态
                    isLoading = !isAllCoreDataLoaded(
                        currentState.copy(
                            userTemplates = result.data,
                            isLoadingTemplates = false,
                        ),
                    ), // 🔧 修复：逻辑错误，应该是取反
                )
            is ModernResult.Error ->
                currentState.copy(
                    errorCode = HomeContract.ErrorCodes.UNKNOWN_ERROR,
                    isLoadingTemplates = false, // 🔧 修复：错误时也要重置加载状态
                    isLoading = false,
                )
            is ModernResult.Loading -> currentState.copy(isLoadingTemplates = true) // 🔧 修复：设置加载状态
        }

    private fun handleTodayWorkoutLoadResult(
        currentState: HomeContract.State,
        result: ModernResult<TodayWorkoutInfo?>,
    ): HomeContract.State =
        when (result) {
            is ModernResult.Success ->
                currentState.copy(
                    todayWorkout = result.data,
                    isLoadingTodayInfo = false,
                    isLoading = !isAllCoreDataLoaded(
                        currentState.copy(
                            todayWorkout = result.data,
                            isLoadingTodayInfo = false,
                        ),
                    ), // 🔧 修复：逻辑错误，应该是取反
                )
            is ModernResult.Error ->
                currentState.copy(
                    errorCode = HomeContract.ErrorCodes.UNKNOWN_ERROR,
                    isLoadingTodayInfo = false,
                    isLoading = false,
                )
            is ModernResult.Loading -> currentState.copy(isLoadingTodayInfo = true)
        }

    private fun handleRecentSessionsLoadResult(
        currentState: HomeContract.State,
        result: ModernResult<ImmutableList<WorkoutSession>>,
    ): HomeContract.State =
        when (result) {
            is ModernResult.Success ->
                currentState.copy(
                    recentSessions = result.data,
                    isLoading = !isAllCoreDataLoaded(
                        currentState.copy(recentSessions = result.data),
                    ), // 🔧 修复：逻辑错误，应该是取反
                )
            is ModernResult.Error ->
                currentState.copy(
                    errorCode = HomeContract.ErrorCodes.UNKNOWN_ERROR,
                    isLoading = false,
                )
            is ModernResult.Loading -> currentState
        }

    private fun handleWorkoutStartResult(
        currentState: HomeContract.State,
        result: ModernResult<String>,
    ): ReduceResult<HomeContract.State, HomeContract.Effect> =
        when (result) {
            is ModernResult.Success -> {
                val newState = currentState.copy(activeSessionId = result.data)
                ReduceResult.withEffect(
                    newState,
                    HomeContract.Effect.NavigateToActiveWorkout(result.data),
                )
            }
            is ModernResult.Error ->
                ReduceResult.withEffect(
                    currentState.copy(errorCode = HomeContract.ErrorCodes.WORKOUT_START_FAILED),
                    HomeContract.Effect.ShowSnackbar(HomeContract.ErrorCodes.WORKOUT_START_FAILED),
                )
            is ModernResult.Loading -> ReduceResult.stateOnly(currentState)
        }

    /**
     * 检查核心数据是否已全部加载
     * 🔧 修复：完善加载状态检查，确保所有核心数据都已加载完成
     */
    private fun isAllCoreDataLoaded(state: HomeContract.State): Boolean {
        val allLoaded = state.user != null &&
            !state.isLoadingStats &&
            !state.isLoadingTodayInfo &&
            !state.isLoadingCalendar &&
            !state.isLoadingTemplates

        WorkoutLogUtils.Template.debug(
            "🔍 isAllCoreDataLoaded: user=${state.user != null}, stats=${!state.isLoadingStats}, today=${!state.isLoadingTodayInfo}, calendar=${!state.isLoadingCalendar}, templates=${!state.isLoadingTemplates} → result=$allLoaded",
        )

        return allLoaded
    }
}
