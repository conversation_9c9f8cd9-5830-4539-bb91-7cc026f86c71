package com.example.gymbro.features.thinkingbox

import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingReducer
import org.junit.Test
import java.util.LinkedHashMap
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * Formal-Phase Timing 修复回归测试矩阵
 *
 * 基于 v1 设计方案的测试用例，验证三个核心修复：
 * 1. Ghost-phase 移除
 * 2. deferredTitles 缓冲机制
 * 3. Early-animation 守卫
 */
class FormalPhaseTimingRegressionTest {

    @Test
    fun `标准切换 - Start→Content→End→AnimFinish`() {
        // 🔥 【回归测试P0】标准phase切换流程
        val initialState = ThinkingReducer.createInitialState("test-session")

        val events = listOf(
            ThinkingEvent.PhaseStart("phase1", "Phase 1"),
            ThinkingEvent.PhaseContent("phase1", "Content 1"),
            ThinkingEvent.PhaseEnd("phase1"),
            ThinkingEvent.PhaseAnimFinished("phase1"),
        )

        var state = initialState
        events.forEach { event ->
            state = ThinkingReducer.reduce(state, event)
        }

        // 验证最终状态
        assertTrue(state.phases.containsKey("phase1"), "phase1应该存在")
        assertTrue(state.phases["phase1"]?.isComplete == true, "phase1应该完成")
        assertEquals("Phase 1", state.phases["phase1"]?.title)
        assertEquals("Content 1", state.phases["phase1"]?.content)
        assertNull(state.activePhaseId, "应该无活跃phase")
        assertTrue(state.pending.isEmpty(), "pending队列应该为空")
    }

    @Test
    fun `Title延迟到达 - Start(title null)→Content→End→TitleUpd→AnimFinish`() {
        // 🔥 【缓冲机制测试】title在phase创建后才到达
        val initialState = ThinkingReducer.createInitialState("test-session")

        val events = listOf(
            ThinkingEvent.PhaseStart("phase1", null), // title为null
            ThinkingEvent.PhaseContent("phase1", "Content"),
            ThinkingEvent.PhaseEnd("phase1"),
            ThinkingEvent.PhaseTitleUpdate("phase1", "Delayed Title"), // title延迟到达
            ThinkingEvent.PhaseAnimFinished("phase1"),
        )

        var state = initialState
        events.forEach { event ->
            state = ThinkingReducer.reduce(state, event)
        }

        // 验证title被正确应用
        assertEquals("Delayed Title", state.phases["phase1"]?.title)
        assertTrue(state.phases["phase1"]?.isComplete == true, "phase应该完成")
    }

    @Test
    fun `Title提前到达 - TitleUpd→Start→Content→End→AnimFinish`() {
        // 🔥 【缓冲机制测试】title在phase创建前就到达
        val initialState = ThinkingReducer.createInitialState("test-session")

        val events = listOf(
            ThinkingEvent.PhaseTitleUpdate("phase1", "Early Title"), // title提前到达
            ThinkingEvent.PhaseStart("phase1", null),
            ThinkingEvent.PhaseContent("phase1", "Content"),
            ThinkingEvent.PhaseEnd("phase1"),
            ThinkingEvent.PhaseAnimFinished("phase1"),
        )

        var state = initialState
        events.forEach { event ->
            state = ThinkingReducer.reduce(state, event)
        }

        // 验证缓存的title被应用
        assertEquals("Early Title", state.phases["phase1"]?.title)
        assertTrue(state.phases["phase1"]?.isComplete == true, "phase应该完成")
        assertTrue(state.deferredTitles.isEmpty(), "deferredTitles应该被清空")
    }

    @Test
    fun `Ghost阻止 - End(unknown)`() {
        // 🔥 【Ghost-phase阻止测试】对不存在的phase发送PhaseEnd
        val initialState = ThinkingReducer.createInitialState("test-session")

        val event = ThinkingEvent.PhaseEnd("nonexistent-phase")
        val newState = ThinkingReducer.reduce(initialState, event)

        // 验证没有创建ghost phase
        assertFalse(newState.phases.containsKey("nonexistent-phase"), "不应该创建ghost phase")
        assertEquals(initialState.phases.size, newState.phases.size, "状态应该保持不变")
        assertEquals(initialState.version, newState.version, "版本不应该增加")
    }

    @Test
    fun `Early-animation检测 - AnimFinish在PhaseEnd之前`() {
        // 🔥 【Early-animation守卫测试】动画完成事件在phase结束前到达
        val phase1 = ThinkingReducer.PhaseUi("phase1", "Title", "Content", isComplete = false)
        val initialState = ThinkingReducer.createInitialState("test-session")
            .copy(
                activePhaseId = "phase1",
                phases = LinkedHashMap(mapOf("phase1" to phase1)),
            )

        val event = ThinkingEvent.PhaseAnimFinished("phase1")
        val newState = ThinkingReducer.reduce(initialState, event)

        // 验证状态保持不变（因为phase未完成）
        assertEquals("phase1", newState.activePhaseId)
        assertFalse(newState.phases["phase1"]?.isComplete == true, "phase应该仍未完成")
        // 注意：这个测试主要验证日志输出，实际状态管理逻辑会保持不变
    }

    @Test
    fun `多phase切换序列测试`() {
        // 🔥 【复杂场景测试】多个phase的完整切换序列
        val initialState = ThinkingReducer.createInitialState("test-session")

        val events = listOf(
            // Phase 1
            ThinkingEvent.PhaseStart("phase1", "Phase 1"),
            ThinkingEvent.PhaseContent("phase1", "Content 1"),
            ThinkingEvent.PhaseEnd("phase1"),

            // Phase 2 在 Phase 1 完成前开始（应该进入队列）
            ThinkingEvent.PhaseStart("phase2", "Phase 2"),
            ThinkingEvent.PhaseContent("phase2", "Content 2"),

            // Phase 1 动画完成，应该切换到 Phase 2
            ThinkingEvent.PhaseAnimationFinished("phase1"),

            // Phase 2 结束
            ThinkingEvent.PhaseEnd("phase2"),
            ThinkingEvent.PhaseAnimationFinished("phase2"),
        )

        var state = initialState
        events.forEachIndexed { index, event ->
            val previousState = state
            state = ThinkingReducer.reduce(state, event)

            when (index) {
                0 -> assertEquals("phase1", state.activePhaseId) // PhaseStart激活phase1
                1 -> assertEquals("phase1", state.activePhaseId) // PhaseContent保持phase1
                2 -> assertEquals("phase1", state.activePhaseId) // PhaseEnd保持phase1
                3 -> assertEquals("phase1", state.activePhaseId) // PhaseStart(phase2)保持phase1，phase2进入队列
                4 -> assertEquals("phase1", state.activePhaseId) // PhaseContent(phase2)保持phase1
                5 -> assertEquals("phase2", state.activePhaseId) // PhaseAnimationFinished切换到phase2
                6 -> assertEquals("phase2", state.activePhaseId) // PhaseEnd保持phase2
                7 -> assertNull(state.activePhaseId) // 最后的PhaseAnimationFinished清空activePhaseId
            }
        }

        // 验证最终状态
        assertTrue(state.phases["phase1"]?.isComplete == true, "phase1应该完成")
        assertTrue(state.phases["phase2"]?.isComplete == true, "phase2应该完成")
        assertTrue(state.pending.isEmpty(), "pending队列应该为空")
    }

    @Test
    fun `Title缓冲内存管理测试`() {
        // 🔥 【内存管理测试】验证deferredTitles的正确清理
        val initialState = ThinkingReducer.createInitialState("test-session")

        // 添加多个提前到达的title
        var state = initialState
        state = ThinkingReducer.reduce(state, ThinkingEvent.PhaseTitleUpdate("phase1", "Title 1"))
        state = ThinkingReducer.reduce(state, ThinkingEvent.PhaseTitleUpdate("phase2", "Title 2"))
        state = ThinkingReducer.reduce(state, ThinkingEvent.PhaseTitleUpdate("phase3", "Title 3"))

        // 验证title被缓存
        assertEquals(3, state.deferredTitles.size)
        assertEquals("Title 1", state.deferredTitles["phase1"])
        assertEquals("Title 2", state.deferredTitles["phase2"])
        assertEquals("Title 3", state.deferredTitles["phase3"])

        // 创建phase1，应该应用并清除对应的缓存title
        state = ThinkingReducer.reduce(state, ThinkingEvent.PhaseStart("phase1", null))

        // 验证phase1的title被应用，缓存被清除
        assertEquals("Title 1", state.phases["phase1"]?.title)
        assertEquals(2, state.deferredTitles.size) // 应该减少1个
        assertFalse(state.deferredTitles.containsKey("phase1"), "phase1的title应该被清除")
        assertTrue(state.deferredTitles.containsKey("phase2"), "phase2的title应该保留")
        assertTrue(state.deferredTitles.containsKey("phase3"), "phase3的title应该保留")
    }
}
