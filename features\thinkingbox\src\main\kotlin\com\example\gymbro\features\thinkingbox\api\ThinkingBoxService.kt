package com.example.gymbro.features.thinkingbox.api

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBox 服务类
 * 
 * 🔥 【Hilt修复】专门的@Singleton服务，处理跨模块通信
 * 避免直接注入@HiltViewModel到@Singleton组件的问题
 */
@Singleton
class ThinkingBoxService @Inject constructor() {
    
    // 消息完成事件流
    private val _messageCompletionEvents = MutableSharedFlow<MessageCompletionEvent>()
    val messageCompletionEvents: SharedFlow<MessageCompletionEvent> = _messageCompletionEvents.asSharedFlow()
    
    // 流启动事件流
    private val _streamStartEvents = MutableSharedFlow<StartStreamEvent>()
    val streamStartEvents: SharedFlow<StartStreamEvent> = _streamStartEvents.asSharedFlow()
    
    // 重置事件流
    private val _resetEvents = MutableSharedFlow<Unit>()
    val resetEvents: SharedFlow<Unit> = _resetEvents.asSharedFlow()
    
    /**
     * 启动流处理
     */
    suspend fun startStream(request: StartStream) {
        _streamStartEvents.emit(
            StartStreamEvent(
                sessionId = request.sessionId,
                userMessageId = request.userMessageId,
                aiResponseId = request.aiResponseId,
                prompt = request.prompt
            )
        )
    }
    
    /**
     * 发送消息完成事件
     */
    suspend fun emitMessageCompletion(messageId: String, finalMarkdown: String) {
        _messageCompletionEvents.emit(
            MessageCompletionEvent(
                messageId = messageId,
                finalMarkdown = finalMarkdown,
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 重置ThinkingBox
     */
    suspend fun reset() {
        _resetEvents.emit(Unit)
    }
}

/**
 * 消息完成事件
 */
data class MessageCompletionEvent(
    val messageId: String,
    val finalMarkdown: String,
    val timestamp: Long
)

/**
 * 流启动事件
 */
data class StartStreamEvent(
    val sessionId: String,
    val userMessageId: String,
    val aiResponseId: String,
    val prompt: String
)
