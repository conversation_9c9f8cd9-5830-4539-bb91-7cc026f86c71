
package com.example.gymbro.features.thinkingbox.domain.model.events

/**
 * ThinkingEvent - 统一事件系统（业务层事件）
 *
 * 🎯 核心设计目标:
 * - 6个统一事件，完全替代旧事件系统
 * - 双时序架构支持：数据时序 → Reducer → 渲染时序
 * - 由 DomainMapper 从 SemanticEvent 映射而来
 * - 供 ThinkingReducer 处理，更新 UiState
 */
sealed class ThinkingEvent {

    // ===== 6个统一事件系统（业务层定义）=====

    /**
     * 预思考内容块事件
     *
     * 🔥 统一事件1：对应 <think> 标签内的流式文本内容
     * 更新 UiState.preThinking，在 Header 中显示
     *
     * @param content 预思考文本内容
     */
    data class PreThinkChunk(val content: String) : ThinkingEvent()

    /**
     * 预思考开始事件
     *
     * 🔥 【729方案2.md】对应 <think> 标签开始
     * 根据是否已有preThinking内容决定具体行为
     */
    object PreThinkStarted : ThinkingEvent()

    /**
     * 思考开始事件
     *
     * 🔥 【729方案2.md】对应 <thinking> 标签开始
     * 标记进入正式思考阶段
     */
    object ThinkingStarted : ThinkingEvent()

    /**
     * 预思考结束事件
     *
     * 🔥 统一事件2：标记 <think> 标签结束，准备切换到正式思考阶段
     * 清空 preThinking，激活 phase id="perthink"
     */
    object PreThinkEnd : ThinkingEvent()

    /**
     * 阶段开始事件
     *
     * 🔥 统一事件3：对应 <phase id="X"> 标签开始，创建新的思考阶段
     * 将阶段加入排队队列，如果当前无活跃阶段则立即激活
     *
     * @param id 阶段唯一标识符
     * @param title 阶段标题（可选，可能在后续的PhaseContent中更新）
     */
    data class PhaseStart(
        val id: String,
        val title: String? = null,
    ) : ThinkingEvent()

    /**
     * 阶段内容事件
     *
     * 🔥 统一事件4：对应阶段内的流式文本内容
     * 更新指定阶段的内容，触发打字机渲染
     *
     * @param id 阶段标识符
     * @param content 增量文本内容
     */
    data class PhaseContent(
        val id: String,
        val content: String,
    ) : ThinkingEvent()

    /**
     * 阶段完成事件
     *
     * 🔥 【729方案2.md】对应 </phase> 标签结束，标记阶段内容完整
     * 注意：此事件不会立即切换activePhaseId，需要等待UI动画完成
     *
     * @param id 阶段标识符
     */
    data class PhaseComplete(val id: String) : ThinkingEvent()

    /**
     * 最终答案开始事件
     *
     * 🔥 统一事件6a：对应 <final> 标签开始
     * 映射到 UiState → 启动后台异步渲染
     */
    object FinalStart : ThinkingEvent()

    /**
     * 最终答案内容事件
     *
     * 🔥 【729方案2.md】对应 <final> 标签内的流式文本内容
     * 映射到 UiState.finalMarkdown → TypewriterRenderer流式渲染
     */
    data class FinalContent(val text: String) : ThinkingEvent()

    /**
     * 最终答案完成事件
     *
     * 🔥 【729方案2.md】对应 </final> 标签结束
     * 设置 isFinalStreaming = false，停止TypewriterRenderer
     */
    object FinalComplete : ThinkingEvent()

    /**
     * 最终渲染就绪事件
     *
     * 🔥 【v2方案】UI层在最后一个formal phase动画完成后发送
     * 触发TypewriterRenderer挂载，开始final内容的打字机渲染
     */
    object FinalRenderingReady : ThinkingEvent()

    /**
     * 最终富文本动画完成事件
     *
     * 🔥 统一事件6c：最终富文本打字机动画完成
     * 触发：停止token计算、显示复制按钮、保存history、标记对话结束
     */
    object FinalAnimationComplete : ThinkingEvent()

    /**
     * 最终答案到达事件（已弃用）
     *
     * 🔥 【弃用】一次性事件，已被 FinalChunk + FinalEnd 替代
     * 保留用于向后兼容，新代码应使用 FinalContent + FinalComplete
     *
     * @param markdown 最终答案的Markdown内容
     */
    @Deprecated("使用 FinalContent + FinalComplete 替代一次性 FinalArrived")
    data class FinalArrived(val markdown: String) : ThinkingEvent()

    // ===== 双时序架构专用事件 =====

    /**
     * 阶段动画完成事件
     *
     * 🔥 【719施工方案2.3】Phase切换"双握手"机制
     * 由UI组件在打字机动画完成后发送，触发activePhaseId切换
     * 遵循双时序架构基线：数据时序 → UI动画完成回调 → 渲染时序
     *
     * 工作流程：
     * 1. PhaseComplete事件标记阶段内容完整，但不切换activePhaseId
     * 2. UI继续播放打字机动画直到完成
     * 3. UI发送PhaseAnimFinished事件
     * 4. Reducer响应此事件，从pending队列激活下一个phase
     *
     * @param id 完成动画的阶段标识符
     */
    data class PhaseAnimFinished(val id: String) : ThinkingEvent()

    /**
     * 显示摘要面板事件
     *
     * 🔥 【UI交互事件】当用户点击SimpleSummaryText时发送
     * 触发半屏Summary面板的显示
     */
    object ShowSummaryPanel : ThinkingEvent()

    /**
     * 摘要动画完成事件
     *
     * 🔥 【动画完成事件】当SimpleSummaryText的显示动画完成后发送
     * 标记摘要组件已完全显示，可以进行下一步操作
     */
    object SummaryAnimationComplete : ThinkingEvent()

    /**
     * 摘要卡片折叠完成事件
     *
     * 🔥 【时序协调修复】当SimpleSummaryText的折叠动画完成后发送
     * 标记可以开始最终富文本渲染，遵循双时序架构
     *
     * 工作流程：
     * 1. 思考完成，显示SimpleSummaryText
     * 2. SimpleSummaryText折叠动画完成
     * 3. 发送SummaryCardCollapsed事件
     * 4. ThinkingReducer更新finalRichTextReady状态
     * 5. StreamingFinalRenderer开始渲染
     */
    object SummaryCardCollapsed : ThinkingEvent()

    /**
     * 思考结束事件
     *
     * 对应 </thinking> 标签结束，标记全部phase计算完成
     * 用于触发思考框关闭和最终内容渲染
     */
    object ThinkingEnd : ThinkingEvent()

    /**
     * 思考框关闭完成事件
     *
     * 🔥 【P2修复】双时序握手事件
     * 当思考框关闭动画完成后发送，用于触发Final渲染
     */
    object ThinkingBoxClosed : ThinkingEvent()

    /**
     * Final渲染完成事件
     *
     * 🔥 【P2修复】双时序握手事件
     * 当Final内容渲染完成后发送，标记整个流程结束
     */
    object FinalRenderingComplete : ThinkingEvent()

    // ===== 辅助事件（向后兼容）=====

    /**
     * 阶段标题更新事件
     *
     * 对应 <title> 标签内容，用于更新阶段标题
     * 注意：此事件已被 PhaseContent 统一处理，保留用于向后兼容
     *
     * @param id 阶段标识符
     * @param title 阶段标题内容
     */
    data class PhaseTitleUpdate(
        val id: String,
        val title: String,
    ) : ThinkingEvent()
}
