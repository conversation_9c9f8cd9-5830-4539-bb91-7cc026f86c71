package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.features.thinkingbox.domain.model.Segment
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import timber.log.Timber
import java.util.ArrayDeque
import javax.inject.Inject
import javax.inject.Singleton

/**
 * SegmentQueueReducer - 基于Segment队列模型的Reducer（729方案3.md）
 *
 * 🎯 核心职责：
 * - 实现Segment队列管理：创建→填充→闭合→渲染→移除
 * - 处理三断点规则：<thinking>、</phase>、</thinking>
 * - 管理双时序架构：Token数据流 + UI渲染队列
 * - 支持finalBuffer后台累积
 *
 * 🔥 架构原则：
 * - 纯函数Reducer，无副作用
 * - 队列保证UI渲染顺序
 * - 状态不可变，支持时间旅行调试
 */
@Singleton
class SegmentQueueReducer @Inject constructor() {

    /**
     * ReduceResult - Reducer结果包装（支持History写入方案B）
     */
    data class ReduceResult(
        val state: TBState,
        val effects: List<ThinkingBoxContract.Effect> = emptyList()
    )

    /**
     * TBState - Segment队列架构状态（729方案3.md）
     * 替换原有复杂状态结构，采用简化的队列模型
     */
    data class TBState(
        val current: Segment? = null, // 当前写入中的段
        val queue: ArrayDeque<Segment> = ArrayDeque(), // 闭合待渲染段队列
        val finalBuffer: StringBuilder = StringBuilder(), // <final>后的所有token
        val thinkingClosed: Boolean = false, // 是否收到</thinking>
        val finalClosed: Boolean = false, // 是否收到</final>
        val streaming: Boolean = true, // 是否还在接收token流

        // 元数据
        val messageId: String? = null,
        val startTime: Long = System.currentTimeMillis(),
        val version: Long = 0L, // 用于触发UI重组
    ) {
        /**
         * 获取下一个待渲染的段
         * UI只渲染队列头部的段
         */
        fun getNextSegmentToRender(): Segment? = queue.firstOrNull()

        /**
         * 判断思考框是否应该关闭
         * 条件：思考阶段结束 && 队列为空
         */
        fun shouldCloseThinkingBox(): Boolean = thinkingClosed && queue.isEmpty()

        /**
         * 判断最终内容是否准备好渲染
         * 条件：思考框已关闭 && finalBuffer不为空
         */
        fun isFinalReadyToRender(): Boolean = shouldCloseThinkingBox() && finalBuffer.isNotEmpty()

        /**
         * 获取最终内容文本
         */
        fun getFinalContent(): String = finalBuffer.toString()

        /**
         * 计算总段数（用于调试和监控）
         */
        fun getTotalSegmentCount(): Int = queue.size + (if (current != null) 1 else 0)

        /**
         * 获取状态摘要（用于日志）
         */
        fun getSummary(): String = "TBState(current=${current?.id}, queue=${queue.size}, " +
                "finalBuffer=${finalBuffer.length}, thinkingClosed=$thinkingClosed, " +
                "finalClosed=$finalClosed, streaming=$streaming)"
    }

    /**
     * 主Reduce方法 - 处理ThinkingEvent并更新状态（支持History写入方案B）
     *
     * @param state 当前状态
     * @param event 输入事件
     * @return ReduceResult包含新状态和History写入Effects
     */
    fun reduce(state: TBState, event: ThinkingEvent): ReduceResult {
        Timber.tag("TB-REDUCER").d("🔄 处理事件: ${event::class.simpleName}")

        val result = when (event) {
            // ===== Segment生命周期事件 =====

            is ThinkingEvent.SegmentStarted -> {
                Timber.tag("TB-REDUCER").d("🎯 [SegmentStarted] 创建段: ${event.id} (${event.kind.name})")

                // 如果有当前段，先闭合它
                val updatedQueue = if (state.current != null) {
                    val closedCurrent = state.current.copySegment(closed = true)
                    ArrayDeque(state.queue).apply { addLast(closedCurrent) }
                } else {
                    state.queue
                }

                // 创建新段
                val newSegment = Segment(
                    id = event.id,
                    kind = event.kind,
                    title = event.title,
                    text = StringBuilder(),
                    closed = false,
                    rendered = false
                )

                ReduceResult(
                    state = state.copy(
                        current = newSegment,
                        queue = updatedQueue,
                        version = state.version + 1
                    )
                )
            }

            is ThinkingEvent.SegmentText -> {
                if (state.current != null) {
                    Timber.tag("TB-REDUCER").d("📝 [SegmentText] 追加文本到段[${state.current.id}]: ${event.text.take(50)}...")
                    state.current.text.append(event.text)
                    ReduceResult(
                        state = state.copy(version = state.version + 1)
                    )
                } else {
                    Timber.tag("TB-REDUCER").w("❌ [SegmentText] 无当前段，忽略文本: ${event.text.take(50)}...")
                    ReduceResult(state = state)
                }
            }

            is ThinkingEvent.SegmentClosed -> {
                Timber.tag("TB-REDUCER").d("🔒 [SegmentClosed] 闭合段: ${event.id}")

                if (state.current?.id == event.id) {
                    // 闭合当前段并加入队列
                    val closedSegment = state.current.copySegment(closed = true)
                    val updatedQueue = ArrayDeque(state.queue).apply { addLast(closedSegment) }

                    ReduceResult(
                        state = state.copy(
                            current = null,
                            queue = updatedQueue,
                            version = state.version + 1
                        )
                    )
                } else {
                    // 段ID不匹配，记录警告但不更新状态
                    Timber.tag("TB-REDUCER").w("❌ [SegmentClosed] 段ID不匹配: 期望=${state.current?.id}, 实际=${event.id}")
                    ReduceResult(state = state)
                }
            }

            is ThinkingEvent.UiSegmentRendered -> {
                Timber.tag("TB-REDUCER").d("✅ [UiSegmentRendered] UI完成渲染段: ${event.id}")

                // 从队列中移除已渲染的段
                val updatedQueue = ArrayDeque(state.queue)
                if (updatedQueue.firstOrNull()?.id == event.id) {
                    updatedQueue.removeFirst()
                    Timber.tag("TB-QUEUE").d("📤 队列移除段: ${event.id}, 剩余: ${updatedQueue.size}")
                } else {
                    Timber.tag("TB-REDUCER").w("❌ [UiSegmentRendered] 队列头段ID不匹配: 期望=${updatedQueue.firstOrNull()?.id}, 实际=${event.id}")
                }

                ReduceResult(
                    state = state.copy(
                        queue = updatedQueue,
                        version = state.version + 1
                    )
                )
            }

            // ===== 思考阶段控制事件 =====

            is ThinkingEvent.ThinkingClosed -> {
                Timber.tag("TB-REDUCER").d("🔚 [ThinkingClosed] 思考阶段结束，触发History写入")

                // 🔥 【History写入方案B】思考阶段结束时写入思考历史
                val thinkingMarkdown = buildThinkingMarkdown(state)
                val historyEffect = if (thinkingMarkdown.isNotEmpty() && state.messageId != null) {
                    listOf(ThinkingBoxContract.Effect.NotifyHistoryThinking(
                        messageId = state.messageId,
                        thinkingMarkdown = thinkingMarkdown
                    ))
                } else {
                    emptyList()
                }

                ReduceResult(
                    state = state.copy(
                        thinkingClosed = true,
                        version = state.version + 1
                    ),
                    effects = historyEffect
                )
            }

            // ===== Final内容处理事件 =====

            is ThinkingEvent.FinalStart -> {
                Timber.tag("TB-REDUCER").d("🔥 [FinalStart] 激活finalBuffer")
                ReduceResult(
                    state = state.copy(version = state.version + 1)
                )
            }

            is ThinkingEvent.FinalContent -> {
                Timber.tag("TB-REDUCER").d("📄 [FinalContent] 追加final内容: ${event.text.take(50)}...")
                state.finalBuffer.append(event.text)
                ReduceResult(
                    state = state.copy(version = state.version + 1)
                )
            }

            is ThinkingEvent.FinalComplete -> {
                Timber.tag("TB-REDUCER").d("🏁 [FinalComplete] Final内容完成，触发History写入")

                // 🔥 【History写入方案B】Final内容完成时写入最终答案历史
                val finalMarkdown = state.getFinalContent()
                val historyEffect = if (finalMarkdown.isNotEmpty() && state.messageId != null) {
                    listOf(ThinkingBoxContract.Effect.NotifyHistoryFinal(
                        messageId = state.messageId,
                        finalMarkdown = finalMarkdown
                    ))
                } else {
                    emptyList()
                }

                ReduceResult(
                    state = state.copy(
                        finalClosed = true,
                        streaming = false,
                        version = state.version + 1
                    ),
                    effects = historyEffect
                )
            }

            // ===== 向后兼容事件 =====

            is ThinkingEvent.FinalArrived -> {
                @Suppress("DEPRECATION")
                Timber.tag("TB-REDUCER").d("🔄 [兼容] FinalArrived: ${event.markdown.take(50)}...")

                // 兼容处理：如果finalBuffer为空，使用FinalArrived的内容
                if (state.finalBuffer.isEmpty()) {
                    state.finalBuffer.append(event.markdown)
                }

                ReduceResult(
                    state = state.copy(
                        finalClosed = true,
                        streaming = false,
                        version = state.version + 1
                    )
                )
            }

            is ThinkingEvent.PhaseTitleUpdate -> {
                @Suppress("DEPRECATION")
                Timber.tag("TB-REDUCER").d("🔄 [兼容] PhaseTitleUpdate已弃用，忽略")
                ReduceResult(state = state)
            }
        }

        // 记录状态变化
        if (result.state.version != state.version) {
            Timber.tag("TB-REDUCER").d("📊 状态更新: ${result.state.getSummary()}")
        }

        // 记录Effects
        if (result.effects.isNotEmpty()) {
            Timber.tag("TB-REDUCER").d("🔥 [History写入] 生成Effects: ${result.effects.size}个")
            result.effects.forEach { effect ->
                Timber.tag("TB-REDUCER").d("  - ${effect::class.simpleName}")
            }
        }

        return result
    }

    /**
     * 批量处理事件 - 优化性能（支持History写入方案B）
     */
    fun reduceBatch(state: TBState, events: List<ThinkingEvent>): ReduceResult {
        val allEffects = mutableListOf<ThinkingBoxContract.Effect>()
        val finalState = events.fold(state) { currentState, event ->
            val result = reduce(currentState, event)
            allEffects.addAll(result.effects)
            result.state
        }
        return ReduceResult(state = finalState, effects = allEffects)
    }

    /**
     * 构建思考历史的Markdown内容（History写入方案B）
     */
    private fun buildThinkingMarkdown(state: TBState): String {
        val markdown = StringBuilder()

        // 添加当前段（如果存在）
        state.current?.let { current ->
            if (current.text.isNotEmpty()) {
                markdown.append("## ${current.title ?: "思考阶段"}\n")
                markdown.append(current.getTextContent())
                markdown.append("\n\n")
            }
        }

        // 添加队列中的段
        state.queue.forEach { segment ->
            if (segment.text.isNotEmpty()) {
                markdown.append("## ${segment.title ?: "思考阶段"}\n")
                markdown.append(segment.getTextContent())
                markdown.append("\n\n")
            }
        }

        return markdown.toString().trim()
    }
}
