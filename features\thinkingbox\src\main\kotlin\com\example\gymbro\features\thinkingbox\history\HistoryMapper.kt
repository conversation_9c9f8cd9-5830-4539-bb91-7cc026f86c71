package com.example.gymbro.features.thinkingbox.history

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingReducer
import com.example.gymbro.features.thinkingbox.internal.model.BlockContent
import com.example.gymbro.features.thinkingbox.internal.model.NodeType
import com.example.gymbro.features.thinkingbox.internal.model.RenderableNode
import com.example.gymbro.features.thinkingbox.internal.model.toAnnotatedString
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBox 历史映射器
 *
 * 负责将思考过程转换为可持久化的格式，支持历史回放功能
 */
@Singleton
class HistoryMapper @Inject constructor() {

    companion object {
        private const val TAG = "TB-HISTORY"
    }

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        prettyPrint = false
    }

    /**
     * 将思考节点列表序列化为 JSON 字符串
     */
    fun serializeThinkingNodes(nodes: List<RenderableNode>): String? {
        return try {
            if (nodes.isEmpty()) {
                null
            } else {
                val serializableNodes = nodes.map { it.toSerializable() }
                json.encodeToString(serializableNodes)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "序列化思考节点失败: ${nodes.size} 个节点")
            null
        }
    }

    /**
     * 从 JSON 字符串反序列化思考节点列表
     */
    fun deserializeThinkingNodes(jsonString: String?): List<RenderableNode> {
        return try {
            if (jsonString.isNullOrBlank()) {
                emptyList()
            } else {
                val serializableNodes = json.decodeFromString<List<SerializableRenderableNode>>(jsonString)
                serializableNodes.map { it.toRenderableNode() }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "反序列化思考节点失败: $jsonString")
            emptyList()
        }
    }

    /**
     * 从 SemanticEvent.TagClosed("final") 提取思考节点
     */
    fun extractThinkingNodesFromFinalEvent(
        finalEvent: SemanticEvent.TagClosed,
        allNodes: List<RenderableNode>,
    ): List<RenderableNode> {
        return try {
            // 过滤出思考相关的节点，排除最终答案节点
            val thinkingNodes = allNodes.filter { node ->
                node.type != NodeType.FINAL_RESULT &&
                    node.block.content.text.isNotBlank()
            }

            Timber.tag(TAG).d("提取思考节点: 总节点 ${allNodes.size}, 思考节点 ${thinkingNodes.size}")
            thinkingNodes
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "提取思考节点失败")
            emptyList()
        }
    }

    /**
     * 检查是否需要保存思考节点
     */
    fun shouldSaveThinkingNodes(nodes: List<RenderableNode>): Boolean {
        return nodes.isNotEmpty() && nodes.any { node ->
            node.type == NodeType.PHASE &&
                node.block.content.text.isNotBlank()
        }
    }

    /**
     * 检查ThinkingUiState是否可以持久化
     *
     * @param thinkingState 思考状态
     * @return 是否可以持久化
     */
    fun canPersist(thinkingState: ThinkingReducer.ThinkingUiState): Boolean {
        return thinkingState.finalMarkdown != null &&
            thinkingState.isCompleted &&
            thinkingState.finalMarkdown!!.isNotBlank()
    }

    /**
     * 将ThinkingUiState转换为历史实体数据
     *
     * @param thinkingState 思考状态
     * @param sessionId 会话ID
     * @return 历史数据映射
     */
    fun toHistoryData(
        thinkingState: ThinkingReducer.ThinkingUiState,
        sessionId: String,
    ): ThinkingHistoryData {
        return ThinkingHistoryData(
            sessionId = sessionId,
            finalMarkdown = thinkingState.finalMarkdown,
            summary = thinkingState.finalMarkdown?.take(120),
            phaseCount = thinkingState.phases.size,
            thinkingDuration = formatThinkingDuration(thinkingState.thinkingDuration),
            sources = emptyList(), // V2系统暂不支持sources
            searchCount = 0, // V2系统暂不支持searchCount
            sourceCount = 0, // V2系统暂不支持sourceCount
            timestamp = System.currentTimeMillis(),
            // 🔥 数据流统一：直接使用新的持久化字段
            functionCall = null, // V2系统暂不支持functionCall
            mcp = null, // V2系统暂不支持mcp
            rawTokens = null, // V2系统暂不支持rawTokens
        )
    }

    /**
     * 🔥 【唯一真源性修复】格式化思考时长
     * 统一使用与UiState.getFormattedDuration()相同的格式
     */
    private fun formatThinkingDuration(durationMs: Long): String {
        if (durationMs <= 0) return "00:00"
        val seconds = durationMs / 1000
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        return String.format("%02d:%02d", minutes, remainingSeconds)
    }

    // 🔥 数据流统一：移除旧的提取方法，直接使用ThinkingReducer.ThinkingUiState中的新字段
}

/**
 * ThinkingBox历史数据
 *
 * 基于 626coach-thinkingbox.md 文档要求的完整字段结构
 */
data class ThinkingHistoryData(
    val sessionId: String,
    val finalMarkdown: String?,
    val summary: String?,
    val phaseCount: Int,
    val thinkingDuration: String,
    val sources: List<String>,
    val searchCount: Int,
    val sourceCount: Int,
    val timestamp: Long,
    // 🔥 Phase 3 扩展：Function Call 和 MCP 支持
    val functionCall: String? = null, // JSON格式存储
    val mcp: String? = null, // JSON格式存储，可为空
    val rawTokens: String? = null, // 可选的调试信息
)

/**
 * 可序列化的 RenderableNode 数据类
 */
@Serializable
data class SerializableRenderableNode(
    val id: String,
    val type: String,
    val title: String,
    val content: String,
    val phase: String? = null,
    val level: Int = 0,
    val isExpanded: Boolean = false,
    val timestamp: Long = System.currentTimeMillis(),
)

/**
 * RenderableNode 扩展函数：转换为可序列化格式
 */
private fun RenderableNode.toSerializable(): SerializableRenderableNode {
    return SerializableRenderableNode(
        id = this.id,
        type = this.type.name,
        title = this.block.title,
        content = this.block.content.text,
        phase = this.block.metadata["phase"],
        level = this.level,
        isExpanded = this.isExpanded,
        timestamp = this.timestamp,
    )
}

/**
 * SerializableRenderableNode 扩展函数：转换为 RenderableNode
 */
private fun SerializableRenderableNode.toRenderableNode(): RenderableNode {
    val metadata = mutableMapOf<String, String>()
    if (this.phase != null) {
        metadata["phase"] = this.phase
    }

    return RenderableNode(
        id = this.id,
        level = this.level,
        block = BlockContent(
            title = this.title,
            content = this.content.toAnnotatedString(),
            metadata = metadata,
        ),
        isExpanded = this.isExpanded,
        type = NodeType.valueOf(this.type),
        timestamp = this.timestamp,
    )
}

/**
 * 历史回放工具类
 */
object ThinkingHistoryReplayUtils {

    /**
     * 为历史回放准备节点数据
     */
    fun prepareNodesForReplay(nodes: List<RenderableNode>): List<RenderableNode> {
        return nodes.map { node ->
            // 重置展开状态，让用户可以重新交互
            node.copy(isExpanded = false)
        }.sortedBy { it.timestamp }
    }

    /**
     * 检查节点数据的完整性
     */
    fun validateNodesIntegrity(nodes: List<RenderableNode>): Boolean {
        return nodes.all { node ->
            node.id.isNotBlank() &&
                node.block.content.text.isNotBlank() &&
                node.timestamp > 0
        }
    }

    /**
     * 获取思考过程摘要
     */
    fun getThinkingProcessSummary(nodes: List<RenderableNode>): String {
        val phaseNodes = nodes.filter { it.type == NodeType.PHASE }
        return if (phaseNodes.isEmpty()) {
            "无思考过程"
        } else {
            val phases = phaseNodes.map { it.block.metadata["phase"] ?: "未知阶段" }.distinct()
            "思考阶段: ${phases.joinToString(" → ")}"
        }
    }
}
