   I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=2, 内容预览='用户'
14:41:07.144 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.144                          E  🔍 [Token输出] [0] Text(content=用户)
14:41:07.144 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
14:41:07.144 TB-PARSER                V  📝 [文本内容] 用户... in state: PRE_THINK
14:41:07.146 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.146 TB-MAPPER                I  🔥 [创建perthink段] 检测到首个纯文本，创建perthink段: '用户...'
14:41:07.148 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentStarted
14:41:07.148 TB-REDUCER               D  🔄 处理事件: SegmentStarted
14:41:07.148                          D  🎯 [SegmentStarted] 创建段: perthink (PERTHINK)
14:41:07.149                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.149 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.149 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.149                          D  📝 [SegmentText] 追加文本到段[perthink]: 用户...
14:41:07.149                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.150 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计1个tokens
14:41:07.150 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由1个tokens
14:41:07.155                          I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=2, 内容预览='25'
14:41:07.155 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.155                          E  🔍 [Token输出] [0] Text(content=25)
14:41:07.156 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
14:41:07.156 TB-PARSER                V  📝 [文本内容] 25... in state: PRE_THINK
14:41:07.156 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.156                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.156 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.156                          D  📝 [SegmentText] 追加文本到段[perthink]: 25...
14:41:07.156                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.156 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计2个tokens
14:41:07.156 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由2个tokens
14:41:07.160 TB-UI                    D  🎯 [渲染段] test-perthink (PERTHINK)
14:41:07.210                          D  🔄 [Segment队列] 队列大小=1, 思考关闭=false, 流式=true
14:41:07.329 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=1, 内容预览='岁'
14:41:07.330 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.330                          E  🔍 [Token输出] [0] Text(content=岁)
14:41:07.330 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入1字符, 输出1个Token, 缓冲剩余0字符
14:41:07.330 TB-PARSER                V  📝 [文本内容] 岁... in state: PRE_THINK
14:41:07.330 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.331                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.331 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.331                          D  📝 [SegmentText] 追加文本到段[perthink]: 岁...
14:41:07.331                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.332 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计3个tokens
14:41:07.332 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由3个tokens
14:41:07.332                          I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=1, 内容预览='，'
14:41:07.333 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.333                          E  🔍 [Token输出] [0] Text(content=，)
14:41:07.333 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入1字符, 输出1个Token, 缓冲剩余0字符
14:41:07.333 TB-PARSER                V  📝 [文本内容] ，... in state: PRE_THINK
14:41:07.333 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.333                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.334 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.334                          D  📝 [SegmentText] 追加文本到段[perthink]: ，...
14:41:07.334                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.334 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计4个tokens
14:41:07.334 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由4个tokens
14:41:07.335                          I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=1, 内容预览='初'
14:41:07.335 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.336                          E  🔍 [Token输出] [0] Text(content=初)
14:41:07.336 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入1字符, 输出1个Token, 缓冲剩余0字符
14:41:07.337 TB-PARSER                V  📝 [文本内容] 初... in state: PRE_THINK
14:41:07.337 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.337                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.337 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.337                          D  📝 [SegmentText] 追加文本到段[perthink]: 初...
14:41:07.337                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.338 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计5个tokens
14:41:07.338 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由5个tokens
14:41:07.338                          I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=1, 内容预览='学'
14:41:07.339 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.340                          E  🔍 [Token输出] [0] Text(content=学)
14:41:07.340 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入1字符, 输出1个Token, 缓冲剩余0字符
14:41:07.340 TB-PARSER                V  📝 [文本内容] 学... in state: PRE_THINK
14:41:07.340 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.340                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.340 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.340                          D  📝 [SegmentText] 追加文本到段[perthink]: 学...
14:41:07.340                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.341 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计6个tokens
14:41:07.341 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由6个tokens
14:41:07.345 TB-UI                    D  🎯 [渲染段] test-perthink (PERTHINK)
14:41:07.470 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=2, 内容预览='健身'
14:41:07.470 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.470                          E  🔍 [Token输出] [0] Text(content=健身)
14:41:07.470 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
14:41:07.471 TB-PARSER                V  📝 [文本内容] 健身... in state: PRE_THINK
14:41:07.471 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.471                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.471 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.471                          D  📝 [SegmentText] 追加文本到段[perthink]: 健身...
14:41:07.471                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.471 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计7个tokens
14:41:07.472 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由7个tokens
14:41:07.472                          I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=1, 内容预览='，'
14:41:07.472 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.472                          E  🔍 [Token输出] [0] Text(content=，)
14:41:07.472 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入1字符, 输出1个Token, 缓冲剩余0字符
14:41:07.472 TB-PARSER                V  📝 [文本内容] ，... in state: PRE_THINK
14:41:07.472 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.472                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.473 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.473                          D  📝 [SegmentText] 追加文本到段[perthink]: ，...
14:41:07.473                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.473 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计8个tokens
14:41:07.473 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由8个tokens
14:41:07.474                          I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=2, 内容预览='身材'
14:41:07.474 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.474                          E  🔍 [Token输出] [0] Text(content=身材)
14:41:07.475 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
14:41:07.475 TB-PARSER                V  📝 [文本内容] 身材... in state: PRE_THINK
14:41:07.475 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.475                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.475 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.475                          D  📝 [SegmentText] 追加文本到段[perthink]: 身材...
14:41:07.475                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.476 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计9个tokens
14:41:07.476 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由9个tokens
14:41:07.476                          I  🔥 [TokenRouter] 路由Token: messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, token长度=2, 内容预览='标准'
14:41:07.477 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:41:07.477                          E  🔍 [Token输出] [0] Text(content=标准)
14:41:07.477 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
14:41:07.477 TB-PARSER                V  📝 [文本内容] 标准... in state: PRE_THINK
14:41:07.477 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
14:41:07.477                          D  🔄 [处理ThinkingEvent] SegmentText
14:41:07.477 TB-REDUCER               D  🔄 处理事件: SegmentText
14:41:07.477                          D  📝 [SegmentText] 追加文本到段[perthink]: 标准...
14:41:07.478                          D  📊 状态更新: TBState(current=perthink, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:41:07.478 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=01dcb732-6014-459c-abc9-ef6c43a7aa79, 总计10个tokens
14:41:07.478 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由10个tokens
14:41:07.483 TB-UI                    D  🎯 [渲染段] test-perthink (PERTHINK)
