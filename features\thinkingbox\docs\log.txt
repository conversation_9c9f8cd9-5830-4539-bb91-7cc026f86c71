15:35:47.978 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, token长度=2, 内容预览='作为'
15:35:47.994 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:35:47.994                          E  🔍 [Token输出] [0] Text(content=作为)
15:35:47.994 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
15:35:47.995 TB-PARSER                V  📝 [文本内容] 作为... in state: PRE_THINK
15:35:47.996 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
15:35:47.996 TB-MAPPER                I  🔥 [创建perthink段] 检测到首个纯文本，创建perthink段: '作为...'
15:35:48.007 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentStarted
15:35:48.007 TB-REDUCER               D  🔄 处理事件: SegmentStarted
15:35:48.007                          D  🎯 [SegmentStarted] 创建段: perthink (PERTHINK)
15:35:48.011                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:35:48.013 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentText
15:35:48.014 TB-REDUCER               D  🔄 处理事件: SegmentText
15:35:48.014                          D  📝 [SegmentText] 追加文本到段[perthink]: 作为...
15:35:48.014                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:35:48.018 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, 总计1个tokens
15:35:48.018 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由1个tokens
15:35:48.025                          I  🔥 [TokenRouter] 路由Token: messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, token长度=2, 内容预览='力量'
15:35:48.026 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:35:48.026                          E  🔍 [Token输出] [0] Text(content=力量)
15:35:48.026 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
15:35:48.026 TB-PARSER                V  📝 [文本内容] 力量... in state: PRE_THINK
15:35:48.026 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
15:35:48.026                          D  🔄 [处理ThinkingEvent] SegmentText
15:35:48.026 TB-REDUCER               D  🔄 处理事件: SegmentText
15:35:48.027                          D  📝 [SegmentText] 追加文本到段[perthink]: 力量...
15:35:48.027                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:35:48.028 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, 总计2个tokens
15:35:48.028 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由2个tokens
15:35:48.035 TB-UI                    D  🎯 [无内容] Segment队列为空
15:35:48.107 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, token长度=2, 内容预览='训练'
15:35:48.107 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:35:48.107                          E  🔍 [Token输出] [0] Text(content=训练)
15:35:48.107 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
15:35:48.108 TB-PARSER                V  📝 [文本内容] 训练... in state: PRE_THINK
15:35:48.109 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
15:35:48.109                          D  🔄 [处理ThinkingEvent] SegmentText
15:35:48.109 TB-REDUCER               D  🔄 处理事件: SegmentText
15:35:48.109                          D  📝 [SegmentText] 追加文本到段[perthink]: 训练...
15:35:48.109                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:35:48.110 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, 总计3个tokens
15:35:48.110 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由3个tokens
15:35:48.110                          I  🔥 [TokenRouter] 路由Token: messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, token长度=3, 内容预览='初学者'
15:35:48.111 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:35:48.111                          E  🔍 [Token输出] [0] Text(content=初学者)
15:35:48.111 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入3字符, 输出1个Token, 缓冲剩余0字符
15:35:48.111 TB-PARSER                V  📝 [文本内容] 初学者... in state: PRE_THINK
15:35:48.111 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
15:35:48.112                          D  🔄 [处理ThinkingEvent] SegmentText
15:35:48.112 TB-REDUCER               D  🔄 处理事件: SegmentText
15:35:48.112                          D  📝 [SegmentText] 追加文本到段[perthink]: 初学者...
15:35:48.112                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:35:48.112 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, 总计4个tokens
15:35:48.112 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由4个tokens
15:35:48.112                          I  🔥 [TokenRouter] 路由Token: messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, token长度=1, 内容预览='，'
15:35:48.113 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:35:48.113                          E  🔍 [Token输出] [0] Text(content=，)
15:35:48.113 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入1字符, 输出1个Token, 缓冲剩余0字符
15:35:48.113 TB-PARSER                V  📝 [文本内容] ，... in state: PRE_THINK
15:35:48.113 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
15:35:48.113                          D  🔄 [处理ThinkingEvent] SegmentText
15:35:48.113 TB-REDUCER               D  🔄 处理事件: SegmentText
15:35:48.113                          D  📝 [SegmentText] 追加文本到段[perthink]: ，...
15:35:48.113                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:35:48.114 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, 总计5个tokens
15:35:48.114 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由5个tokens
15:35:48.115                          I  🔥 [TokenRouter] 路由Token: messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, token长度=3, 内容预览='我需要'
15:35:48.116 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:35:48.116                          E  🔍 [Token输出] [0] Text(content=我需要)
15:35:48.116 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入3字符, 输出1个Token, 缓冲剩余0字符
15:35:48.116 TB-PARSER                V  📝 [文本内容] 我需要... in state: PRE_THINK
15:35:48.116 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
15:35:48.116                          D  🔄 [处理ThinkingEvent] SegmentText
15:35:48.116 TB-REDUCER               D  🔄 处理事件: SegmentText
15:35:48.117                          D  📝 [SegmentText] 追加文本到段[perthink]: 我需要...
15:35:48.117                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:35:48.117 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, 总计6个tokens
15:35:48.117 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由6个tokens
15:35:48.122 TB-UI                    D  🎯 [无内容] Segment队列为空
15:35:48.227 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, token长度=3, 内容预览='为用户'
15:35:48.228 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:35:48.228                          E  🔍 [Token输出] [0] Text(content=为用户)
15:35:48.228 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入3字符, 输出1个Token, 缓冲剩余0字符
15:35:48.229 TB-PARSER                V  📝 [文本内容] 为用户... in state: PRE_THINK
15:35:48.229 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
15:35:48.229                          D  🔄 [处理ThinkingEvent] SegmentText
15:35:48.229 TB-REDUCER               D  🔄 处理事件: SegmentText
15:35:48.232                          D  📝 [SegmentText] 追加文本到段[perthink]: 为用户...
15:35:48.232                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:35:48.235 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, 总计7个tokens
15:35:48.235 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由7个tokens
15:35:48.235                          I  🔥 [TokenRouter] 路由Token: messageId=3ad06d97-4600-4b3b-a5ae-6894e3670321, token长度=2, 内容预览='提供'
15:35:48.236 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:35:48.236                          E  🔍 [Token输出] [0] Text(content=提供)
15:35:48.236 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入2字符, 输出1个Token, 缓冲剩余0字符
15:35:48.237 TB-PARSER                V  📝 [文本内容] 提供... in state: PRE_THINK
15:35:48.237 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
15:35:48.237                          D  🔄 [处理ThinkingEvent] SegmentText
15:35:48.237 TB-REDUCER               D  🔄 处理事件: SegmentText
15:35:48.238                          D  📝 [SegmentText] 追加文本到段[perthink]: 提供...
