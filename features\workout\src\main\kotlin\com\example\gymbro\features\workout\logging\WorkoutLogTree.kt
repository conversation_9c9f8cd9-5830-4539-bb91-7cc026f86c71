package com.example.gymbro.features.workout.logging

import timber.log.Timber

/**
 * Workout模块专用日志工具类 - 【重构清理】
 *
 * 🎯 功能：
 * - 提供workout模块统一的日志标签和工具方法
 * - 使用WK前缀标识workout模块日志
 * - 现在作为静态工具类，不再继承Timber.Tree
 * - 消除与ModuleAwareTree的重复处理
 *
 * 🔥 使用示例：
 * ```kotlin
 * Timber.tag(WorkoutLogUtils.TAG_CORE).i("模板保存成功")
 * Timber.tag(WorkoutLogUtils.TAG_EXERCISE).d("动作添加: ${exercise.name}")
 * WorkoutLogUtils.logSaveStep("START", templateName)
 * ```
 */
object WorkoutLogUtils {

    // === 整合后的日志标签定义（减少重复）===

    /** 核心业务流程 (合并 WK-TEMPLATE + WK-CRITICAL) */
    const val TAG_CORE = "WK-CORE"

    /** 数据处理 (合并 WK-MAPPER + WK-VALIDATION + WK-DB) */
    const val TAG_DATA = "WK-DATA"

    /** 动作相关操作 */
    const val TAG_EXERCISE = "WK-EXERCISE"

    /** 调试相关 (仅关键调试信息) */
    const val TAG_DEBUG = "WK-DEBUG"

    /** 错误相关 */
    const val TAG_ERROR = "WK-ERROR"

    /** 性能相关 */
    const val TAG_PERFORMANCE = "WK-PERF"

    // === 向后兼容的旧标签（保持现有代码工作）===
    /** @deprecated 使用 TAG_CORE 替代 */
    const val TAG_TEMPLATE = TAG_CORE

    /** @deprecated 使用 TAG_DATA 替代 */
    const val TAG_JSON = TAG_DATA

    /** @deprecated 使用 TAG_DATA 替代 */
    const val TAG_DATABASE = TAG_DATA

    /** @deprecated 使用 TAG_CORE 替代 */
    const val TAG_CRITICAL = TAG_CORE

    /** @deprecated 使用 TAG_DATA 替代 */
    const val TAG_VALIDATION = TAG_DATA

    // === 统一日志方法 - 消除重复信息，现在作为静态工具方法 ===

    /**
     * 🔥 保存链路跟踪 - 统一WK前缀保存流程日志
     */
    fun logSaveStep(step: String, templateName: String, detail: String = "") {
        val message = if (detail.isNotEmpty()) {
            "🔥 [WK-SAVE-$step] $templateName - $detail"
        } else {
            "🔥 [WK-SAVE-$step] $templateName"
        }
        Timber.tag(TAG_CORE).i(message)
    }

    /**
     * 🔥 保存错误跟踪
     */
    fun logSaveError(step: String, templateName: String, error: String) {
        Timber.tag(TAG_CORE).e("❌ [WK-SAVE-$step] $templateName - ERROR: $error")
    }

    /**
     * 统一记录模板信息，避免重复日志
     */
    fun logTemplateInfo(templateName: String, exerciseCount: Int, context: String) {
        Timber.tag(TAG_CORE).i("🔥 [$context] $templateName (${exerciseCount}动作)")
    }

    /**
     * 统一记录组数据，避免重复的详细日志
     * 🔥 优化：只在真正必要时记录详细信息
     */
    fun logExerciseDetails(
        exerciseName: String,
        setCount: Int,
        context: String,
        onlyOnError: Boolean = true,
    ) {
        // 只在错误情况下记录详细日志
        if (!onlyOnError || setCount == 0) {
            Timber.tag(TAG_EXERCISE).d("🔥 [$context] $exerciseName: ${setCount}组")
        }
    }

    /**
     * 统一JSON操作日志 - 仅记录失败情况
     */
    fun logJsonOperation(operation: String, size: Int, success: Boolean) {
        if (!success) {
            Timber.tag(TAG_DATA).e("❌ [$operation] JSON处理失败, 长度: $size")
        }
    }

    /**
     * 统一数据映射日志 - 仅记录大量数据或异常情况
     */
    fun logDataMapping(from: String, to: String, itemCount: Int) {
        // 只在数据量大或为0时记录
        if (itemCount > 10 || itemCount == 0) {
            Timber.tag(TAG_DATA).d("🔄 [$from→$to] 映射完成: $itemCount 项")
        }
    }

    /**
     * 🔥 快速日志方法 - 模板相关
     */
    object Template {
        fun info(message: String) = Timber.tag(TAG_TEMPLATE).i(message)
        fun debug(message: String) = Timber.tag(TAG_TEMPLATE).d(message)
        fun error(
            message: String,
            throwable: Throwable? = null,
        ) = Timber.tag(TAG_TEMPLATE).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_TEMPLATE).w(message)
    }

    /**
     * 🔥 快速日志方法 - 动作相关
     */
    object Exercise {
        fun info(message: String) = Timber.tag(TAG_EXERCISE).i(message)
        fun debug(message: String) = Timber.tag(TAG_EXERCISE).d(message)
        fun error(
            message: String,
            throwable: Throwable? = null,
        ) = Timber.tag(TAG_EXERCISE).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_EXERCISE).w(message)
    }

    /**
     * 🔥 快速日志方法 - JSON处理相关
     */
    object Json {
        fun info(message: String) = Timber.tag(TAG_JSON).i(message)
        fun debug(message: String) = Timber.tag(TAG_JSON).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_JSON).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_JSON).w(message)
    }

    /**
     * 🔥 快速日志方法 - 数据库相关
     */
    object Database {
        fun info(message: String) = Timber.tag(TAG_DATABASE).i(message)
        fun debug(message: String) = Timber.tag(TAG_DATABASE).d(message)
        fun error(
            message: String,
            throwable: Throwable? = null,
        ) = Timber.tag(TAG_DATABASE).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_DATABASE).w(message)
    }

    /**
     * 🔥 快速日志方法 - 关键数据流
     */
    object Critical {
        fun info(message: String) = Timber.tag(TAG_CRITICAL).i(message)
        fun debug(message: String) = Timber.tag(TAG_CRITICAL).d(message)
        fun error(
            message: String,
            throwable: Throwable? = null,
        ) = Timber.tag(TAG_CRITICAL).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_CRITICAL).w(message)
    }

    /**
     * 🔥 数据验证日志
     */
    object Validation {
        fun success(message: String) = Timber.tag(TAG_VALIDATION).i("✅ $message")
        fun failure(message: String) = Timber.tag(TAG_VALIDATION).e("❌ $message")
        fun warning(message: String) = Timber.tag(TAG_VALIDATION).w("⚠️ $message")
    }
}