package com.example.gymbro.features.thinkingbox

import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingReducer
import org.junit.Assert.*
import org.junit.Test

/**
 * V2方案 Final Typewriter Flow 功能测试
 *
 * 🎯 验证v2方案的核心功能：
 * - FinalToken事件的处理和finalTokens状态更新
 * - FinalEnd事件的处理和isFinalStreaming状态更新
 * - FinalRenderingReady事件的处理
 */
class V2TypewriterFlowTest {

    private val reducer = ThinkingReducer()

    @Test
    fun `FinalToken事件应该正确更新finalTokens和isFinalStreaming状态`() {
        // Given
        val initialState = reducer.createInitialState("test-session")
        val token1 = "Hello "
        val token2 = "World!"

        // When - 发送第一个FinalToken事件
        val state1 = reducer.reduce(initialState, ThinkingEvent.FinalToken(token1))

        // Then - 验证第一个token的状态更新
        assertEquals(listOf(token1), state1.finalTokens)
        assertTrue("isFinalStreaming应该为true", state1.isFinalStreaming)
        assertEquals(token1, state1.finalMarkdown)

        // When - 发送第二个FinalToken事件
        val state2 = reducer.reduce(state1, ThinkingEvent.FinalToken(token2))

        // Then - 验证token累积
        assertEquals(listOf(token1, token2), state2.finalTokens)
        assertTrue("isFinalStreaming应该保持为true", state2.isFinalStreaming)
        assertEquals(token1 + token2, state2.finalMarkdown)
    }

    @Test
    fun `FinalEnd事件应该停止流式传输`() {
        // Given
        val initialState = reducer.createInitialState("test-session")
        val tokenState = reducer.reduce(initialState, ThinkingEvent.FinalToken("Test content"))

        // When
        val finalState = reducer.reduce(tokenState, ThinkingEvent.FinalEnd)

        // Then
        assertFalse("isFinalStreaming应该为false", finalState.isFinalStreaming)
        assertEquals("finalTokens应该保持不变", listOf("Test content"), finalState.finalTokens)
    }

    @Test
    fun `FinalRenderingReady事件应该不改变状态`() {
        // Given
        val initialState = reducer.createInitialState("test-session")

        // When
        val resultState = reducer.reduce(initialState, ThinkingEvent.FinalRenderingReady)

        // Then - 状态应该保持不变（除了可能的内部时间戳等）
        assertEquals(initialState.finalTokens, resultState.finalTokens)
        assertEquals(initialState.isFinalStreaming, resultState.isFinalStreaming)
        assertEquals(initialState.finalMarkdown, resultState.finalMarkdown)
    }

    @Test
    fun `完整的Final Typewriter Flow序列测试`() {
        // Given
        val initialState = reducer.createInitialState("test-session")

        // When - 模拟完整的v2方案流程
        val step1 = reducer.reduce(initialState, ThinkingEvent.FinalToken("Token 1 "))
        val step2 = reducer.reduce(step1, ThinkingEvent.FinalToken("Token 2 "))
        val step3 = reducer.reduce(step2, ThinkingEvent.FinalToken("Token 3"))
        val step4 = reducer.reduce(step3, ThinkingEvent.FinalEnd)
        val step5 = reducer.reduce(step4, ThinkingEvent.FinalRenderingReady)

        // Then - 验证最终状态
        assertEquals(
            "finalTokens应该包含所有token",
            listOf("Token 1 ", "Token 2 ", "Token 3"),
            step5.finalTokens,
        )
        assertFalse("isFinalStreaming应该为false", step5.isFinalStreaming)
        assertEquals(
            "finalMarkdown应该是所有token的连接",
            "Token 1 Token 2 Token 3",
            step5.finalMarkdown,
        )
    }

    @Test
    fun `斜体token测试（以波浪号开头）`() {
        // Given
        val initialState = reducer.createInitialState("test-session")

        // When
        val state = reducer.reduce(initialState, ThinkingEvent.FinalToken("~这是斜体内容"))

        // Then
        assertEquals(listOf("~这是斜体内容"), state.finalTokens)
        assertEquals("~这是斜体内容", state.finalMarkdown)
        assertTrue(state.isFinalStreaming)
    }

    @Test
    fun `混合普通和斜体token测试`() {
        // Given
        val initialState = reducer.createInitialState("test-session")

        // When
        val step1 = reducer.reduce(initialState, ThinkingEvent.FinalToken("普通内容 "))
        val step2 = reducer.reduce(step1, ThinkingEvent.FinalToken("~斜体内容 "))
        val step3 = reducer.reduce(step2, ThinkingEvent.FinalToken("更多普通内容"))

        // Then
        assertEquals(
            listOf("普通内容 ", "~斜体内容 ", "更多普通内容"),
            step3.finalTokens,
        )
        assertEquals("普通内容 ~斜体内容 更多普通内容", step3.finalMarkdown)
    }

    @Test
    fun `空token处理测试`() {
        // Given
        val initialState = reducer.createInitialState("test-session")

        // When
        val state = reducer.reduce(initialState, ThinkingEvent.FinalToken(""))

        // Then
        assertEquals(listOf(""), state.finalTokens)
        assertEquals("", state.finalMarkdown)
        assertTrue(state.isFinalStreaming)
    }

    @Test
    fun `版本号递增测试`() {
        // Given
        val initialState = reducer.createInitialState("test-session")
        val initialVersion = initialState.version

        // When
        val state1 = reducer.reduce(initialState, ThinkingEvent.FinalToken("test"))
        val state2 = reducer.reduce(state1, ThinkingEvent.FinalEnd)

        // Then
        assertTrue("版本号应该递增", state1.version > initialVersion)
        assertTrue("版本号应该继续递增", state2.version > state1.version)
    }
}
