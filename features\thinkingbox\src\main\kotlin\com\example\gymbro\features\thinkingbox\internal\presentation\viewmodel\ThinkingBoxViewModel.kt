package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.network.router.TokenRouter
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.Segment
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.SegmentQueueReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ThinkingBoxViewModel - 基于Segment队列架构的ViewModel（729方案3.md）
 */
@HiltViewModel
class ThinkingBoxViewModel @Inject constructor(
    private val tokenRouter: TokenRouter,
    private val streamingParser: StreamingThinkingMLParser,
    private val domainMapper: DomainMapper,
    private val segmentQueueReducer: SegmentQueueReducer,
) : ViewModel() {

    private var internalState = SegmentQueueReducer.TBState()
    private var mappingContext = DomainMapper.MappingContext()
    private var parseJob: Job? = null

    private val _state = MutableStateFlow(ThinkingBoxContract.State())
    val state: StateFlow<ThinkingBoxContract.State> = _state.asStateFlow()

    private val _effect = MutableSharedFlow<ThinkingBoxContract.Effect>()
    val effect: SharedFlow<ThinkingBoxContract.Effect> = _effect.asSharedFlow()

    fun initialize(messageId: String) {
        if (internalState.messageId == messageId && parseJob?.isActive == true) {
            Timber.tag("TB-VIEWMODEL").d("🔄 [跳过初始化] messageId=$messageId 已初始化")
            return
        }

        Timber.tag("TB-VIEWMODEL").i("🚀 [初始化] messageId=$messageId")
        internalState = SegmentQueueReducer.TBState(messageId = messageId)
        mappingContext = DomainMapper.MappingContext()
        parseJob?.cancel()

        startTokenStreamListening(messageId)
        updateContractState()

        // 🔥 【调试】输出初始状态
        Timber.tag("TB-VIEWMODEL").d("📊 [初始状态] ${convertToContractState(internalState)}")
    }



    fun processIntent(intent: ThinkingBoxContract.Intent) {
        when (intent) {
            is ThinkingBoxContract.Intent.Initialize -> initialize(intent.messageId)
            is ThinkingBoxContract.Intent.UiSegmentRendered -> {
                processThinkingEvent(ThinkingEvent.UiSegmentRendered(intent.segmentId))
            }
            is ThinkingBoxContract.Intent.Reset -> reset()
            is ThinkingBoxContract.Intent.ClearError -> {
                updateContractState(_state.value.copy(error = null))
            }
        }
    }

    private fun startTokenStreamListening(messageId: String) {
        Timber.tag("TB-VIEWMODEL").i("🎯 [Token流启动] messageId=$messageId")

        parseJob = viewModelScope.launch {
            try {
                // 获取ConversationScope
                Timber.tag("TB-VIEWMODEL").d("🔍 [获取ConversationScope] messageId=$messageId")
                val conversationScope = tokenRouter.getOrCreateScope(messageId)
                Timber.tag("TB-VIEWMODEL").d("✅ [ConversationScope获取成功] scope=$conversationScope")

                // 启动解析器处理token流
                Timber.tag("TB-VIEWMODEL").d("🎯 [启动解析器] 开始监听token流")
                streamingParser.parseTokenStream(
                    messageId = messageId,
                    tokens = conversationScope.tokens,
                    onEvent = { semanticEvent ->
                        Timber.tag("TB-VIEWMODEL").d("🔍 [语义事件] ${semanticEvent::class.simpleName}")

                        // 映射为ThinkingEvent
                        val mappingResult = domainMapper.mapSemanticToThinking(semanticEvent, mappingContext)
                        mappingContext = mappingResult.context

                        // 处理映射结果
                        mappingResult.events.forEach { thinkingEvent ->
                            Timber.tag("TB-VIEWMODEL").d("🔄 [处理ThinkingEvent] ${thinkingEvent::class.simpleName}")
                            processThinkingEvent(thinkingEvent)
                        }
                    }
                )
            } catch (e: Exception) {
                Timber.tag("TB-VIEWMODEL").e(e, "❌ [Token流启动失败] messageId=$messageId")
            }
        }
    }

    private fun processThinkingEvent(event: ThinkingEvent) {
        val result = segmentQueueReducer.reduce(internalState, event)
        if (result.state !== internalState) {
            internalState = result.state
            updateContractState()
            handleEffects(event, result.state)
        }

        // 🔥 【History写入方案B】处理History写入Effects
        result.effects.forEach { effect ->
            handleHistoryEffect(effect)
        }
    }

    /**
     * 处理History写入Effects（History写入方案B）
     */
    private fun handleHistoryEffect(effect: ThinkingBoxContract.Effect) {
        when (effect) {
            is ThinkingBoxContract.Effect.NotifyHistoryThinking -> {
                Timber.tag("TB-HISTORY").d("🔥 [History写入] 思考历史: ${effect.messageId}")
                // 发送Effect给EffectHandler，由HistoryActor监听落库
                emitEffect(effect)
            }

            is ThinkingBoxContract.Effect.NotifyHistoryFinal -> {
                Timber.tag("TB-HISTORY").d("🔥 [History写入] 最终答案: ${effect.messageId}")
                // 发送Effect给EffectHandler，由HistoryActor监听落库
                emitEffect(effect)
            }

            else -> {
                // 其他Effect不在这里处理
            }
        }
    }

    private fun handleEffects(event: ThinkingEvent, state: SegmentQueueReducer.TBState) {
        when (event) {
            is ThinkingEvent.SegmentText, is ThinkingEvent.FinalContent -> {
                emitEffect(ThinkingBoxContract.Effect.ScrollToBottom)
            }
            is ThinkingEvent.ThinkingClosed -> {
                val thinkingMarkdown = generateThinkingMarkdown(state)
                emitEffect(ThinkingBoxContract.Effect.NotifyHistoryThinking(
                    messageId = state.messageId ?: "",
                    thinkingMarkdown = thinkingMarkdown
                ))
            }
            is ThinkingEvent.FinalComplete -> {
                emitEffect(ThinkingBoxContract.Effect.NotifyHistoryFinal(
                    messageId = state.messageId ?: "",
                    finalMarkdown = state.getFinalContent()
                ))
            }
            else -> {}
        }

        if (state.shouldCloseThinkingBox()) {
            emitEffect(ThinkingBoxContract.Effect.CloseThinkingBox)
        }
    }

    private fun updateContractState(contractState: ThinkingBoxContract.State? = null) {
        val newContractState = contractState ?: convertToContractState(internalState)
        _state.value = newContractState
    }

    private fun convertToContractState(tbState: SegmentQueueReducer.TBState): ThinkingBoxContract.State {
        return ThinkingBoxContract.State(
            messageId = tbState.messageId ?: "",
            segmentsQueue = tbState.queue.map { segment ->
                ThinkingBoxContract.SegmentUi(
                    id = segment.id,
                    kind = segment.kind.name,
                    title = segment.title,
                    content = segment.getTextContent(),
                    isComplete = segment.closed
                )
            },
            currentSegment = tbState.current?.let { segment ->
                ThinkingBoxContract.SegmentUi(
                    id = segment.id,
                    kind = segment.kind.name,
                    title = segment.title,
                    content = segment.getTextContent(),
                    isComplete = segment.closed
                )
            },
            finalReady = tbState.isFinalReadyToRender(),
            finalContent = tbState.getFinalContent(),
            streaming = tbState.streaming,
            thinkingClosed = tbState.thinkingClosed,
        )
    }

    private fun generateThinkingMarkdown(state: SegmentQueueReducer.TBState): String {
        val segments = mutableListOf<Segment>()
        state.queue.forEach { segment ->
            if (segment.closed && segment.kind != SegmentKind.FINAL) {
                segments.add(segment)
            }
        }
        state.current?.let { current ->
            if (current.closed && current.kind != SegmentKind.FINAL) {
                segments.add(current)
            }
        }

        return segments.joinToString("\n\n") { segment ->
            when (segment.kind) {
                SegmentKind.PERTHINK -> "## ${segment.title ?: "Pre-thinking"}\n${segment.getTextContent()}"
                SegmentKind.PHASE -> "## ${segment.title ?: "Phase ${segment.id}"}\n${segment.getTextContent()}"
                SegmentKind.FINAL -> ""
            }
        }.trim()
    }

    private fun emitEffect(effect: ThinkingBoxContract.Effect) {
        viewModelScope.launch { _effect.emit(effect) }
    }

    private fun reset() {
        parseJob?.cancel()
        internalState = SegmentQueueReducer.TBState()
        mappingContext = DomainMapper.MappingContext()
        updateContractState(ThinkingBoxContract.State())
    }

    override fun onCleared() {
        super.onCleared()
        parseJob?.cancel()
    }
}
