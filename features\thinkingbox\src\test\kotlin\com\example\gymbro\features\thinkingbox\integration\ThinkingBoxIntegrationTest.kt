package com.example.gymbro.features.thinkingbox.integration

import com.example.gymbro.features.thinkingbox.api.StartStream
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxApi
import com.example.gymbro.features.thinkingbox.api.OnMessageComplete
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import org.mockito.kotlin.*

/**
 * ThinkingBox 集成测试
 * 
 * 🔥 【P4修复】验证完整的跨模块协议和消息完成回调流程
 */
class ThinkingBoxIntegrationTest {

    /**
     * 测试完整的消息处理流程
     * 
     * 验证：StartStream → Token处理 → MessageComplete回调
     */
    @Test
    fun `test complete message processing flow`() = runTest {
        // 创建mock的ThinkingBoxApi
        val mockApi = mock<ThinkingBoxApi>()
        var capturedCallback: OnMessageComplete? = null
        
        // 捕获设置的回调
        whenever(mockApi.setOnMessageComplete(any())).thenAnswer { invocation ->
            capturedCallback = invocation.getArgument(0)
            Unit
        }
        
        // 设置回调
        var receivedMessageId: String? = null
        var receivedFinalMarkdown: String? = null
        
        mockApi.setOnMessageComplete { messageId, finalMarkdown ->
            receivedMessageId = messageId
            receivedFinalMarkdown = finalMarkdown
        }
        
        // 验证回调被设置
        assertNotNull(capturedCallback)
        
        // 模拟启动流
        val startRequest = StartStream(
            sessionId = "session-123",
            userMessageId = "user-msg-456",
            aiResponseId = "ai-response-789",
            prompt = "测试提示"
        )
        
        mockApi.startStream(startRequest)
        
        // 验证startStream被调用
        verify(mockApi).startStream(startRequest)
        
        // 模拟消息完成回调
        capturedCallback?.invoke("ai-response-789", "这是最终的Markdown内容")
        
        // 验证回调数据
        assertEquals("ai-response-789", receivedMessageId)
        assertEquals("这是最终的Markdown内容", receivedFinalMarkdown)
        
        println("✅ 集成测试通过：完整的消息处理流程正确工作")
    }

    /**
     * 测试跨模块协议的数据传递
     */
    @Test
    fun `test cross module protocol data transfer`() = runTest {
        val startRequest = StartStream(
            sessionId = "test-session",
            userMessageId = "test-user-msg",
            aiResponseId = "test-ai-response",
            prompt = "测试跨模块协议"
        )
        
        // 验证数据结构完整性
        assertEquals("test-session", startRequest.sessionId)
        assertEquals("test-user-msg", startRequest.userMessageId)
        assertEquals("test-ai-response", startRequest.aiResponseId)
        assertEquals("测试跨模块协议", startRequest.prompt)
        
        println("✅ 跨模块协议数据传递测试通过")
    }

    /**
     * 测试错误处理场景
     */
    @Test
    fun `test error handling scenarios`() = runTest {
        val mockApi = mock<ThinkingBoxApi>()
        
        // 测试空消息ID的处理
        var errorOccurred = false
        mockApi.setOnMessageComplete { messageId, finalMarkdown ->
            if (messageId.isBlank()) {
                errorOccurred = true
            }
        }
        
        // 模拟设置回调
        var capturedCallback: OnMessageComplete? = null
        whenever(mockApi.setOnMessageComplete(any())).thenAnswer { invocation ->
            capturedCallback = invocation.getArgument(0)
            Unit
        }
        
        mockApi.setOnMessageComplete { messageId, finalMarkdown ->
            if (messageId.isBlank()) {
                errorOccurred = true
            }
        }
        
        // 验证错误处理机制存在
        assertNotNull(capturedCallback)
        
        println("✅ 错误处理场景测试通过")
    }

    /**
     * 测试重置功能
     */
    @Test
    fun `test reset functionality`() = runTest {
        val mockApi = mock<ThinkingBoxApi>()
        
        // 调用重置
        mockApi.reset()
        
        // 验证重置被调用
        verify(mockApi).reset()
        
        println("✅ 重置功能测试通过")
    }

    /**
     * 测试多次消息处理
     */
    @Test
    fun `test multiple message processing`() = runTest {
        val mockApi = mock<ThinkingBoxApi>()
        val receivedMessages = mutableListOf<Pair<String, String>>()
        
        // 设置回调收集所有消息
        var capturedCallback: OnMessageComplete? = null
        whenever(mockApi.setOnMessageComplete(any())).thenAnswer { invocation ->
            capturedCallback = invocation.getArgument(0)
            Unit
        }
        
        mockApi.setOnMessageComplete { messageId, finalMarkdown ->
            receivedMessages.add(messageId to finalMarkdown)
        }
        
        // 模拟多次消息完成
        capturedCallback?.invoke("msg-1", "内容1")
        capturedCallback?.invoke("msg-2", "内容2")
        capturedCallback?.invoke("msg-3", "内容3")
        
        // 验证所有消息都被正确处理
        assertEquals(3, receivedMessages.size)
        assertEquals("msg-1" to "内容1", receivedMessages[0])
        assertEquals("msg-2" to "内容2", receivedMessages[1])
        assertEquals("msg-3" to "内容3", receivedMessages[2])
        
        println("✅ 多次消息处理测试通过")
    }
}
