package com.example.gymbro.features.workout.template.edit.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import timber.log.Timber
import javax.inject.Inject

/**
 * 动作管理处理器
 *
 * 🎯 职责：
 * - 处理动作的增删改查操作
 * - 管理动作数据的状态转换
 * - 确保数据一致性
 *
 * 📋 遵循标准：
 * - 单一职责原则
 * - 纯函数式编程
 * - 不可变状态管理
 */
class ExerciseManagementHandlers @Inject constructor() {

    // === 动作添加 ===

    fun handleAddExercise(
        intent: TemplateEditContract.Intent.AddExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        Timber.d("🔧 [AddExercise] 开始添加动作: ${intent.exercise.name}")
        Timber.d("🔧 [AddExercise] 当前动作数量: ${state.exercises.size}")
        Timber.d("🔧 [AddExercise] 最大限制: ${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE}")

        // 检查动作数量限制
        if (state.exercises.size >= TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE) {
            Timber.w(
                "🔧 [AddExercise] 达到动作数量限制: ${state.exercises.size}/${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE}",
            )
            return ReduceResult.withEffect(
                state,
                TemplateEditContract.Effect.ShowToast(
                    UiText.DynamicString(
                        "最多只能添加 ${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE} 个动作",
                    ),
                ),
            )
        }

        val newExercise = mapExerciseToDto(intent.exercise)
        val updatedExercises = state.exercises + newExercise

        // 调试日志
        Timber.d("🔧 [AddExercise] 新动作ID: ${newExercise.id}")
        Timber.d("🔧 [AddExercise] 新动作 customSets: ${newExercise.customSets.size}")
        newExercise.customSets.forEachIndexed { index, set ->
            Timber.d(
                "🔧 [AddExercise] 新动作组${index + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s",
            )
        }

        return ReduceResult.stateOnly(
            state.copy(
                exercises = updatedExercises,
                hasUnsavedChanges = true,
                // 🔥 修复：不再触发自动保存，避免转圈圈问题
            ),
        )
    }

    fun handleAddExercises(
        intent: TemplateEditContract.Intent.AddExercises,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val newExercises = intent.exercises.map { exercise ->
            mapExerciseToDto(exercise)
        }

        // 检查批量添加后的动作数量限制
        val totalExercisesAfterAdd = state.exercises.size + newExercises.size
        if (totalExercisesAfterAdd > TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE) {
            val maxCanAdd = TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE - state.exercises.size
            return ReduceResult.withEffect(
                state,
                TemplateEditContract.Effect.ShowToast(
                    UiText.DynamicString(
                        "最多只能再添加 $maxCanAdd 个动作（当前 ${state.exercises.size}/${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE}）",
                    ),
                ),
            )
        }

        // 调试日志
        Timber.d("🔧 [AddExercises] 批量添加 ${newExercises.size} 个动作")
        newExercises.forEachIndexed { index, exercise ->
            Timber.d(
                "🔧 [AddExercises] 动作${index + 1}: ${exercise.exerciseName}, customSets=${exercise.customSets.size}",
            )
        }

        return ReduceResult.stateOnly(
            state.copy(
                exercises = state.exercises + newExercises,
                hasUnsavedChanges = true,
                // 🔥 修复：不再触发自动保存，避免转圈圈问题
            ),
        )
    }

    // === 动作更新 ===

    fun handleUpdateExercise(
        intent: TemplateEditContract.Intent.UpdateExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 🔥 数据一致性验证：确保更新操作的原子性
        Timber.d("🔥 [UPDATE-EXERCISE] 开始处理动作更新: ${intent.exercise.exerciseName}")
        Timber.d("🔥 [UPDATE-EXERCISE] 目标组数: ${intent.exercise.customSets.size}")

        // 🔥 详细数据验证日志，确保数据完整性
        WorkoutLogUtils.Exercise.debug(
            "🔥 [UPDATE-EXERCISE] 数据验证 - 动作=${intent.exercise.exerciseName}, customSets=${intent.exercise.customSets.size}",
        )

        // 🔥 逐组验证数据完整性，避免丢失用户输入
        intent.exercise.customSets.forEachIndexed { index, set ->
            WorkoutLogUtils.Exercise.debug(
                "🔥 [UPDATE-EXERCISE] 组${index + 1}数据: weight=${set.targetWeight}kg, reps=${set.targetReps}, rest=${set.restTimeSeconds}s",
            )
        }

        // 🔥 原子性状态更新：确保整个更新操作的一致性
        val updatedExercises = state.exercises.map { exercise ->
            if (exercise.id == intent.exercise.id) {
                // 🔥 核心修复：直接使用更新后的数据，避免中间状态处理导致的数据丢失
                val updatedExercise = intent.exercise
                
                // 🔥 数据完整性二次验证
                if (updatedExercise.customSets.isEmpty()) {
                    Timber.w("🔥 [UPDATE-EXERCISE] ⚠️ 警告: 更新后的动作没有组数据，这可能不是预期行为")
                }
                
                updatedExercise
            } else {
                exercise
            }
        }

        // 🔥 状态一致性验证：确保更新后的状态符合预期
        val updatedState = state.copy(
            exercises = updatedExercises,
            hasUnsavedChanges = true,
            autoSaveState = TemplateContract.AutoSaveState.Inactive, // 避免立即触发自动保存，防止状态竞争
        )

        // 🔥 最终验证：确认状态更新成功
        val targetExercise = updatedExercises.find { it.id == intent.exercise.id }
        if (targetExercise != null) {
            Timber.d("🔥 [UPDATE-EXERCISE] 状态更新成功: ${targetExercise.exerciseName}, 组数=${targetExercise.customSets.size}")
        } else {
            Timber.e("🔥 [UPDATE-EXERCISE] ❌ 严重错误: 无法找到更新后的动作数据")
        }

        return ReduceResult.stateOnly(
            updatedState,
            // 🔥 修复：不触发自动保存，避免状态更新竞争导致的数据丢失
        )
    }

    // === 动作删除 ===

    fun handleRemoveExercise(
        intent: TemplateEditContract.Intent.RemoveExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                exercises = state.exercises.filterNot { it.id == intent.exerciseId },
                hasUnsavedChanges = true,
                // 🔥 修复：不再触发自动保存，避免转圈圈问题
            ),
        )
    }

    // === 快速操作 ===

    fun handleQuickDuplicateExercise(
        intent: TemplateEditContract.Intent.QuickDuplicateExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val exerciseToDuplicate = state.exercises.find { it.id == intent.exerciseId }
            ?: return ReduceResult.noChange(state)

        val duplicatedExercise = exerciseToDuplicate.copy(
            id = "temp_${System.currentTimeMillis()}_${exerciseToDuplicate.id}",
            exerciseName = "${exerciseToDuplicate.exerciseName} (副本)",
        )

        val updatedExercises = state.exercises.toMutableList().apply {
            val originalIndex = indexOfFirst { it.id == intent.exerciseId }
            add(originalIndex + 1, duplicatedExercise)
        }

        return ReduceResult.stateOnly(
            state.copy(
                exercises = updatedExercises,
                hasUnsavedChanges = true,
                showQuickActions = false,
                quickActionTargetId = null,
            ),
        )
    }

    fun handleQuickDeleteExercise(
        intent: TemplateEditContract.Intent.QuickDeleteExercise,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val updatedExercises = state.exercises.filter { it.id != intent.exerciseId }

        return ReduceResult.stateOnly(
            state.copy(
                exercises = updatedExercises,
                hasUnsavedChanges = true,
                showQuickActions = false,
                quickActionTargetId = null,
            ),
        )
    }

    // === 工具函数 ===

    /**
     * 将Exercise转换为TemplateExerciseDto
     */
    private fun mapExerciseToDto(
        exercise: com.example.gymbro.domain.exercise.model.Exercise,
    ): com.example.gymbro.shared.models.workout.TemplateExerciseDto {
        // 🔥 优化：只在添加动作失败或需要调试时记录
        if (extractTextSafely(exercise.name).isBlank()) {
            Timber.tag("WK-EXERCISE").w("⚠️ [ADD-SOURCE] 动作名称为空: ${exercise.id}")
        }

        // 为新添加的动作生成默认的 customSets
        val defaultCustomSets = (1..3).map { setNumber ->
            com.example.gymbro.shared.models.workout.TemplateSetDto(
                setNumber = setNumber,
                targetWeight = 0f,
                targetReps = 10,
                restTimeSeconds = 60,
                targetDuration = null,
                rpe = null,
            )
        }

        val resultDto = com.example.gymbro.shared.models.workout.TemplateExerciseDto(
            id = com.example.gymbro.shared.models.workout.WorkoutTemplateDto.generateId(),
            exerciseId = exercise.id,
            exerciseName = extractTextSafely(exercise.name),
            // 🔥 关键修复：为动作库数据提供默认值，避免无限加载
            imageUrl = exercise.imageUrl ?: "https://example.com/default_exercise.jpg",
            videoUrl = exercise.videoUrl ?: "https://example.com/default_exercise.mp4",
            sets = 3,
            reps = 10,
            rpe = null,
            targetWeight = 0f,
            restTimeSeconds = 60,
            notes = null,
            customSets = defaultCustomSets,
        )

        // 🔥 优化：只在转换失败或数据缺失时记录
        if (resultDto.exerciseName.isBlank() || (resultDto.imageUrl == null && resultDto.videoUrl == null)) {
            Timber.tag("WK-EXERCISE").w(
                "⚠️ [ADD-RESULT] 转换完成但数据可能缺失: ${resultDto.exerciseName}, imageUrl=${resultDto.imageUrl}, videoUrl=${resultDto.videoUrl}",
            )
        }

        return resultDto
    }

    /**
     * 安全提取UiText中的文本内容
     */
    private fun extractTextSafely(uiText: com.example.gymbro.core.ui.text.UiText): String {
        return when (uiText) {
            is com.example.gymbro.core.ui.text.UiText.DynamicString -> uiText.value
            is com.example.gymbro.core.ui.text.UiText.StringResource -> "Exercise_${uiText.resId}"
            is com.example.gymbro.core.ui.text.UiText.ErrorCode -> uiText.errorCode.code
            is com.example.gymbro.core.ui.text.UiText.Empty -> "未命名动作"
        }
    }
}
