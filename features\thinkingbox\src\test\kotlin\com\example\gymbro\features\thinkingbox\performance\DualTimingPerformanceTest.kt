package com.example.gymbro.features.thinkingbox.performance

import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxContractReducer
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import kotlin.system.measureTimeMillis

/**
 * 双时序握手性能测试
 * 
 * 🔥 【P4修复】验证双时序握手机制的性能表现
 * 确保状态更新在16ms内完成，满足60fps要求
 */
class DualTimingPerformanceTest {

    private val contractReducer = ThinkingBoxContractReducer()
    private val initialState = ThinkingBoxContract.State()

    /**
     * 测试Phase切换的双时序握手性能
     * 
     * 期望：单次状态更新 ≤ 16ms（60fps要求）
     */
    @Test
    fun `test phase switching dual timing handshake performance`() = runTest {
        val messageId = "perf-test-msg"
        var currentState = initialState.copy(messageId = messageId)
        
        // 预热
        repeat(10) {
            val event = ThinkingEvent.PhaseStart("warmup-$it", "预热阶段")
            val intent = ThinkingBoxContract.Intent.HandleThinkingEvent(event)
            contractReducer.reduce(intent, currentState)
        }
        
        // 测试Phase数据完成的性能
        val phaseEndTime = measureTimeMillis {
            val event = ThinkingEvent.PhaseEnd("test-phase")
            val intent = ThinkingBoxContract.Intent.HandleThinkingEvent(event)
            val result = contractReducer.reduce(intent, currentState)
            currentState = result.state
        }
        
        // 测试Phase动画完成的性能
        val phaseAnimTime = measureTimeMillis {
            val intent = ThinkingBoxContract.Intent.PhaseAnimationFinished("test-phase")
            val result = contractReducer.reduce(intent, currentState)
            currentState = result.state
        }
        
        // 验证性能要求
        assertTrue("Phase数据完成处理时间($phaseEndTime ms)应该 ≤ 16ms", phaseEndTime <= 16)
        assertTrue("Phase动画完成处理时间($phaseAnimTime ms)应该 ≤ 16ms", phaseAnimTime <= 16)
        
        println("✅ Phase切换双时序握手性能测试通过")
        println("   - Phase数据完成: ${phaseEndTime}ms")
        println("   - Phase动画完成: ${phaseAnimTime}ms")
    }

    /**
     * 测试Final渲染的双时序握手性能
     */
    @Test
    fun `test final rendering dual timing handshake performance`() = runTest {
        val messageId = "final-perf-test"
        var currentState = initialState.copy(messageId = messageId)
        
        // 测试Final数据完成的性能
        val finalEndTime = measureTimeMillis {
            val event = ThinkingEvent.FinalEnd
            val intent = ThinkingBoxContract.Intent.HandleThinkingEvent(event)
            val result = contractReducer.reduce(intent, currentState)
            currentState = result.state
        }
        
        // 测试思考框关闭的性能
        val thinkingClosedTime = measureTimeMillis {
            val intent = ThinkingBoxContract.Intent.ThinkingBoxClosed
            val result = contractReducer.reduce(intent, currentState)
            currentState = result.state
        }
        
        // 测试Final渲染完成的性能
        val finalRenderingTime = measureTimeMillis {
            val intent = ThinkingBoxContract.Intent.FinalRenderingComplete
            val result = contractReducer.reduce(intent, currentState)
            currentState = result.state
        }
        
        // 验证性能要求
        assertTrue("Final数据完成处理时间($finalEndTime ms)应该 ≤ 16ms", finalEndTime <= 16)
        assertTrue("思考框关闭处理时间($thinkingClosedTime ms)应该 ≤ 16ms", thinkingClosedTime <= 16)
        assertTrue("Final渲染完成处理时间($finalRenderingTime ms)应该 ≤ 16ms", finalRenderingTime <= 16)
        
        println("✅ Final渲染双时序握手性能测试通过")
        println("   - Final数据完成: ${finalEndTime}ms")
        println("   - 思考框关闭: ${thinkingClosedTime}ms")
        println("   - Final渲染完成: ${finalRenderingTime}ms")
    }

    /**
     * 测试批量事件处理性能
     */
    @Test
    fun `test batch event processing performance`() = runTest {
        val messageId = "batch-perf-test"
        var currentState = initialState.copy(messageId = messageId)
        
        // 创建100个事件
        val events = (1..100).map { i ->
            when (i % 4) {
                0 -> ThinkingEvent.PhaseStart("phase-$i", "阶段$i")
                1 -> ThinkingEvent.PhaseContent("phase-$i", "内容$i")
                2 -> ThinkingEvent.PhaseEnd("phase-$i")
                else -> ThinkingEvent.FinalToken("token-$i")
            }
        }
        
        // 测试批量处理性能
        val batchTime = measureTimeMillis {
            events.forEach { event ->
                val intent = ThinkingBoxContract.Intent.HandleThinkingEvent(event)
                val result = contractReducer.reduce(intent, currentState)
                currentState = result.state
            }
        }
        
        val averageTime = batchTime.toDouble() / events.size
        
        // 验证平均处理时间
        assertTrue("平均事件处理时间($averageTime ms)应该 ≤ 1ms", averageTime <= 1.0)
        
        println("✅ 批量事件处理性能测试通过")
        println("   - 总处理时间: ${batchTime}ms")
        println("   - 平均处理时间: ${"%.2f".format(averageTime)}ms/事件")
        println("   - 处理事件数量: ${events.size}")
    }

    /**
     * 测试内存使用效率
     */
    @Test
    fun `test memory usage efficiency`() = runTest {
        val messageId = "memory-test"
        var currentState = initialState.copy(messageId = messageId)
        
        // 记录初始内存
        System.gc()
        val initialMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()
        
        // 处理大量事件
        repeat(1000) { i ->
            val event = ThinkingEvent.PhaseContent("phase-${i % 10}", "内容$i")
            val intent = ThinkingBoxContract.Intent.HandleThinkingEvent(event)
            val result = contractReducer.reduce(intent, currentState)
            currentState = result.state
        }
        
        // 记录处理后内存
        System.gc()
        val finalMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()
        
        val memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024 // MB
        
        // 验证内存使用合理（应该 < 50MB）
        assertTrue("内存增长($memoryIncrease MB)应该 < 50MB", memoryIncrease < 50)
        
        println("✅ 内存使用效率测试通过")
        println("   - 初始内存: ${initialMemory / 1024 / 1024}MB")
        println("   - 最终内存: ${finalMemory / 1024 / 1024}MB")
        println("   - 内存增长: ${memoryIncrease}MB")
    }

    /**
     * 测试状态一致性
     */
    @Test
    fun `test state consistency under concurrent operations`() = runTest {
        val messageId = "consistency-test"
        var currentState = initialState.copy(messageId = messageId)
        
        // 模拟并发操作序列
        val operations = listOf(
            ThinkingEvent.PhaseStart("1", "阶段1"),
            ThinkingEvent.PhaseContent("1", "内容1"),
            ThinkingEvent.PhaseEnd("1"),
            ThinkingEvent.PhaseStart("2", "阶段2"),
            ThinkingEvent.PhaseContent("2", "内容2"),
            ThinkingEvent.PhaseEnd("2"),
            ThinkingEvent.ThinkingEnd,
            ThinkingEvent.FinalStart,
            ThinkingEvent.FinalToken("最终内容"),
            ThinkingEvent.FinalEnd
        )
        
        // 执行操作序列
        operations.forEach { event ->
            val intent = ThinkingBoxContract.Intent.HandleThinkingEvent(event)
            val result = contractReducer.reduce(intent, currentState)
            currentState = result.state
        }
        
        // 验证最终状态一致性
        assertTrue("应该有2个phase", currentState.phases.size == 2)
        assertTrue("phase 1应该数据完成", currentState.phaseDataCompleted.contains("1"))
        assertTrue("phase 2应该数据完成", currentState.phaseDataCompleted.contains("2"))
        assertTrue("final数据应该完成", currentState.finalDataCompleted)
        assertTrue("思考应该完成", currentState.isThinkingComplete)
        
        println("✅ 状态一致性测试通过")
        println("   - Phases数量: ${currentState.phases.size}")
        println("   - 数据完成的Phases: ${currentState.phaseDataCompleted}")
        println("   - Final数据完成: ${currentState.finalDataCompleted}")
        println("   - 思考完成: ${currentState.isThinkingComplete}")
    }
}
