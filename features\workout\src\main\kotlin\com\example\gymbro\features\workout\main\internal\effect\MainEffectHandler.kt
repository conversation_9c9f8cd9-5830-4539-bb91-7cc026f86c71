package com.example.gymbro.features.workout.main.internal.effect

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.profile.usecase.GetUserProfileUseCase
import com.example.gymbro.domain.shared.common.model.TimeRangeSpan
import com.example.gymbro.domain.workout.model.calendar.TodayWorkoutInfo
import com.example.gymbro.domain.workout.usecase.calendar.GetCalendarDataUseCase
import com.example.gymbro.domain.workout.usecase.session.ManageWorkoutSessionUseCase
import com.example.gymbro.domain.workout.usecase.stats.GetWorkoutStatsUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.features.workout.calendar.internal.CalendarContract
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.main.HomeContract
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.datetime.LocalDate
import javax.inject.Inject

/**
 * Home模块副作用处理器 - 标准MVI副作用处理器
 *
 * 设计原则：
 * 1. 处理Intent产生的副作用
 * 2. 调用UseCase执行业务逻辑
 * 3. 将结果转换为新的Intent发送给Reducer
 * 4. 管理协程生命周期
 * 5. 提供良好的错误处理和日志记录
 */
internal class HomeEffectHandler
@Inject
constructor(
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val getWorkoutStatsUseCase: GetWorkoutStatsUseCase,
    private val getCalendarDataUseCase: GetCalendarDataUseCase,
    private val templateManagementUseCase: TemplateManagementUseCase,
    private val manageWorkoutSessionUseCase: ManageWorkoutSessionUseCase,
    private val logger: Logger,
) {

    // 🔥 新增：Effect 处理方法 - 符合 MVI 2.0 标准
    private lateinit var handlerScope: CoroutineScope
    private lateinit var intentSender: (HomeContract.Intent) -> Unit

    /**
     * 初始化 EffectHandler - 符合 MVI 2.0 标准
     */
    fun initialize(scope: CoroutineScope, intentSender: (HomeContract.Intent) -> Unit) {
        this.handlerScope = scope
        this.intentSender = intentSender
        logger.d("HomeEffectHandler", "EffectHandler 初始化完成")
    }

    /**
     * ✅ 修正：这是 EffectHandler 的唯一入口，处理来自 ViewModel 的 Effect。
     */
    fun handle(effect: HomeContract.Effect) {
        logger.d("HomeEffectHandler", "处理 Effect: ${effect::class.simpleName}")
        WorkoutLogUtils.Template.debug("🔥 收到 Effect: ${effect::class.simpleName}")

        // ✅ 修正：所有业务逻辑由 Effect 触发
        when (effect) {
            is HomeContract.Effect.LoadInitialDataEffect -> {
                WorkoutLogUtils.Template.debug("📊 处理初始数据加载 Effect")
                loadUserProfile(handlerScope, intentSender)
                loadWorkoutStats(handlerScope, intentSender)
                loadCalendarData(effect.date, handlerScope, intentSender)
                loadWorkoutTemplates(handlerScope, intentSender)
                loadTodayWorkout(effect.date, handlerScope, intentSender)
                loadRecentSessions(handlerScope, intentSender)
            }
            // 🔥 关键修复：添加对 LoadSpecificDateDataEffect 的处理
            is HomeContract.Effect.LoadSpecificDateDataEffect -> {
                WorkoutLogUtils.Template.debug("📅 处理特定日期数据加载 Effect，日期: ${effect.date}")
                loadCalendarData(effect.date, handlerScope, intentSender)
                loadTodayWorkout(effect.date, handlerScope, intentSender)
            }
            is HomeContract.Effect.StartQuickWorkoutEffect -> {
                WorkoutLogUtils.Template.debug("🏃 处理快速训练 Effect")
                handleStartQuickWorkout(handlerScope, intentSender)
            }
            is HomeContract.Effect.StartWithTemplateEffect -> {
                WorkoutLogUtils.Template.debug("📋 处理模板训练 Effect，模板ID: ${effect.templateId}")
                handleStartWithTemplate(effect.templateId, handlerScope, intentSender)
            }

            // 导航和其他UI Effect由ViewModel直接传递给UI层，这里只记录日志
            is HomeContract.Effect.NavigateToActiveWorkout,
            is HomeContract.Effect.NavigateToTemplates,
            is HomeContract.Effect.NavigateToStats,
            is HomeContract.Effect.NavigateToPlans,
            is HomeContract.Effect.NavigateToExerciseLibrary,
            is HomeContract.Effect.NavigateToCalendar,
            is HomeContract.Effect.NavigateToSubscription,
            is HomeContract.Effect.ShowSnackbar,
            is HomeContract.Effect.HapticFeedback,
            is HomeContract.Effect.ShowTemplateDialog,
            is HomeContract.Effect.ShowSaveTemplateDialog,
            -> {
                logger.d("HomeEffectHandler", "UI Effect '${effect::class.simpleName}' 将由UI层处理。")
                WorkoutLogUtils.Template.debug(
                    "🎯 UI Effect '${effect::class.simpleName}' 已确认，将由UI层处理",
                )
            }
        }
    }

    // ❌ 移除旧的 handle(intent: ...) 方法，因为它破坏了 MVI 流程
    // 所有业务逻辑现在通过 handle(effect: ...) 方法处理

    // === 具体副作用处理方法 ===

    private fun loadUserProfile(
        scope: CoroutineScope,
        sendIntent: (HomeContract.Intent) -> Unit,
    ) {
        logger.d("HomeEffectHandler", "加载用户资料")

        scope.launch {
            try {
                val result = getUserProfileUseCase()
                sendIntent(HomeContract.Intent.OnUserLoaded(result))
            } catch (e: Exception) {
                logger.e(e, "用户资料加载异常")
                sendIntent(
                    HomeContract.Intent.OnUserLoaded(
                        ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "HomeEffectHandler.loadUserProfile",
                                message = UiText.DynamicString("用户资料加载异常"),
                                entityType = "UserProfile",
                                cause = e,
                            ),
                        ),
                    ),
                )
            }
        }
    }

    private fun loadWorkoutStats(
        scope: CoroutineScope,
        sendIntent: (HomeContract.Intent) -> Unit,
    ) {
        logger.d("HomeEffectHandler", "加载训练统计")

        scope.launch {
            try {
                // 使用正确的参数类型 - TimeRangeSpan
                val timeRange = TimeRangeSpan.lastDays(30) // 获取最近30天的统计
                val result = getWorkoutStatsUseCase(timeRange)
                sendIntent(HomeContract.Intent.OnStatsLoaded(result))
            } catch (e: Exception) {
                logger.e(e, "训练统计加载异常")
                sendIntent(
                    HomeContract.Intent.OnStatsLoaded(
                        ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "HomeEffectHandler.loadWorkoutStats",
                                message = UiText.DynamicString("训练统计加载异常"),
                                entityType = "WorkoutStats",
                                cause = e,
                            ),
                        ),
                    ),
                )
            }
        }
    }

    private fun loadCalendarData(
        date: LocalDate,
        scope: CoroutineScope,
        sendIntent: (HomeContract.Intent) -> Unit,
    ) {
        logger.d("HomeEffectHandler", "加载增强日历数据: $date")

        scope.launch {
            try {
                // TODO: 实现增强日历数据加载
                // 暂时返回空数据以修复编译错误
                val calendarEntries = emptyMap<LocalDate, CalendarContract.CalendarEntry>()
                val totalCompletedWeight = 0.0
                val completedTrainingDays = 0
                val totalTrainingDays = 0

                sendIntent(
                    HomeContract.Intent.OnEnhancedCalendarLoaded(
                        calendarEntries = calendarEntries,
                        totalCompletedWeight = totalCompletedWeight,
                        completedTrainingDays = completedTrainingDays,
                        totalTrainingDays = totalTrainingDays,
                    ),
                )
            } catch (e: Exception) {
                logger.e(e, "增强日历数据加载异常")
                // 发送空数据而不是错误，保持UI稳定
                sendIntent(
                    HomeContract.Intent.OnEnhancedCalendarLoaded(
                        calendarEntries = emptyMap(),
                        totalCompletedWeight = 0.0,
                        completedTrainingDays = 0,
                        totalTrainingDays = 0,
                    ),
                )
            }
        }
    }

    private fun loadWorkoutTemplates(
        scope: CoroutineScope,
        sendIntent: (HomeContract.Intent) -> Unit,
    ) {
        logger.d("HomeEffectHandler", "加载训练模板")

        templateManagementUseCase
            .GetTemplates()
            .invoke(Unit)
            .onEach { result ->
                when (result) {
                    is ModernResult.Success -> {
                        val dtoList: List<com.example.gymbro.shared.models.workout.WorkoutTemplateDto> = result.data
                        logger.d("HomeEffectHandler", "加载到 ${dtoList.size} 个模板")

                        // 手动转换WorkoutTemplateDto到Domain模型
                        val convertedTemplates =
                            dtoList
                                .map { dto ->
                                    com.example.gymbro.domain.workout.model.template.WorkoutTemplate(
                                        id = dto.id,
                                        name = dto.name,
                                        userId = "current_user", // TODO: 从当前用户获取
                                        description = dto.description.takeIf { it.isNotBlank() },
                                        exercises = emptyList(), // TODO: 练习转换将在后续处理
                                        targetMuscleGroups = emptyList(), // TODO: 从metadata获取
                                        difficulty =
                                        when (dto.difficulty) {
                                            com.example.gymbro.shared.models.workout.Difficulty.EASY -> 1
                                            com.example.gymbro.shared.models.workout.Difficulty.MEDIUM -> 2
                                            com.example.gymbro.shared.models.workout.Difficulty.HARD -> 3
                                            com.example.gymbro.shared.models.workout.Difficulty.EXPERT -> 4
                                            else -> 1
                                        },
                                        estimatedDuration = 60, // TODO: 从metadata获取
                                        createdAt = dto.createdAt,
                                        updatedAt = dto.updatedAt,
                                        isPublic = false,
                                        isFavorite = false,
                                        usageCount = 0,
                                        tags = emptyList(),
                                    )
                                }.toImmutableList()

                        sendIntent(
                            HomeContract.Intent.OnTemplatesLoaded(
                                ModernResult.Success(convertedTemplates),
                            ),
                        )
                    }
                    is ModernResult.Error -> {
                        logger.e("HomeEffectHandler", "训练模板加载失败: ${result.error}")
                        sendIntent(HomeContract.Intent.OnTemplatesLoaded(result))
                    }
                    is ModernResult.Loading -> {
                        // Loading状态可以在UI中显示加载指示器
                    }
                }
            }.catch { exception ->
                logger.e("HomeEffectHandler", "训练模板加载异常", exception)
                sendIntent(
                    HomeContract.Intent.OnTemplatesLoaded(
                        ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "HomeEffectHandler.loadWorkoutTemplates",
                                message = UiText.DynamicString("训练模板加载异常"),
                                entityType = "WorkoutTemplate",
                                cause = exception,
                            ),
                        ),
                    ),
                )
            }.launchIn(scope)
    }

    private fun loadTodayWorkout(
        date: LocalDate,
        scope: CoroutineScope,
        sendIntent: (HomeContract.Intent) -> Unit,
    ) {
        logger.d("HomeEffectHandler", "加载今日训练: $date")

        scope.launch {
            try {
                // 加载今日训练数据
                val params = GetCalendarDataUseCase.Params(date, date)
                val calendarResult =
                    getCalendarDataUseCase(params).firstOrNull()
                        ?: ModernResult.Error(
                            DataErrors.DataError.notFound(
                                operationName = "HomeEffectHandler.loadTodayWorkout",
                                message = UiText.DynamicString("今日训练数据为空"),
                                entityType = "TodayWorkout",
                            ),
                        )

                // 转换为TodayWorkoutInfo
                val todayWorkoutResult =
                    when (calendarResult) {
                        is ModernResult.Success -> {
                            val dayInfo = calendarResult.data[date]
                            val todayWorkout =
                                dayInfo?.let { info ->
                                    // 从CalendarDayInfo创建TodayWorkoutInfo
                                    // 注意：这里需要根据实际的CalendarDayInfo结构来调整
                                    TodayWorkoutInfo(
                                        id = "today-$date",
                                        name = "今日训练",
                                        exerciseCount = 0, // 从dayInfo获取
                                        estimatedDuration = 60, // 从dayInfo获取
                                        muscleGroups = emptyList(), // 从dayInfo获取
                                    )
                                }
                            ModernResult.Success(todayWorkout)
                        }
                        is ModernResult.Error -> ModernResult.Error(calendarResult.error)
                        is ModernResult.Loading -> ModernResult.Loading
                    }

                sendIntent(HomeContract.Intent.OnTodayWorkoutLoaded(todayWorkoutResult))
            } catch (e: Exception) {
                logger.e(e, "今日训练数据加载异常")
                sendIntent(
                    HomeContract.Intent.OnTodayWorkoutLoaded(
                        ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "HomeEffectHandler.loadTodayWorkout",
                                message = UiText.DynamicString("今日训练数据加载异常"),
                                entityType = "TodayWorkout",
                                cause = e,
                            ),
                        ),
                    ),
                )
            }
        }
    }

    private fun loadRecentSessions(
        scope: CoroutineScope,
        sendIntent: (HomeContract.Intent) -> Unit,
    ) {
        logger.d("HomeEffectHandler", "加载最近训练会话")

        scope.launch {
            try {
                // 加载最近训练会话 - 暂时返回空列表，后续集成GetWorkoutSessionsUseCase
                @Suppress("UNCHECKED_CAST")
                val result =
                    ModernResult.Success(
                        emptyList<com.example.gymbro.domain.workout.model.session.WorkoutSession>().toImmutableList(),
                    )
                sendIntent(HomeContract.Intent.OnRecentSessionsLoaded(result))
            } catch (e: Exception) {
                logger.e(e, "最近训练会话加载异常")
                sendIntent(
                    HomeContract.Intent.OnRecentSessionsLoaded(
                        ModernResult.Error(
                            DataErrors.DataError.access(
                                operationName = "HomeEffectHandler.loadRecentSessions",
                                message = UiText.DynamicString("最近训练会话加载异常"),
                                entityType = "RecentSessions",
                                cause = e,
                            ),
                        ),
                    ),
                )
            }
        }
    }

    private fun handleStartQuickWorkout(
        scope: CoroutineScope,
        sendIntent: (HomeContract.Intent) -> Unit,
    ) {
        logger.d("HomeEffectHandler", "开始快速训练 - 导航到Session Selection Flow")

        scope.launch {
            try {
                // 导航到NEW_SESSION路由，触发Session Selection Flow
                // 使用空字符串作为sessionId，这会触发SessionScreen显示训练源选择器
                val result = ModernResult.Success("") // 空sessionId触发选择器
                sendIntent(HomeContract.Intent.OnWorkoutStarted(result))
            } catch (e: Exception) {
                logger.e(e, "快速训练启动系统异常")
                sendIntent(
                    HomeContract.Intent.OnWorkoutStarted(
                        ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "HomeEffectHandler.handleStartQuickWorkout",
                                message = UiText.DynamicString("快速训练启动系统异常"),
                                entityType = "WorkoutSession",
                                cause = e,
                            ),
                        ),
                    ),
                )
            }
        }
    }

    private fun handleStartWithTemplate(
        templateId: String,
        scope: CoroutineScope,
        sendIntent: (HomeContract.Intent) -> Unit,
    ) {
        logger.d("HomeEffectHandler", "使用模板开始训练: $templateId")

        scope.launch {
            try {
                // 暂时返回模拟结果，后续集成正确的UseCase
                val result = ModernResult.Success("mock-session-from-template-$templateId")
                sendIntent(HomeContract.Intent.OnWorkoutStarted(result))
            } catch (e: Exception) {
                logger.e(e, "模板训练启动系统异常")
                sendIntent(
                    HomeContract.Intent.OnWorkoutStarted(
                        ModernResult.Error(
                            DataErrors.DataError.create(
                                operationName = "HomeEffectHandler.handleStartWithTemplate",
                                message = UiText.DynamicString("模板训练启动系统异常"),
                                entityType = "WorkoutSession",
                                cause = e,
                            ),
                        ),
                    ),
                )
            }
        }
    }
}
