package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.features.thinkingbox.domain.interfaces.BackgroundContentType
import com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundIntent
import com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingBoxBackgroundState
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import timber.log.Timber
import java.util.ArrayDeque
import java.util.LinkedHashMap

/**
 * ThinkingReducer - 双时序架构Reducer
 *
 * 处理ThinkingEvent事件，维护数据时序和渲染时序分离
 * 🔥 【文档规范修复】状态结构完全符合thinkingbox双时序baseline.md要求
 */
object ThinkingReducer {

    /**
     * ThinkingUiState - 符合双时序baseline文档规范的状态结构
     *
     * 🔥 【关键修复】按照文档要求使用：
     * - phases: LinkedHashMap<String, Phase> 确保阶段顺序
     * - pending: ArrayDeque<String> 优化队列性能
     */
    data class ThinkingUiState(
        // 🔥 【文档规范修复】核心状态字段 - 完全符合baseline文档
        val phases: LinkedHashMap<String, PhaseUi> = linkedMapOf(),
        val activePhaseId: String? = null, // 渲染中的阶段
        val pending: ArrayDeque<String> = ArrayDeque(), // 等待队列
        val preThinking: String? = null, // 预思考文本
        val finalMarkdown: String? = null, // 完整富文本
        val isStreaming: Boolean = true,

        // 🔥 【流式修复】最终内容缓冲区
        val finalBuffer: StringBuilder = StringBuilder(),

        // 🔥 【节流修复】最终内容更新节流
        val lastFinalUpdateTime: Long = 0L,

        // 🔥 【P0修复】Title 缓冲机制
        val deferredTitles: MutableMap<String, String> = mutableMapOf(),

        // 元数据字段
        val sessionId: String? = null,
        val startTime: Long = 0L,
        val version: Long = 0L, // 用于触发Compose重组

        // 🔥 【时序协调修复】最终富文本渲染就绪状态
        val finalRichTextReady: Boolean = false,

        // 🔥 【Final时序修复】Final内容到达标志
        val finalContentArrived: Boolean = false,

        // 🔥 【717修复方案】TypewriterRenderer核心字段
        val finalTokens: List<String> = emptyList(), // Final token流式列表
        val isFinalStreaming: Boolean = false, // Final流式状态

        // 🔥 【中优先级修复2】思考完成状态管理
        val isThinkingComplete: Boolean = false, // 是否完成所有思考阶段
        val thinkingDuration: Long = 0L, // 思考持续时间（毫秒）
        val totalTokens: Int = 0, // 总token数量

        // 🔥 【对话完成状态】最终富文本动画完成，整个对话结束
        val isConversationComplete: Boolean = false, // 是否完成整个对话（包括最终富文本动画）
        val showCopyButton: Boolean = false, // 是否显示复制按钮
        val shouldSaveHistory: Boolean = false, // 是否应该保存到历史记录

        // 🔥 【问题1修复】Perthink完成状态，防止异常回退
        val perthinkCompleted: Boolean = false,

        // 🔥 【MVI 2.0】背景状态管理
        val backgroundState:
        ThinkingBoxBackgroundState = ThinkingBoxBackgroundState(),

        // 🔥 【P2修复】双时序握手状态
        val phaseDataCompleted: MutableSet<String> = mutableSetOf(), // 数据完成的Phase ID
        val phaseAnimCompleted: MutableSet<String> = mutableSetOf(), // 动画完成的Phase ID
        val thinkingClosed: Boolean = false, // 思考框是否已关闭
        val finalDataCompleted: Boolean = false, // Final数据是否完成
        val finalAnimCompleted: Boolean = false, // Final动画是否完成
    ) {
        // 便利属性
        val hasFinal: Boolean get() = finalMarkdown != null

        // 🔥 【唯一真源性修复】移除重复的shouldShowCollapsed，统一使用UiState中的实现
        // 🔥 【唯一真源性修复】移除重复的getFormattedDuration，统一使用UiState中的实现
        // 🔥 【唯一真源性修复】移除重复的formattedThinkingDuration，统一使用UiState中的实现

        // 计算总token数量（基于所有phases内容和finalMarkdown）
        fun calculateTotalTokens(
            tokenizerService: TokenizerService? = null,
        ): Int {
            val phaseContent = phases.values.joinToString(" ") { "${it.title ?: ""} ${it.content}" }
            val finalContent = finalMarkdown ?: ""
            val preThinkContent = preThinking ?: ""
            val allContent = "$phaseContent $finalContent $preThinkContent"

            return if (tokenizerService != null && tokenizerService.isAvailable()) {
                tokenizerService.countTokens(allContent)
            } else {
                // 简单的token估算：平均4个字符为1个token
                (allContent.length + 3) / 4
            }
        }

        val isCompleted: Boolean get() = !isStreaming && hasFinal
        val activePhase: PhaseUi? get() = activePhaseId?.let { phases[it] }
        val hasPendingPhases: Boolean get() = pending.isNotEmpty()
    }

    /**
     * Phase UI数据结构
     */
    data class PhaseUi(
        val id: String,
        val title: String? = null,
        val content: String = "",
        val isComplete: Boolean = false,
    ) {
        val hasTitle: Boolean get() = title != null && title.isNotEmpty()
    }

    /**
     * 主Reducer函数 - 处理ThinkingEvent事件
     *
     * 🔥 架构原则：
     * - 纯函数：给定相同输入，总是产生相同输出
     * - 双时序协调：数据时序与UI动画时序分离处理
     * - 状态完整性：确保状态转换的一致性和可预测性
     *
     * @param state 当前状态
     * @param event 事件
     * @return 新状态
     */
    fun reduce(state: ThinkingUiState, event: ThinkingEvent): ThinkingUiState {
        Timber.Forest.tag("TB-REDUCER").v("🔧 [状态转换] 处理事件: ${event::class.simpleName}")

        return when (event) {
            is ThinkingEvent.PreThinkChunk -> {
                Timber.Forest.tag("TB-REDUCER").d("📝 [PreThinkChunk] 内容长度: ${event.content.length}")

                val newPreThinking = (state.preThinking ?: "") + event.content
                state.copy(
                    preThinking = newPreThinking,
                    isStreaming = true,
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.PreThinkEnd -> {
                Timber.Forest.tag("TB-REDUCER").d("🔚 [PreThinkEnd] 预思考结束")

                // 检查是否存在 perthink phase
                val existingPerthinkPhase = state.phases["perthink"]
                if (existingPerthinkPhase != null) {
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put("perthink", existingPerthinkPhase.copy(isComplete = true))
                    }

                    // 双时序协调：检查是否有等待的正式 phase 需要激活
                    val nextPhaseId = state.pending.firstOrNull()
                    val shouldSwitchToNextPhase = nextPhaseId != null && state.activePhaseId == "perthink"

                    if (shouldSwitchToNextPhase) {
                        val newPending = ArrayDeque(state.pending).apply { removeFirst() }
                        Timber.Forest.tag("TB-REDUCER").i("🔄 [双时序] perthink 完成，切换到正式 phase: $nextPhaseId")

                        state.copy(
                            preThinking = null,
                            phases = updatedPhases,
                            activePhaseId = nextPhaseId,
                            pending = newPending,
                            perthinkCompleted = true,
                            version = state.version + 1,
                        )
                    } else {
                        state.copy(
                            preThinking = null,
                            phases = updatedPhases,
                            perthinkCompleted = true,
                            version = state.version + 1,
                        )
                    }
                } else {
                    // 创建空的 perthink phase 并标记完成
                    val perthinkPhase = PhaseUi(
                        id = "perthink",
                        title = ThinkingBoxStrings.PERTHINK_TITLE,
                        content = state.preThinking ?: "",
                        isComplete = true,
                    )
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put("perthink", perthinkPhase)
                    }

                    state.copy(
                        preThinking = null,
                        phases = updatedPhases,
                        perthinkCompleted = true,
                        version = state.version + 1,
                    )
                }
            }

            is ThinkingEvent.PhaseStart -> {
                Timber.Forest.tag("TB-REDUCER").d("🎯 [PhaseStart] ID: ${event.id}, Title: ${event.title}")

                // 检查是否有缓存的 title
                val deferredTitle = state.deferredTitles.remove(event.id)
                val finalTitle = deferredTitle ?: event.title

                val newPhase = PhaseUi(id = event.id, title = finalTitle)
                val updatedPhases = LinkedHashMap(state.phases).apply {
                    put(event.id, newPhase)
                }

                // 记录思考开始时间（只在第一个非 perthink phase 时记录）
                val shouldRecordStartTime = state.startTime == 0L && event.id != "perthink"
                val newStartTime = if (shouldRecordStartTime) System.currentTimeMillis() else state.startTime

                if (event.id == "perthink") {
                    // perthink 特殊处理
                    if (state.activePhaseId == null) {
                        Timber.Forest.tag("TB-REDUCER").d("🚨 [perthink] 直接激活")
                        state.copy(
                            phases = updatedPhases,
                            activePhaseId = "perthink",
                            isStreaming = true,
                            startTime = newStartTime,
                            version = state.version + 1,
                        )
                    } else {
                        Timber.Forest.tag("TB-REDUCER").d("🚨 [perthink] 进入队列")
                        val newPending = ArrayDeque(state.pending).apply {
                            if ("perthink" !in this && "perthink" != state.activePhaseId) {
                                addLast("perthink")
                            }
                        }
                        state.copy(
                            phases = updatedPhases,
                            pending = newPending,
                            startTime = newStartTime,
                            version = state.version + 1,
                        )
                    }
                } else if (state.activePhaseId == null) {
                    // 没有活跃 phase 时，立即激活
                    Timber.Forest.tag("TB-REDUCER").d("🎯 [Phase激活] 立即激活: ${event.id}")
                    state.copy(
                        phases = updatedPhases,
                        activePhaseId = event.id,
                        isStreaming = true,
                        startTime = newStartTime,
                        version = state.version + 1,
                    )
                } else {
                    // 有活跃 phase 时，加入 pending 队列
                    val newPending = ArrayDeque(state.pending).apply {
                        if (event.id !in this && event.id != state.activePhaseId) {
                            addLast(event.id)
                        }
                    }
                    Timber.Forest.tag("TB-REDUCER").d("📋 [Phase队列] ${event.id} 加入队列，当前活跃: ${state.activePhaseId}")

                    state.copy(
                        phases = updatedPhases,
                        pending = newPending,
                        startTime = newStartTime,
                        version = state.version + 1,
                    )
                }
            }

            is ThinkingEvent.PhaseTitleUpdate -> {
                val existingPhase = state.phases[event.id]
                Timber.Forest.tag("TB-TITLE").i("🏷️ [标题更新] Phase: ${event.id}, Title: '${event.title}'")

                if (existingPhase != null) {
                    val updatedPhase = existingPhase.copy(title = event.title)
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put(event.id, updatedPhase)
                    }

                    state.copy(
                        phases = updatedPhases,
                        version = state.version + 1,
                    )
                } else {
                    // Phase 不存在时缓存 title
                    Timber.Forest.tag("TB-TITLE").w("⏳ [标题缓存] Phase 未就绪，缓存 title: ${event.id} -> '${event.title}'")
                    state.deferredTitles[event.id] = event.title
                    state // 不提升 version
                }
            }

            is ThinkingEvent.PhaseContent -> {
                val existingPhase = state.phases[event.id]

                if (existingPhase != null) {
                    // 双时序保护：如果 phase 已完成，不再接受新内容
                    if (existingPhase.isComplete) {
                        Timber.Forest.tag("TB-REDUCER").d("🔒 [内容保护] Phase ${event.id} 已完成，忽略新内容")
                        return state
                    }

                    val updatedPhase = existingPhase.copy(
                        content = existingPhase.content + event.content,
                    )
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put(event.id, updatedPhase)
                    }

                    Timber.Forest.tag("TB-REDUCER").v("📝 [内容追加] Phase ${event.id}: +${event.content.length} 字符")

                    state.copy(
                        phases = updatedPhases,
                        // 优化：PhaseContent 不频繁更新 version，避免过度重组
                    )
                } else {
                    Timber.Forest.tag("TB-REDUCER").w("❌ [内容丢失] Phase ${event.id} 不存在")
                    state
                }
            }

            is ThinkingEvent.PhaseComplete -> {
                val phase = state.phases[event.id]
                Timber.Forest.tag("TB-REDUCER").d("🔚 [PhaseComplete] Phase: ${event.id}")

                if (phase != null) {
                    val updatedPhases = LinkedHashMap(state.phases).apply {
                        put(event.id, phase.copy(isComplete = true))
                    }

                    Timber.Forest.tag("TB-REDUCER").d("🔥 [数据时序] Phase ${event.id} 标记完成，等待动画时序")

                    state.copy(
                        phases = updatedPhases,
                        version = state.version + 1,
                    )
                } else {
                    Timber.Forest.tag("TB-REDUCER").w("⚠️ [PhaseComplete] Phase ${event.id} 不存在，忽略")
                    state
                }
            }

            is ThinkingEvent.PhaseAnimFinished -> {
                // 🔥 双时序握手机制核心实现
                val currentPhase = state.phases[event.id]
                Timber.Forest.tag("TB-REDUCER").d("🎬 [动画时序] Phase ${event.id} 动画完成")

                // 验证双时序条件：必须是当前活跃 phase 且数据已完成
                if (event.id != state.activePhaseId || currentPhase?.isComplete != true) {
                    Timber.Forest.tag("TB-REDUCER").w(
                        "🔄 [双时序验证失败] activePhaseId=${state.activePhaseId}, isComplete=${currentPhase?.isComplete}",
                    )
                    return state
                }

                // 双时序条件满足，切换到下一个 phase
                val nextPhaseId = state.pending.peekFirst()
                val newPending = ArrayDeque(state.pending).apply {
                    if (isNotEmpty()) pollFirst()
                }

                Timber.Forest.tag("TB-REDUCER").i("🔄 [Phase切换] ${event.id} → $nextPhaseId")

                val isThinkingComplete = nextPhaseId == null

                state.copy(
                    activePhaseId = nextPhaseId,
                    pending = newPending,
                    isThinkingComplete = isThinkingComplete,
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.ShowSummaryPanel -> {
                Timber.Forest.tag("TB-REDUCER").i("🎯 [UI交互] 显示摘要面板")
                state.copy(version = state.version + 1)
            }

            is ThinkingEvent.SummaryAnimationComplete -> {
                Timber.Forest.tag("TB-REDUCER").i("🎯 [动画完成] 摘要动画完成")
                state.copy(version = state.version + 1)
            }

            is ThinkingEvent.SummaryCardCollapsed -> {
                Timber.Forest.tag("TB-REDUCER").w("⚠️ [废弃事件] SummaryCardCollapsed 已废弃")
                state
            }

            is ThinkingEvent.ThinkingEnd -> {
                Timber.Forest.tag("TB-REDUCER").i("🔚 [ThinkingEnd] 思考结束")

                // 检查是否还有未完成的 phase
                val hasIncompletePhases = state.phases.values.any { !it.isComplete }
                val hasActivePhasePending = state.activePhaseId != null

                if (hasIncompletePhases || hasActivePhasePending) {
                    Timber.Forest.tag("TB-REDUCER").i("⏳ [ThinkingEnd] 等待 phase 完成")
                    state.copy(
                        isStreaming = false,
                        version = state.version + 1,
                    )
                } else {
                    Timber.Forest.tag("TB-REDUCER").i("✅ [ThinkingEnd] 立即完成")
                    state.copy(
                        isStreaming = false,
                        isThinkingComplete = true,
                        activePhaseId = null,
                        version = state.version + 1,
                    )
                }
            }

            is ThinkingEvent.FinalStart -> {
                Timber.Forest.tag("TB-REDUCER").d("🔥 [FinalStart] 启动后台渲染")
                state.copy(
                    isFinalStreaming = true,
                    finalContentArrived = true,
                    finalTokens = emptyList(),
                    finalBuffer = StringBuilder(),
                    finalMarkdown = "",
                    isStreaming = false,
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalContent -> {
                Timber.Forest.tag("TB-REDUCER").v("📄 [FinalContent] 累积长度: ${event.text.length}")

                val updatedTokens = state.finalTokens + event.text
                val updatedBuffer = StringBuilder(state.finalBuffer).apply {
                    append(event.text)
                }
                val currentFinalMarkdown = updatedBuffer.toString()

                state.copy(
                    finalTokens = updatedTokens,
                    finalBuffer = updatedBuffer,
                    finalMarkdown = currentFinalMarkdown,
                    isFinalStreaming = true,
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalComplete -> {
                Timber.Forest.tag("TB-REDUCER").d("🔥 [FinalComplete] Final 数据处理完成，激活渲染器")
                state.copy(
                    isFinalStreaming = false,
                    isStreaming = false,
                    finalRichTextReady = true,
                    isThinkingComplete = true,
                    activePhaseId = null,
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalRenderingReady -> {
                Timber.Forest.tag("TB-REDUCER").w("🔥 [防重复] FinalRenderingReady 已被忽略")
                state
            }

            is ThinkingEvent.FinalAnimationComplete -> {
                Timber.Forest.tag("TB-REDUCER").i("🎯 [对话完成] Final 动画完成")
                state.copy(
                    isConversationComplete = true,
                    showCopyButton = true,
                    shouldSaveHistory = true,
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalArrived -> {
                // 向后兼容处理
                val currentTime = System.currentTimeMillis()
                val thinkingDuration = if (state.startTime > 0) currentTime - state.startTime else 0L
                val totalTokens = state.calculateTotalTokens()

                Timber.Forest.tag("TB-REDUCER").d("📄 [向后兼容] FinalArrived: 长度=${event.markdown.length}")

                val finalContent = if (state.finalBuffer.isNotEmpty()) {
                    state.finalBuffer.toString()
                } else {
                    event.markdown
                }

                // 清空缓存的 titles
                state.deferredTitles.clear()

                state.copy(
                    finalMarkdown = finalContent,
                    thinkingDuration = thinkingDuration,
                    totalTokens = totalTokens,
                    version = state.version + 1,
                )
            }

            // 🔥 【P2修复】新增事件处理
            is ThinkingEvent.ThinkingBoxClosed -> {
                Timber.Forest.tag("TB-REDUCER").d("🎯 [思考框关闭] 处理思考框关闭事件")
                state.copy(
                    thinkingClosed = true,
                    version = state.version + 1,
                )
            }

            is ThinkingEvent.FinalRenderingComplete -> {
                Timber.Forest.tag("TB-REDUCER").d("🎯 [Final渲染完成] 处理Final渲染完成事件")
                state.copy(
                    finalAnimCompleted = true,
                    version = state.version + 1,
                )
            }
        }
    }

    /**
     * 批量处理事件 - 优化性能
     */
    fun reduceBatch(state: ThinkingUiState, events: List<ThinkingEvent>): ThinkingUiState {
        return events.fold(state) { currentState, event ->
            reduce(currentState, event)
        }
    }

    /**
     * 创建初始状态
     * 🔥 【文档规范修复】使用符合baseline文档的数据结构
     */
    fun createInitialState(sessionId: String): ThinkingUiState {
        return ThinkingUiState(
            phases = linkedMapOf(),
            pending = ArrayDeque(),
            sessionId = sessionId,
            startTime = System.currentTimeMillis(),
        )
    }

    /**
     * 重置状态 - 用于新会话
     * 🔥 【文档规范修复】使用符合baseline文档的数据结构
     */
    fun resetState(state: ThinkingUiState, newSessionId: String): ThinkingUiState {
        return ThinkingUiState(
            phases = linkedMapOf(),
            pending = ArrayDeque(),
            sessionId = newSessionId,
            startTime = System.currentTimeMillis(),
        )
    }

    /**
     * 🔥 【MVI 2.0】处理背景Intent
     *
     * @param state 当前状态
     * @param intent 背景Intent
     * @return 更新后的状态
     */
    fun reduceBackgroundIntent(
        state: ThinkingUiState,
        intent: ThinkingBoxBackgroundIntent,
    ): ThinkingUiState {
        Timber.Forest.tag("TB-BG-REDUCER").d("Processing background intent: ${intent::class.simpleName}")

        return when (intent) {
            is ThinkingBoxBackgroundIntent.ToggleBlur -> {
                val newBackgroundState = state.backgroundState.copy(
                    isBlurred = !state.backgroundState.isBlurred,
                )
                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }

            is ThinkingBoxBackgroundIntent.AdjustBlurRadius -> {
                val validRadius = intent.radius.coerceIn(0f, 50f) // 限制在合理范围内
                val newBackgroundState = state.backgroundState.copy(
                    blurRadius = validRadius,
                    isBlurred = validRadius > 0f, // 自动开启模糊如果半径 > 0
                )
                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }

            is ThinkingBoxBackgroundIntent.ChangeBackgroundTint -> {
                val newBackgroundState = state.backgroundState.copy(
                    backgroundTint = intent.tint,
                )
                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }

            is ThinkingBoxBackgroundIntent.ResetBackground -> {
                val newBackgroundState = ThinkingBoxBackgroundState()
                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }

            is ThinkingBoxBackgroundIntent.AutoAdjustBackground -> {
                // 🔥 【智能背景调整】根据主题和内容类型自动选择最佳背景效果
                val (blurRadius, tint) = when (intent.contentType) {
                    BackgroundContentType.Default -> {
                        if (intent.isDarkTheme) 8f to 0x40FFFFFF else 6f to 0x20000000
                    }
                    BackgroundContentType.Thinking -> {
                        if (intent.isDarkTheme) 12f to 0x60FFFFFF else 10f to 0x30000000
                    }
                    BackgroundContentType.Final -> {
                        if (intent.isDarkTheme) 15f to 0x80FFFFFF else 12f to 0x40000000
                    }
                    BackgroundContentType.Completed -> {
                        if (intent.isDarkTheme) 5f to 0x30FFFFFF else 4f to 0x15000000
                    }
                }

                val newBackgroundState = state.backgroundState.copy(
                    isBlurred = true,
                    blurRadius = blurRadius,
                    backgroundTint = tint.toInt(),
                )

                Timber.Forest.tag("TB-BG-REDUCER").i(
                    "Auto adjusted background: isDark=${intent.isDarkTheme}, " +
                        "type=${intent.contentType}, blur=$blurRadius, tint=$tint",
                )

                state.copy(
                    backgroundState = newBackgroundState,
                    version = state.version + 1,
                )
            }
        }
    }

    /**
     * 状态验证 - 调试用
     */
    fun validateState(state: ThinkingUiState): List<String> {
        val issues = mutableListOf<String>()

        // 检查activePhaseId是否存在于phases中
        if (state.activePhaseId != null && state.activePhaseId !in state.phases) {
            issues.add("Active phase ID not found in phases: ${state.activePhaseId}")
        }

        // 检查pending队列中的phase是否都存在
        state.pending.forEach { phaseId ->
            if (phaseId !in state.phases) {
                issues.add("Pending phase ID not found in phases: $phaseId")
            }
        }

        // 检查是否有重复的phase ID
        if (state.activePhaseId != null && state.activePhaseId in state.pending) {
            issues.add("Active phase ID also in pending queue: ${state.activePhaseId}")
        }

        return issues
    }
}
